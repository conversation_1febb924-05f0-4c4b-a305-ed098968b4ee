{"permissions": {"allow": ["Bash(grep:*)", "<PERSON><PERSON>(python:*)", "WebFetch(domain:akshare.akfamily.xyz)", "Bash(pip --version)", "Ba<PERSON>(pip3:*)", "Bash(uv sync:*)", "Bash(cp:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/deploy_db.sh:*)", "Bash(npm install)", "Bash(npm run dev:*)", "Bash(uv add:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "Bash(find:*)", "<PERSON><PERSON>(sed:*)", "Bash(node:*)", "Bash(awk:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)"], "deny": [], "defaultMode": "acceptEdits"}, "outputStyle": "Explanatory"}