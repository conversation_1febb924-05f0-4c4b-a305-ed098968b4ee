#!/bin/bash

# Ultrathink 股票量化分析系统 - 前端原型启动脚本
# 智能选择最佳启动方式

echo "🚀 启动 Ultrathink 股票量化分析系统原型..."
echo ""

# 检查是否存在构建文件
if [ -d "dist" ]; then
    echo "📦 发现构建文件，使用静态服务器启动..."
    
    # 优先使用Python
    if command -v python3 &> /dev/null; then
        echo "🐍 使用 Python3 启动服务器..."
        python3 启动原型.py
    elif command -v python &> /dev/null; then
        echo "🐍 使用 Python 启动服务器..."
        python 启动原型.py
    # 备选Node.js
    elif command -v node &> /dev/null; then
        echo "⚡️ 使用 Node.js 启动服务器..."
        node 启动原型.js
    else
        echo "❌ 错误: 需要安装 Python3 或 Node.js 来启动服务器"
        echo ""
        echo "安装方法:"
        echo "  macOS: brew install python3 或 brew install node"
        echo "  Ubuntu: sudo apt install python3 或 sudo apt install nodejs"
        echo ""
        echo "或者使用开发模式: npm run dev"
        exit 1
    fi
else
    echo "📦 未找到构建文件，使用开发模式..."
    
    # 检查 Node.js 环境
    if ! command -v node &> /dev/null; then
        echo "❌ 错误: 未检测到 Node.js，请先安装 Node.js (建议版本 >= 16)"
        exit 1
    fi

    # 检查 npm 环境
    if ! command -v npm &> /dev/null; then
        echo "❌ 错误: 未检测到 npm，请确保 Node.js 安装完整"
        exit 1
    fi

    echo "✅ Node.js 版本: $(node --version)"
    echo "✅ npm 版本: $(npm --version)"
    echo ""

    # 检查依赖是否已安装
    if [ ! -d "node_modules" ]; then
        echo "📦 正在安装项目依赖..."
        npm install
        
        if [ $? -ne 0 ]; then
            echo "❌ 依赖安装失败，请检查网络连接或使用国内镜像"
            echo "   可尝试: npm config set registry https://registry.npmmirror.com/"
            exit 1
        fi
        
        echo "✅ 依赖安装完成"
        echo ""
    fi

    echo "🎯 演示账户信息:"
    echo "   管理员: admin / admin123"
    echo "   普通用户: user / user123"
    echo "   演示用户: demo / demo123"
    echo ""

    echo "🌐 启动开发服务器..."
    echo "   本地访问: http://localhost:5173"
    echo ""
    echo "💡 使用 Ctrl+C 停止服务器"
    echo ""

    # 启动开发服务器
    npm run prototype
fi