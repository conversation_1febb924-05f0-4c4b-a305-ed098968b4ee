import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import WindiCSS from 'vite-plugin-windicss'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'

// https://vite.dev/config/
export default defineConfig({
  // 使用相对路径，支持直接打开HTML文件
  base: './',
  plugins: [
    vue(),
    // WindiCSS
    WindiCSS(),
    // 自动导入 Vue、Vue Router、Pinia、VueUse 的 API
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core'
      ],
      resolvers: [ElementPlusResolver()],
      dts: true, // 生成类型声明文件
    }),
    // 自动导入组件
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true, // 生成类型声明文件
    }),
  ],
  // 路径别名
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  // SCSS 全局变量
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/assets/scss/_variables.scss"; @import "@/assets/scss/_mixins.scss";`,
      },
    },
  },
  // 开发服务器配置 - 纯前端原型版本
  server: {
    port: 5173,
    host: '0.0.0.0'
    // 移除后端API代理，使用本地Mock数据
  },
  // Preview 服务器配置
  preview: {
    port: 5173,
    host: '0.0.0.0'
    // 移除后端API代理，使用本地Mock数据
  },
  // 构建配置 - 优化为可直接打开的静态文件
  build: {
    // 生成相对路径的资源引用
    assetsDir: 'assets',
    // 禁用代码分割，生成单一JS文件（可选）
    rollupOptions: {
      output: {
        // 手动控制chunk分割，避免过多文件
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus', '@iconify/vue'],
          charts: ['echarts']
        }
      }
    }
  }
})
