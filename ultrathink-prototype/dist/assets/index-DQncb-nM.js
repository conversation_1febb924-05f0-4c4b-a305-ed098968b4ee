import{_ as z,u as B,a as d}from"./index-BSF2zNx2.js";/* empty css                     *//* empty css                    *//* empty css                 */import{W as R,r as w,j as q,aA as K,az as M,x as p,B as s,O as o,F as r,ac as k,K as g,M as x,S,y as _}from"./vendor-Dq0JXR-b.js";import{g as A,h as C,i as I,c as N,j as U,E as f}from"./ui-CHSsU9Lc.js";const j={class:"login-container"},P={class:"login-content"},T={class:"login-header"},W={class:"logo-section"},J={class:"logo-icon"},L={class:"login-card"},O={class:"input-wrapper"},$={class:"input-wrapper"},D={__name:"index",setup(G){const v=M(),b=K(),y=B(),t=R({username:"",password:"",remember_me:!0}),V={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码至少6位",trigger:"blur"}]},c=w(null),n=w(!1),i=async()=>{if(c.value)try{if(!await c.value.validate())return;n.value=!0;const e=await y.login(t);if(e.success){f.success("登录成功！");const u=b.query.redirect||"/";v.push(u)}else f.error(e.message||"登录失败")}catch(l){console.error("登录失败:",l),f.error(l.message||"登录失败，请检查用户名和密码")}finally{n.value=!1}};return q(()=>{if(y.isAuthenticated){const l=b.query.redirect||"/";v.push(l)}}),(l,e)=>{const u=C,m=A,h=I,E=N,F=U;return _(),p("div",j,[e[9]||(e[9]=s("div",{class:"background-gradient"},null,-1)),e[10]||(e[10]=s("div",{class:"background-pattern"},null,-1)),s("div",P,[s("div",T,[s("div",W,[s("div",J,[o(d,{name:"chart-line",size:48})]),e[3]||(e[3]=s("h1",{class:"system-title"},"股票量化分析系统",-1)),e[4]||(e[4]=s("p",{class:"system-subtitle"},"用户认证登录",-1))])]),s("div",L,[o(F,{ref_key:"loginFormRef",ref:c,model:t,rules:V,class:"login-form",onSubmit:S(i,["prevent"])},{default:r(()=>[e[7]||(e[7]=s("div",{class:"form-title"},[s("h2",null,"登录账户"),s("p",null,"请输入您的用户名和密码")],-1)),o(m,{prop:"username",class:"form-item"},{default:r(()=>[s("div",O,[o(d,{name:"user",class:"input-icon"}),o(u,{modelValue:t.username,"onUpdate:modelValue":e[0]||(e[0]=a=>t.username=a),placeholder:"请输入用户名",size:"large",class:"custom-input",onKeyup:k(i,["enter"])},null,8,["modelValue"])])]),_:1}),o(m,{prop:"password",class:"form-item"},{default:r(()=>[s("div",$,[o(d,{name:"locked",class:"input-icon"}),o(u,{modelValue:t.password,"onUpdate:modelValue":e[1]||(e[1]=a=>t.password=a),type:"password",placeholder:"请输入密码",size:"large",class:"custom-input","show-password":"",onKeyup:k(i,["enter"])},null,8,["modelValue"])])]),_:1}),o(m,{class:"remember-item"},{default:r(()=>[o(h,{modelValue:t.remember_me,"onUpdate:modelValue":e[2]||(e[2]=a=>t.remember_me=a),class:"remember-checkbox"},{default:r(()=>e[5]||(e[5]=[g(" 记住登录状态 (30天) ",-1)])),_:1,__:[5]},8,["modelValue"])]),_:1}),o(m,{class:"login-button-item"},{default:r(()=>[o(E,{type:"primary",size:"large",class:"login-button",loading:n.value,onClick:i},{default:r(()=>[n.value?(_(),p(x,{key:1},[g(" 登录中... ")],64)):(_(),p(x,{key:0},[o(d,{name:"arrow-right",class:"button-icon"}),e[6]||(e[6]=g(" 登录系统 ",-1))],64))]),_:1},8,["loading"])]),_:1})]),_:1,__:[7]},8,["model"])]),e[8]||(e[8]=s("div",{class:"login-footer"},[s("p",null,"© 2024 股票量化分析系统 - JWT认证版本"),s("p",{class:"contact-info"},"还没有账号？请联系管理员创建")],-1))])])}}},se=z(D,[["__scopeId","data-v-b832fbf5"]]);export{se as default};
