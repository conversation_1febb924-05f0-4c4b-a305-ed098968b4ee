import{u as ie,_ as re,a as te,h as me}from"./index-BSF2zNx2.js";import{C as ue}from"./CommonTable-DDC4hMKa.js";/* empty css                     */import{s as F}from"./el-select-BLKpJAwO.js";import{r as V,W as oe,c as L,w as ae,E as $,y as i,F as e,x as f,I as T,B as m,O as s,K as h,L as d,u as p,M as U,ab as O,j as pe,Q as ge,S as fe}from"./vendor-Dq0JXR-b.js";import{E as A,F as _e,G as ye,p as z,H as xe,c as ce,l as he,f as ne,g as ve,x as ke,w as be,j as we,t as Te}from"./ui-CHSsU9Lc.js";/* empty css                   *//* empty css                    */import"./CommonPagination-IzNmwDoz.js";function $e(){const c=ie(),B=V(!1),_=V([]),v=oe({current:1,pageSize:20,total:0}),b=oe({triggerType:null,status:null,userId:null}),w=L(()=>c.isAdmin),y=async()=>{B.value=!0;try{const l={skip:(v.current-1)*v.pageSize,limit:v.pageSize,...Object.fromEntries(Object.entries(b).filter(([x,S])=>S!=null))},t=w.value?await F.getAllExecutionsAdmin(l):await F.getAllExecutions(l);console.log(t),t&&t.data?(_.value=Array.isArray(t.data.items)?t.data.items:[],v.total=t.data.total||0):(console.warn("API响应格式异常:",t),_.value=[],v.total=0)}catch(l){console.error("加载任务执行记录失败:",l);let t="加载任务执行记录失败";l.response?l.response.status===401?t="未授权，请重新登录":l.response.status===403?t="权限不足":l.response.status>=500?t="服务器内部错误，请稍后重试":l.response.data&&l.response.data.detail&&(t=l.response.data.detail):l.request?t="网络连接失败，请检查网络状态":l.message&&(t=l.message),A.error(t),_.value=[],v.total=0}finally{B.value=!1}};return{loading:B,taskExecutions:_,pagination:v,filters:b,isAdmin:w,loadTaskExecutions:y,cancelExecution:async l=>{try{await F.cancelExecution(l),A.success("任务取消成功"),await y()}catch(t){console.error("取消任务执行失败:",t);let x="任务取消失败";throw t.response&&t.response.data&&t.response.data.detail?x=t.response.data.detail:t.message&&(x=t.message),A.error(x),t}},deleteExecution:async l=>{try{await F.deleteExecution(l),A.success("执行记录删除成功"),await y()}catch(t){console.error("删除执行记录失败:",t);let x="执行记录删除失败";throw t.response&&t.response.data&&t.response.data.detail?x=t.response.data.detail:t.message&&(x=t.message),A.error(x),t}},refreshData:async()=>{await y()},resetFilters:()=>{b.triggerType=null,b.status=null,b.userId=null,v.current=1,y()},getStatusTagType:l=>({pending:"info",running:"warning",completed:"success",failed:"danger",cancelled:"info"})[l]||"info",getStatusText:l=>({pending:"待执行",running:"执行中",completed:"已完成",failed:"已失败",cancelled:"已取消"})[l]||l,formatDuration:l=>{if(!l)return"-";if(l<60)return`${l}秒`;const t=Math.floor(l/60),x=l%60;if(t<60)return`${t}分${x}秒`;const S=Math.floor(t/60),P=t%60;return`${S}时${P}分${x}秒`},formatDateTime:l=>l?new Date(l).toLocaleString("zh-CN"):"-"}}const Ee={key:0,class:"execution-details"},Me={class:"detail-section"},Se={key:0},Ce={key:1,class:"text-gray-500"},ze={class:"font-medium"},De={class:"text-xs text-gray-500"},Ae={class:"config-display"},Ve={class:"config-item"},Be={key:0,class:"config-item"},Ie={class:"stock-codes"},Pe={key:0},je={class:"config-item"},Fe={class:"config-item"},Ue={key:0,class:"detail-section"},Oe={key:1,class:"detail-section"},Le={class:"dialog-footer"},Ne={__name:"TaskExecutionDetails",props:{modelValue:{type:Boolean,default:!1},execution:{type:Object,default:null}},emits:["update:modelValue","cancel-execution"],setup(c,{emit:B}){const _=c,v=B,b=ie(),w=V(_.modelValue),y=V({currentPage:1,pageSize:20,total:0}),N=L(()=>b.isAdmin),q=L(()=>_.execution&&(_.execution.status==="pending"||_.execution.status==="running")),W=L(()=>{var C;if(!((C=_.execution)!=null&&C.results_data))return y.value.total=0,[];const a=Array.isArray(_.execution.results_data)?_.execution.results_data:[];y.value.total=a.length;const n=(y.value.currentPage-1)*y.value.pageSize,r=n+y.value.pageSize,E=a.slice(n,r);return console.log(`Paginating results: ${n} to ${r}, total: ${a.length}, result:`,E),E}),H=[{prop:"stock_code",label:"股票代码","min-width":100,fixed:"left"},{prop:"stock_name",label:"股票名称",minWidth:120},{prop:"price",label:"股价","min-width":80,formatter:(a,n,r,E)=>typeof r=="number"?r.toFixed(2):r},{prop:"signals",label:"信号",minWidth:100,slot:"signals"},{prop:"scan_time",label:"扫描时间","min-width":180,formatter:(a,n,r,E)=>r?r.replace("T"," ").substring(0,19):""},{prop:"period",label:"周期","min-width":80,formatter:(a,n,r,E)=>({d:"日线",w:"周线",m:"月线"})[r]||r}];ae(()=>_.modelValue,a=>{w.value=a}),ae(w,a=>{v("update:modelValue",a)}),ae(()=>_.execution,()=>{y.value.currentPage=1});const M=()=>{var a;return(a=_.execution)!=null&&a.task_config_raw?_.execution.task_config_raw:{}},R=a=>({pending:"info",running:"warning",completed:"success",failed:"danger",cancelled:"info"})[a]||"info",G=a=>({pending:"待执行",running:"执行中",completed:"已完成",failed:"已失败",cancelled:"已取消"})[a]||a,I=a=>({buy:"success",sell:"danger",hold:"info"})[a]||"info",l=a=>({d:"日线",w:"周线",m:"月线"})[a]||a,t=a=>({n:"不复权",qfq:"前复权",hfq:"后复权"})[a]||a,x=a=>a?new Date(a).toLocaleString("zh-CN"):"-",S=a=>{if(!a)return"-";if(a<60)return`${a}秒`;const n=Math.floor(a/60),r=a%60;if(n<60)return`${n}分${r}秒`;const E=Math.floor(n/60),C=n%60;return`${E}时${C}分${r}秒`},P=({page:a,size:n})=>{y.value.currentPage=a,y.value.pageSize=n},Q=async()=>{if(_.execution)try{await ne.confirm("确认取消此任务执行？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await F.cancelExecution(_.execution.id),A.success("任务取消成功"),v("cancel-execution",_.execution.id),w.value=!1}catch(a){a!=="cancel"&&(A.error("任务取消失败"),console.error(a))}};return(a,n)=>{const r=ye,E=_e,C=xe,K=ce,J=he;return i(),$(J,{modelValue:w.value,"onUpdate:modelValue":n[1]||(n[1]=k=>w.value=k),title:"任务执行详情",width:"80%","close-on-click-modal":!1,"append-to-body":"",style:{"--el-dialog-margin-top":"5vh"}},{footer:e(()=>[m("span",Le,[s(K,{onClick:n[0]||(n[0]=k=>w.value=!1)},{default:e(()=>n[9]||(n[9]=[h("关闭",-1)])),_:1,__:[9]}),q.value?(i(),$(K,{key:0,type:"danger",onClick:Q},{default:e(()=>n[10]||(n[10]=[h(" 取消执行 ",-1)])),_:1,__:[10]})):T("",!0)])]),default:e(()=>[c.execution?(i(),f("div",Ee,[m("div",Me,[n[6]||(n[6]=m("h3",null,"基本信息",-1)),s(E,{column:2,border:""},{default:e(()=>[s(r,{label:"执行ID"},{default:e(()=>[h(d(c.execution.id),1)]),_:1}),s(r,{label:"任务名称"},{default:e(()=>[c.execution.scheduled_task_name?(i(),f("span",Se,d(c.execution.scheduled_task_name),1)):(i(),f("span",Ce,"手动扫描"))]),_:1}),s(r,{label:"触发方式"},{default:e(()=>[s(p(z),{type:c.execution.trigger_type==="scheduled"?"success":"info",size:"small"},{default:e(()=>[h(d(c.execution.trigger_type==="scheduled"?"定时触发":"手动触发"),1)]),_:1},8,["type"])]),_:1}),s(r,{label:"执行状态"},{default:e(()=>[s(p(z),{type:R(c.execution.status),size:"small"},{default:e(()=>[h(d(G(c.execution.status)),1)]),_:1},8,["type"])]),_:1}),s(r,{label:"开始时间"},{default:e(()=>[h(d(x(c.execution.start_time)),1)]),_:1}),s(r,{label:"结束时间"},{default:e(()=>[h(d(x(c.execution.end_time)),1)]),_:1}),s(r,{label:"执行时长"},{default:e(()=>[h(d(S(c.execution.duration_seconds)),1)]),_:1}),s(r,{label:"结果数量"},{default:e(()=>[s(p(z),{type:"info",size:"small"},{default:e(()=>[h(d(c.execution.results_count)+" 条 ",1)]),_:1})]),_:1}),N.value&&c.execution.user_username?(i(),$(r,{key:0,label:"执行用户"},{default:e(()=>[m("div",null,[m("div",ze,d(c.execution.user_username),1),m("div",De,d(c.execution.user_email),1)])]),_:1})):T("",!0),s(r,{label:"创建时间"},{default:e(()=>[h(d(x(c.execution.created_at)),1)]),_:1}),c.execution.task_config?(i(),$(r,{key:1,label:"任务配置",span:2},{default:e(()=>[m("div",Ae,[m("div",Ve,[n[2]||(n[2]=m("span",{class:"config-label"},"扫描指标：",-1)),(i(!0),f(U,null,O(M().indicators||[],k=>(i(),$(p(z),{key:k,class:"mr-1 mb-1",size:"small"},{default:e(()=>[h(d(k),1)]),_:2},1024))),128))]),M().stock_codes?(i(),f("div",Be,[n[3]||(n[3]=m("span",{class:"config-label"},"股票代码：",-1)),m("div",Ie,[(i(!0),f(U,null,O(M().stock_codes.slice(0,10),k=>(i(),$(p(z),{key:k,class:"mr-1 mb-1",size:"small",type:"success"},{default:e(()=>[h(d(k),1)]),_:2},1024))),128)),M().stock_codes.length>10?(i(),f("span",Pe," ... 等 "+d(M().stock_codes.length)+" 只股票 ",1)):T("",!0)])])):T("",!0),m("div",je,[n[4]||(n[4]=m("span",{class:"config-label"},"扫描周期：",-1)),(i(!0),f(U,null,O(M().periods||["d"],k=>(i(),$(p(z),{key:k,class:"mr-1",size:"small",type:"warning"},{default:e(()=>[h(d(l(k)),1)]),_:2},1024))),128))]),m("div",Fe,[n[5]||(n[5]=m("span",{class:"config-label"},"复权方式：",-1)),h(" "+d(t(M().adjust)),1)])])]),_:1})):T("",!0)]),_:1})]),c.execution.error_message?(i(),f("div",Ue,[n[7]||(n[7]=m("h3",null,"错误信息",-1)),s(C,{title:c.execution.error_message,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):T("",!0),c.execution.results_data&&c.execution.results_data.length>0?(i(),f("div",Oe,[m("h3",null,[n[8]||(n[8]=h(" 执行结果 ",-1)),s(p(z),{size:"small",type:"info"},{default:e(()=>[h(d(c.execution.results_count)+" 条",1)]),_:1})]),s(ue,{data:W.value,columns:H,pagination:y.value,onPageChange:P},{signals:e(({row:k})=>[k.signals&&Array.isArray(k.signals)?(i(!0),f(U,{key:0},O(k.signals,j=>(i(),$(p(z),{key:j,type:I(j),size:"small",class:"mr-1"},{default:e(()=>[h(d(j),1)]),_:2},1032,["type"]))),128)):T("",!0)]),_:1},8,["data","pagination"])])):T("",!0)])):T("",!0)]),_:1},8,["modelValue"])}}},qe=re(Ne,[["__scopeId","data-v-3935e595"]]),We={class:"task-history-page card"},He={class:"filters-section"},Re={class:"task-list-section card"},Ge={key:0},Ke={class:"font-medium"},Qe={class:"text-xs text-gray-500"},Je={key:1,class:"text-gray-400"},Xe={key:0,class:"font-medium"},Ye={key:1,class:"text-gray-500 italic"},Ze={key:0},et={key:1},tt={key:2},at={key:0},nt={key:1},st={class:"table-actions"},lt={__name:"index",setup(c){const{loading:B,taskExecutions:_,pagination:v,filters:b,isAdmin:w,loadTaskExecutions:y,cancelExecution:N,deleteExecution:q,refreshData:W,getStatusTagType:H,getStatusText:M,formatDuration:R,formatDateTime:G}=$e(),I=V([]),l=V(!1),t=V(null),x=V(!1);let S=null;const P=L(()=>{const u=[{prop:"id",label:"ID","min-width":80,fixed:"left"}];return w.value&&u.push({prop:"user_info",label:"用户","min-width":140,slot:"user_info"}),u.push({prop:"task_name",label:"任务名称","min-width":160,slot:"task_name"},{prop:"trigger_type",label:"触发方式","min-width":100,slot:"trigger_type"},{prop:"status",label:"执行状态","min-width":100,slot:"status"},{prop:"results_count",label:"结果数量","min-width":100},{prop:"duration",label:"执行时长","min-width":100,slot:"duration"},{prop:"start_time",label:"开始时间","min-width":160,slot:"start_time"}),u}),Q=async()=>{if(w.value){x.value=!0;try{const u=await me.getUserList();u&&u.data?I.value=Array.isArray(u.data)?u.data:[]:I.value=[]}catch(u){console.error("加载用户列表失败:",u),A.error("加载用户列表失败"),I.value=[]}finally{x.value=!1}}},a=()=>{v.current=1,y()},n=({page:u,size:g})=>{v.current=u,v.pageSize=g,y()},r=u=>{t.value=u,l.value=!0},E=u=>u.status==="pending"||u.status==="running",C=u=>u.status!=="pending"&&u.status!=="running",K=async u=>{try{await ne.confirm("确认取消此任务执行？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await N(u)}catch(g){g!=="cancel"&&console.error(g)}},J=async u=>{try{await ne.confirm("确认删除此执行记录？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await q(u)}catch(g){g!=="cancel"&&console.error(g)}},k=()=>{S=setInterval(async()=>{try{await y()}catch(u){console.warn("轮询更新失败:",u)}},1e4)},j=()=>{S&&(clearInterval(S),S=null)};return pe(async()=>{w.value&&await Q(),await W(),k()}),ge(()=>{j()}),(u,g)=>{const D=be,X=ke,Y=ve,de=we,se=z,Z=ce,ee=Te;return i(),f("div",We,[g[6]||(g[6]=m("div",{class:"page-header"},[m("h1",null,"任务历史")],-1)),m("div",He,[s(de,{inline:"",onSubmit:g[3]||(g[3]=fe(()=>{},["prevent"]))},{default:e(()=>[s(Y,{label:"触发类型"},{default:e(()=>[s(X,{modelValue:p(b).triggerType,"onUpdate:modelValue":g[0]||(g[0]=o=>p(b).triggerType=o),placeholder:"全部",clearable:"",onChange:a,style:{width:"150px"}},{default:e(()=>[s(D,{label:"手动触发",value:"manual"}),s(D,{label:"定时触发",value:"scheduled"})]),_:1},8,["modelValue"])]),_:1}),s(Y,{label:"执行状态"},{default:e(()=>[s(X,{modelValue:p(b).status,"onUpdate:modelValue":g[1]||(g[1]=o=>p(b).status=o),placeholder:"全部",clearable:"",onChange:a,style:{width:"150px"}},{default:e(()=>[s(D,{label:"待执行",value:"pending"}),s(D,{label:"执行中",value:"running"}),s(D,{label:"已完成",value:"completed"}),s(D,{label:"已失败",value:"failed"}),s(D,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),p(w)?(i(),$(Y,{key:0,label:"用户"},{default:e(()=>[s(X,{modelValue:p(b).userId,"onUpdate:modelValue":g[2]||(g[2]=o=>p(b).userId=o),placeholder:"全部用户",clearable:"",filterable:"",loading:x.value,onChange:a,style:{width:"150px"}},{default:e(()=>[(i(!0),f(U,null,O(I.value,o=>(i(),$(D,{key:o.id,label:`${o.username} (${o.email})`,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})):T("",!0)]),_:1})]),m("div",Re,[s(ue,{data:p(_),loading:p(B),columns:P.value,stripe:!0,"show-pagination":!0,pagination:{total:p(v).total,pageSizes:[20,50,100],layout:"total, sizes, prev, pager, next, jumper"},"current-page":p(v).current,"page-size":p(v).pageSize,"empty-text":"暂无任务执行记录",onPageChange:n,class:"task-history-table"},{user_info:e(({row:o})=>[o.user_username?(i(),f("div",Ge,[m("div",Ke,d(o.user_username),1),m("div",Qe,d(o.user_email),1)])):(i(),f("div",Je,"未知用户"))]),task_name:e(({row:o})=>[o.scheduled_task_name?(i(),f("div",Xe,d(o.scheduled_task_name),1)):(i(),f("div",Ye," 手动扫描 "))]),trigger_type:e(({row:o})=>[s(se,{type:o.trigger_type==="scheduled"?"success":"info",size:"small"},{default:e(()=>[h(d(o.trigger_type==="scheduled"?"定时":"手动"),1)]),_:2},1032,["type"])]),status:e(({row:o})=>[s(se,{type:p(H)(o.status),size:"small"},{default:e(()=>[h(d(p(M)(o.status)),1)]),_:2},1032,["type"])]),duration:e(({row:o})=>[o.duration_seconds?(i(),f("span",Ze,d(p(R)(o.duration_seconds)),1)):o.status==="running"?(i(),f("span",et,g[5]||(g[5]=[m("i",{class:"i-carbon-time animate-spin"},null,-1)]))):(i(),f("span",tt,"-"))]),start_time:e(({row:o})=>[o.start_time?(i(),f("span",at,d(p(G)(o.start_time)),1)):(i(),f("span",nt,"-"))]),actions:e(({row:o})=>[m("div",st,[s(ee,{content:"查看详情",placement:"top"},{default:e(()=>[s(Z,{text:"",size:"small",circle:"",onClick:le=>r(o),class:"action-btn-view"},{default:e(()=>[s(te,{name:"view"})]),_:2},1032,["onClick"])]),_:2},1024),E(o)?(i(),$(ee,{key:0,content:"取消执行",placement:"top"},{default:e(()=>[s(Z,{text:"",size:"small",circle:"",onClick:le=>K(o.id),class:"action-btn-cancel"},{default:e(()=>[s(te,{name:"stop"})]),_:2},1032,["onClick"])]),_:2},1024)):T("",!0),C(o)?(i(),$(ee,{key:1,content:"删除记录",placement:"top"},{default:e(()=>[s(Z,{text:"",size:"small",circle:"",onClick:le=>J(o.id),class:"action-btn-delete"},{default:e(()=>[s(te,{name:"delete"})]),_:2},1032,["onClick"])]),_:2},1024)):T("",!0)])]),_:1},8,["data","loading","columns","pagination","current-page","page-size"])]),s(qe,{modelValue:l.value,"onUpdate:modelValue":g[4]||(g[4]=o=>l.value=o),execution:t.value},null,8,["modelValue","execution"])])}}},ft=re(lt,[["__scopeId","data-v-e67d9701"]]);export{ft as default};
