const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-DQncb-nM.js","./vendor-Dq0JXR-b.js","./ui-CHSsU9Lc.js","./index-CjhSXAbF.css","./el-form-item-BWkJzdQ_.css","./el-checkbox-DIj50LEB.css","./el-input-tPmZxDKr.css","./index-C6qRbeXw.js","./searchStats-Dfdmz4M0.js","./charts-ClPwX7rX.js","./index-WMPcN2JM.css","./el-overlay-uHqKdL1G.css","./el-input-number-D6iOyBgb.css","./index-IUI9w0dn.js","./IconButton-BMtfTzry.js","./IconButton-GjjX0zXV.css","./index-DXc5njjq.css","./index-lSW7P6EN.js","./CommonPagination-IzNmwDoz.js","./CommonPagination-CeCTNVef.css","./index-BCyATiNR.css","./index-CeGQYd4B.js","./index-CzYcfKL2.js","./CommonTable-DDC4hMKa.js","./CommonTable-CiD31kvl.css","./PeriodIndicatorMatrix-CdnFxisq.js","./PeriodIndicatorMatrix-Dymvm3A0.css","./index-DE6pJB_Z.css","./index-oiosCFXF.js","./el-select-BLKpJAwO.js","./el-select-DC6_bRTH.css","./index-B6rTzKJy.css","./el-radio-BTLfVIp4.css","./index-BfUCavJo.js","./index-BsxEmPif.css","./index-BpoXCZp9.js","./index-WwSpi2O_.css","./index-ZuRl6Ynu.js","./index-BZTpWC8a.css","./index-BbcJuyos.js"])))=>i.map(i=>d[i]);
import{n as Vt,h as Dr,o as Fr,g as dt,as as Jt,i as Ir,w as je,f as Zt,D as Nr,r as F,at as $r,j as Be,s as le,c as U,au as Y,e as Lr,u as D,av as Se,aw as Mr,x as L,E as J,y as C,A as bt,ax as Ur,ay as jr,az as Kt,ae as ft,B as k,I as q,O as N,M as $e,ab as Le,F as B,L as V,H as ie,aA as Br,Q as qr,G as zr,al as Hr,ac as Wr,K as oe,ah as Vr,a0 as Gt,T as St,J as kt,aB as Jr}from"./vendor-Dq0JXR-b.js";import{E as De,I as Zr,a as Kr,b as Gr,c as Xr,d as Qr,e as Yr,f as en}from"./ui-CHSsU9Lc.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function r(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(s){if(s.ep)return;s.ep=!0;const o=r(s);fetch(s.href,o)}})();function Xt(e){return Dr()?(Fr(e),!0):!1}const Ze=new WeakMap,tn=(...e)=>{var t;const r=e[0],n=(t=dt())==null?void 0:t.proxy;if(n==null&&!Jt())throw new Error("injectLocal must be called in setup");return n&&Ze.has(n)&&r in Ze.get(n)?Ze.get(n)[r]:Ir(...e)},Qt=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const rn=Object.prototype.toString,nn=e=>rn.call(e)==="[object Object]",sn=()=>{};function on(...e){if(e.length!==1)return Nr(...e);const t=e[0];return typeof t=="function"?Zt($r(()=>({get:t,set:sn}))):F(t)}function an(e,t){function r(...n){return new Promise((s,o)=>{Promise.resolve(e(()=>t.apply(this,n),{fn:t,thisArg:this,args:n})).then(s).catch(o)})}return r}const Yt=e=>e();function cn(e=Yt,t={}){const{initialState:r="active"}=t,n=on(r==="active");function s(){n.value=!1}function o(){n.value=!0}const a=(...i)=>{n.value&&e(...i)};return{isActive:Zt(n),pause:s,resume:o,eventFilter:a}}function ln(e,t){var r;if(typeof e=="number")return e+t;const n=((r=e.match(/^-?\d+\.?\d*/))==null?void 0:r[0])||"",s=e.slice(n.length),o=Number.parseFloat(n)+t;return Number.isNaN(o)?e:o+s}function _e(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function Ke(e){return Array.isArray(e)?e:[e]}function un(e){return dt()}function dn(e,t,r={}){const{eventFilter:n=Yt,...s}=r;return je(e,an(n,t),s)}function fn(e,t,r={}){const{eventFilter:n,initialState:s="active",...o}=r,{eventFilter:a,pause:i,resume:u,isActive:c}=cn(n,{initialState:s});return{stop:dn(e,t,{...o,eventFilter:a}),pause:i,resume:u,isActive:c}}function mt(e,t=!0,r){un()?Be(e,r):t?e():Vt(e)}function mn(e,t,r){return je(e,t,{...r,immediate:!0})}const me=Qt?window:void 0,hn=Qt?window.document:void 0;function nt(e){var t;const r=Y(e);return(t=r==null?void 0:r.$el)!=null?t:r}function ve(...e){const t=[],r=()=>{t.forEach(i=>i()),t.length=0},n=(i,u,c,l)=>(i.addEventListener(u,c,l),()=>i.removeEventListener(u,c,l)),s=U(()=>{const i=Ke(Y(e[0])).filter(u=>u!=null);return i.every(u=>typeof u!="string")?i:void 0}),o=mn(()=>{var i,u;return[(u=(i=s.value)==null?void 0:i.map(c=>nt(c)))!=null?u:[me].filter(c=>c!=null),Ke(Y(s.value?e[1]:e[0])),Ke(D(s.value?e[2]:e[1])),Y(s.value?e[3]:e[2])]},([i,u,c,l])=>{if(r(),!(i!=null&&i.length)||!(u!=null&&u.length)||!(c!=null&&c.length))return;const h=nn(l)?{...l}:l;t.push(...i.flatMap(w=>u.flatMap(g=>c.map(f=>n(w,g,f,h)))))},{flush:"post"}),a=()=>{o(),r()};return Xt(r),a}function pn(){const e=le(!1),t=dt();return t&&Be(()=>{e.value=!0},t),e}function er(e){const t=pn();return U(()=>(t.value,!!e()))}const gn=Symbol("vueuse-ssr-width");function tr(){const e=Jt()?tn(gn,null):null;return typeof e=="number"?e:void 0}function ge(e,t={}){const{window:r=me,ssrWidth:n=tr()}=t,s=er(()=>r&&"matchMedia"in r&&typeof r.matchMedia=="function"),o=le(typeof n=="number"),a=le(),i=le(!1),u=c=>{i.value=c.matches};return Lr(()=>{if(o.value){o.value=!s.value;const c=Y(e).split(",");i.value=c.some(l=>{const h=l.includes("not all"),w=l.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),g=l.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let f=!!(w||g);return w&&f&&(f=n>=_e(w[1])),g&&f&&(f=n<=_e(g[1])),h?!f:f});return}s.value&&(a.value=r.matchMedia(Y(e)),i.value=a.value.matches)}),ve(a,"change",u,{passive:!0}),U(()=>i.value)}const yn={sm:640,md:768,lg:1024,xl:1280,"2xl":1536};function wn(e,t={}){function r(g,f){let m=Y(e[Y(g)]);return f!=null&&(m=ln(m,f)),typeof m=="number"&&(m=`${m}px`),m}const{window:n=me,strategy:s="min-width",ssrWidth:o=tr()}=t,a=typeof o=="number",i=a?le(!1):{value:!0};a&&mt(()=>i.value=!!n);function u(g,f){return!i.value&&a?g==="min"?o>=_e(f):o<=_e(f):n?n.matchMedia(`(${g}-width: ${f})`).matches:!1}const c=g=>ge(()=>`(min-width: ${r(g)})`,t),l=g=>ge(()=>`(max-width: ${r(g)})`,t),h=Object.keys(e).reduce((g,f)=>(Object.defineProperty(g,f,{get:()=>s==="min-width"?c(f):l(f),enumerable:!0,configurable:!0}),g),{});function w(){const g=Object.keys(e).map(f=>[f,h[f],_e(r(f))]).sort((f,m)=>f[2]-m[2]);return U(()=>g.filter(([,f])=>f.value).map(([f])=>f))}return Object.assign(h,{greaterOrEqual:c,smallerOrEqual:l,greater(g){return ge(()=>`(min-width: ${r(g,.1)})`,t)},smaller(g){return ge(()=>`(max-width: ${r(g,-.1)})`,t)},between(g,f){return ge(()=>`(min-width: ${r(g)}) and (max-width: ${r(f,-.1)})`,t)},isGreater(g){return u("min",r(g,.1))},isGreaterOrEqual(g){return u("min",r(g))},isSmaller(g){return u("max",r(g,-.1))},isSmallerOrEqual(g){return u("max",r(g))},isInBetween(g,f){return u("min",r(g))&&u("max",r(f,-.1))},current:w,active(){const g=w();return U(()=>g.value.length===0?"":g.value.at(s==="min-width"?-1:0))}})}const Ce=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Pe="__vueuse_ssr_handlers__",_n=vn();function vn(){return Pe in Ce||(Ce[Pe]=Ce[Pe]||{}),Ce[Pe]}function bn(e,t){return _n[e]||t}function Sn(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const kn={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},xt="vueuse-storage";function xn(e,t,r,n={}){var s;const{flush:o="pre",deep:a=!0,listenToStorageChanges:i=!0,writeDefaults:u=!0,mergeDefaults:c=!1,shallow:l,window:h=me,eventFilter:w,onError:g=y=>{console.error(y)},initOnMounted:f}=n,m=(l?le:F)(typeof t=="function"?t():t),p=U(()=>Y(e));if(!r)try{r=bn("getDefaultStorage",()=>{var y;return(y=me)==null?void 0:y.localStorage})()}catch(y){g(y)}if(!r)return m;const E=Y(t),_=Sn(E),v=(s=n.serializer)!=null?s:kn[_],{pause:P,resume:I}=fn(m,()=>se(m.value),{flush:o,deep:a,eventFilter:w});je(p,()=>b(),{flush:o});let x=!1;const T=y=>{f&&!x||b(y)},M=y=>{f&&!x||A(y)};h&&i&&(r instanceof Storage?ve(h,"storage",T,{passive:!0}):ve(h,xt,M)),f?mt(()=>{x=!0,b()}):b();function re(y,R){if(h){const W={key:p.value,oldValue:y,newValue:R,storageArea:r};h.dispatchEvent(r instanceof Storage?new StorageEvent("storage",W):new CustomEvent(xt,{detail:W}))}}function se(y){try{const R=r.getItem(p.value);if(y==null)re(R,null),r.removeItem(p.value);else{const W=v.write(y);R!==W&&(r.setItem(p.value,W),re(R,W))}}catch(R){g(R)}}function Oe(y){const R=y?y.newValue:r.getItem(p.value);if(R==null)return u&&E!=null&&r.setItem(p.value,v.write(E)),E;if(!y&&c){const W=v.read(R);return typeof c=="function"?c(W,E):_==="object"&&!Array.isArray(W)?{...E,...W}:W}else return typeof R!="string"?R:v.read(R)}function b(y){if(!(y&&y.storageArea!==r)){if(y&&y.key==null){m.value=E;return}if(!(y&&y.key!==p.value)){P();try{(y==null?void 0:y.newValue)!==v.write(m.value)&&(m.value=Oe(y))}catch(R){g(R)}finally{y?Vt(I):I()}}}}function A(y){b(y.detail)}return m}const Et=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function En(e,t={}){const{document:r=hn,autoExit:n=!1}=t,s=U(()=>{var _;return(_=nt(e))!=null?_:r==null?void 0:r.documentElement}),o=le(!1),a=U(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(_=>r&&_ in r||s.value&&_ in s.value)),i=U(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(_=>r&&_ in r||s.value&&_ in s.value)),u=U(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(_=>r&&_ in r||s.value&&_ in s.value)),c=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(_=>r&&_ in r),l=er(()=>s.value&&r&&a.value!==void 0&&i.value!==void 0&&u.value!==void 0),h=()=>c?(r==null?void 0:r[c])===s.value:!1,w=()=>{if(u.value){if(r&&r[u.value]!=null)return r[u.value];{const _=s.value;if((_==null?void 0:_[u.value])!=null)return!!_[u.value]}}return!1};async function g(){if(!(!l.value||!o.value)){if(i.value)if((r==null?void 0:r[i.value])!=null)await r[i.value]();else{const _=s.value;(_==null?void 0:_[i.value])!=null&&await _[i.value]()}o.value=!1}}async function f(){if(!l.value||o.value)return;w()&&await g();const _=s.value;a.value&&(_==null?void 0:_[a.value])!=null&&(await _[a.value](),o.value=!0)}async function m(){await(o.value?g():f())}const p=()=>{const _=w();(!_||_&&h())&&(o.value=_)},E={capture:!1,passive:!0};return ve(r,Et,p,E),ve(()=>nt(s),Et,p,E),mt(p,!1),n&&Xt(g),{isSupported:l,isFullscreen:o,enter:f,exit:g,toggle:m}}function Pa(e,t,r={}){const{window:n=me}=r;return xn(e,t,n==null?void 0:n.localStorage,r)}const ht=Se("theme",()=>{const e=F(!0),t=U(()=>e.value?"dark":"light"),r=U(()=>e.value?"dark":"light"),n=()=>{e.value=!e.value,o(),localStorage.setItem("theme",t.value)},s=i=>{e.value=i==="dark",o(),localStorage.setItem("theme",i)},o=()=>{const i=document.body;e.value?(i.classList.add("dark"),i.classList.remove("light")):(i.classList.remove("dark"),i.classList.add("light"))};return{isDark:e,theme:t,themeClass:r,toggleTheme:n,setTheme:s,initTheme:()=>{const i=localStorage.getItem("theme");if(i)s(i);else{const u=window.matchMedia("(prefers-color-scheme: dark)").matches;s(u?"dark":"light")}}}}),Tn="modulepreload",An=function(e,t){return new URL(e,t).href},Tt={},G=function(t,r,n){let s=Promise.resolve();if(r&&r.length>0){let a=function(l){return Promise.all(l.map(h=>Promise.resolve(h).then(w=>({status:"fulfilled",value:w}),w=>({status:"rejected",reason:w}))))};const i=document.getElementsByTagName("link"),u=document.querySelector("meta[property=csp-nonce]"),c=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));s=a(r.map(l=>{if(l=An(l,n),l in Tt)return;Tt[l]=!0;const h=l.endsWith(".css"),w=h?'[rel="stylesheet"]':"";if(!!n)for(let m=i.length-1;m>=0;m--){const p=i[m];if(p.href===l&&(!h||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${w}`))return;const f=document.createElement("link");if(f.rel=h?"stylesheet":Tn,h||(f.as="script"),f.crossOrigin="",f.href=l,c&&f.setAttribute("nonce",c),document.head.appendChild(f),h)return new Promise((m,p)=>{f.addEventListener("load",m),f.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${l}`)))})}))}function o(a){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=a,window.dispatchEvent(i),!i.defaultPrevented)throw a}return s.then(a=>{for(const i of a||[])i.status==="rejected"&&o(i.reason);return t().catch(o)})};function rr(e,t){return function(){return e.apply(t,arguments)}}const{toString:Rn}=Object.prototype,{getPrototypeOf:pt}=Object,{iterator:qe,toStringTag:nr}=Symbol,ze=(e=>t=>{const r=Rn.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),X=e=>(e=e.toLowerCase(),t=>ze(t)===e),He=e=>t=>typeof t===e,{isArray:he}=Array,be=He("undefined");function ke(e){return e!==null&&!be(e)&&e.constructor!==null&&!be(e.constructor)&&Z(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const sr=X("ArrayBuffer");function On(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&sr(e.buffer),t}const Cn=He("string"),Z=He("function"),or=He("number"),xe=e=>e!==null&&typeof e=="object",Pn=e=>e===!0||e===!1,Fe=e=>{if(ze(e)!=="object")return!1;const t=pt(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(nr in e)&&!(qe in e)},Dn=e=>{if(!xe(e)||ke(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Fn=X("Date"),In=X("File"),Nn=X("Blob"),$n=X("FileList"),Ln=e=>xe(e)&&Z(e.pipe),Mn=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Z(e.append)&&((t=ze(e))==="formdata"||t==="object"&&Z(e.toString)&&e.toString()==="[object FormData]"))},Un=X("URLSearchParams"),[jn,Bn,qn,zn]=["ReadableStream","Request","Response","Headers"].map(X),Hn=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ee(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,s;if(typeof e!="object"&&(e=[e]),he(e))for(n=0,s=e.length;n<s;n++)t.call(null,e[n],n,e);else{if(ke(e))return;const o=r?Object.getOwnPropertyNames(e):Object.keys(e),a=o.length;let i;for(n=0;n<a;n++)i=o[n],t.call(null,e[i],i,e)}}function ar(e,t){if(ke(e))return null;t=t.toLowerCase();const r=Object.keys(e);let n=r.length,s;for(;n-- >0;)if(s=r[n],t===s.toLowerCase())return s;return null}const ce=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ir=e=>!be(e)&&e!==ce;function st(){const{caseless:e}=ir(this)&&this||{},t={},r=(n,s)=>{const o=e&&ar(t,s)||s;Fe(t[o])&&Fe(n)?t[o]=st(t[o],n):Fe(n)?t[o]=st({},n):he(n)?t[o]=n.slice():t[o]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&Ee(arguments[n],r);return t}const Wn=(e,t,r,{allOwnKeys:n}={})=>(Ee(t,(s,o)=>{r&&Z(s)?e[o]=rr(s,r):e[o]=s},{allOwnKeys:n}),e),Vn=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Jn=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Zn=(e,t,r,n)=>{let s,o,a;const i={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)a=s[o],(!n||n(a,e,t))&&!i[a]&&(t[a]=e[a],i[a]=!0);e=r!==!1&&pt(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},Kn=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},Gn=e=>{if(!e)return null;if(he(e))return e;let t=e.length;if(!or(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Xn=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&pt(Uint8Array)),Qn=(e,t)=>{const n=(e&&e[qe]).call(e);let s;for(;(s=n.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Yn=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},es=X("HTMLFormElement"),ts=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),At=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),rs=X("RegExp"),cr=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};Ee(r,(s,o)=>{let a;(a=t(s,o,e))!==!1&&(n[o]=a||s)}),Object.defineProperties(e,n)},ns=e=>{cr(e,(t,r)=>{if(Z(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(Z(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},ss=(e,t)=>{const r={},n=s=>{s.forEach(o=>{r[o]=!0})};return he(e)?n(e):n(String(e).split(t)),r},os=()=>{},as=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function is(e){return!!(e&&Z(e.append)&&e[nr]==="FormData"&&e[qe])}const cs=e=>{const t=new Array(10),r=(n,s)=>{if(xe(n)){if(t.indexOf(n)>=0)return;if(ke(n))return n;if(!("toJSON"in n)){t[s]=n;const o=he(n)?[]:{};return Ee(n,(a,i)=>{const u=r(a,s+1);!be(u)&&(o[i]=u)}),t[s]=void 0,o}}return n};return r(e,0)},ls=X("AsyncFunction"),us=e=>e&&(xe(e)||Z(e))&&Z(e.then)&&Z(e.catch),lr=((e,t)=>e?setImmediate:t?((r,n)=>(ce.addEventListener("message",({source:s,data:o})=>{s===ce&&o===r&&n.length&&n.shift()()},!1),s=>{n.push(s),ce.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Z(ce.postMessage)),ds=typeof queueMicrotask<"u"?queueMicrotask.bind(ce):typeof process<"u"&&process.nextTick||lr,fs=e=>e!=null&&Z(e[qe]),d={isArray:he,isArrayBuffer:sr,isBuffer:ke,isFormData:Mn,isArrayBufferView:On,isString:Cn,isNumber:or,isBoolean:Pn,isObject:xe,isPlainObject:Fe,isEmptyObject:Dn,isReadableStream:jn,isRequest:Bn,isResponse:qn,isHeaders:zn,isUndefined:be,isDate:Fn,isFile:In,isBlob:Nn,isRegExp:rs,isFunction:Z,isStream:Ln,isURLSearchParams:Un,isTypedArray:Xn,isFileList:$n,forEach:Ee,merge:st,extend:Wn,trim:Hn,stripBOM:Vn,inherits:Jn,toFlatObject:Zn,kindOf:ze,kindOfTest:X,endsWith:Kn,toArray:Gn,forEachEntry:Qn,matchAll:Yn,isHTMLForm:es,hasOwnProperty:At,hasOwnProp:At,reduceDescriptors:cr,freezeMethods:ns,toObjectSet:ss,toCamelCase:ts,noop:os,toFiniteNumber:as,findKey:ar,global:ce,isContextDefined:ir,isSpecCompliantForm:is,toJSONObject:cs,isAsyncFn:ls,isThenable:us,setImmediate:lr,asap:ds,isIterable:fs};function O(e,t,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}d.inherits(O,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:d.toJSONObject(this.config),code:this.code,status:this.status}}});const ur=O.prototype,dr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{dr[e]={value:e}});Object.defineProperties(O,dr);Object.defineProperty(ur,"isAxiosError",{value:!0});O.from=(e,t,r,n,s,o)=>{const a=Object.create(ur);return d.toFlatObject(e,a,function(u){return u!==Error.prototype},i=>i!=="isAxiosError"),O.call(a,e.message,t,r,n,s),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};const ms=null;function ot(e){return d.isPlainObject(e)||d.isArray(e)}function fr(e){return d.endsWith(e,"[]")?e.slice(0,-2):e}function Rt(e,t,r){return e?e.concat(t).map(function(s,o){return s=fr(s),!r&&o?"["+s+"]":s}).join(r?".":""):t}function hs(e){return d.isArray(e)&&!e.some(ot)}const ps=d.toFlatObject(d,{},null,function(t){return/^is[A-Z]/.test(t)});function We(e,t,r){if(!d.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=d.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,p){return!d.isUndefined(p[m])});const n=r.metaTokens,s=r.visitor||l,o=r.dots,a=r.indexes,u=(r.Blob||typeof Blob<"u"&&Blob)&&d.isSpecCompliantForm(t);if(!d.isFunction(s))throw new TypeError("visitor must be a function");function c(f){if(f===null)return"";if(d.isDate(f))return f.toISOString();if(d.isBoolean(f))return f.toString();if(!u&&d.isBlob(f))throw new O("Blob is not supported. Use a Buffer instead.");return d.isArrayBuffer(f)||d.isTypedArray(f)?u&&typeof Blob=="function"?new Blob([f]):Buffer.from(f):f}function l(f,m,p){let E=f;if(f&&!p&&typeof f=="object"){if(d.endsWith(m,"{}"))m=n?m:m.slice(0,-2),f=JSON.stringify(f);else if(d.isArray(f)&&hs(f)||(d.isFileList(f)||d.endsWith(m,"[]"))&&(E=d.toArray(f)))return m=fr(m),E.forEach(function(v,P){!(d.isUndefined(v)||v===null)&&t.append(a===!0?Rt([m],P,o):a===null?m:m+"[]",c(v))}),!1}return ot(f)?!0:(t.append(Rt(p,m,o),c(f)),!1)}const h=[],w=Object.assign(ps,{defaultVisitor:l,convertValue:c,isVisitable:ot});function g(f,m){if(!d.isUndefined(f)){if(h.indexOf(f)!==-1)throw Error("Circular reference detected in "+m.join("."));h.push(f),d.forEach(f,function(E,_){(!(d.isUndefined(E)||E===null)&&s.call(t,E,d.isString(_)?_.trim():_,m,w))===!0&&g(E,m?m.concat(_):[_])}),h.pop()}}if(!d.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Ot(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function gt(e,t){this._pairs=[],e&&We(e,this,t)}const mr=gt.prototype;mr.append=function(t,r){this._pairs.push([t,r])};mr.toString=function(t){const r=t?function(n){return t.call(this,n,Ot)}:Ot;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function gs(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function hr(e,t,r){if(!t)return e;const n=r&&r.encode||gs;d.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let o;if(s?o=s(t,r):o=d.isURLSearchParams(t)?t.toString():new gt(t,r).toString(n),o){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Ct{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){d.forEach(this.handlers,function(n){n!==null&&t(n)})}}const pr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ys=typeof URLSearchParams<"u"?URLSearchParams:gt,ws=typeof FormData<"u"?FormData:null,_s=typeof Blob<"u"?Blob:null,vs={isBrowser:!0,classes:{URLSearchParams:ys,FormData:ws,Blob:_s},protocols:["http","https","file","blob","url","data"]},yt=typeof window<"u"&&typeof document<"u",at=typeof navigator=="object"&&navigator||void 0,bs=yt&&(!at||["ReactNative","NativeScript","NS"].indexOf(at.product)<0),Ss=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",ks=yt&&window.location.href||"http://localhost",xs=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:yt,hasStandardBrowserEnv:bs,hasStandardBrowserWebWorkerEnv:Ss,navigator:at,origin:ks},Symbol.toStringTag,{value:"Module"})),H={...xs,...vs};function Es(e,t){return We(e,new H.classes.URLSearchParams,{visitor:function(r,n,s,o){return H.isNode&&d.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function Ts(e){return d.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function As(e){const t={},r=Object.keys(e);let n;const s=r.length;let o;for(n=0;n<s;n++)o=r[n],t[o]=e[o];return t}function gr(e){function t(r,n,s,o){let a=r[o++];if(a==="__proto__")return!0;const i=Number.isFinite(+a),u=o>=r.length;return a=!a&&d.isArray(s)?s.length:a,u?(d.hasOwnProp(s,a)?s[a]=[s[a],n]:s[a]=n,!i):((!s[a]||!d.isObject(s[a]))&&(s[a]=[]),t(r,n,s[a],o)&&d.isArray(s[a])&&(s[a]=As(s[a])),!i)}if(d.isFormData(e)&&d.isFunction(e.entries)){const r={};return d.forEachEntry(e,(n,s)=>{t(Ts(n),s,r,0)}),r}return null}function Rs(e,t,r){if(d.isString(e))try{return(t||JSON.parse)(e),d.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const Te={transitional:pr,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,o=d.isObject(t);if(o&&d.isHTMLForm(t)&&(t=new FormData(t)),d.isFormData(t))return s?JSON.stringify(gr(t)):t;if(d.isArrayBuffer(t)||d.isBuffer(t)||d.isStream(t)||d.isFile(t)||d.isBlob(t)||d.isReadableStream(t))return t;if(d.isArrayBufferView(t))return t.buffer;if(d.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let i;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Es(t,this.formSerializer).toString();if((i=d.isFileList(t))||n.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return We(i?{"files[]":t}:t,u&&new u,this.formSerializer)}}return o||s?(r.setContentType("application/json",!1),Rs(t)):t}],transformResponse:[function(t){const r=this.transitional||Te.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(d.isResponse(t)||d.isReadableStream(t))return t;if(t&&d.isString(t)&&(n&&!this.responseType||s)){const a=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(i){if(a)throw i.name==="SyntaxError"?O.from(i,O.ERR_BAD_RESPONSE,this,null,this.response):i}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:H.classes.FormData,Blob:H.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};d.forEach(["delete","get","head","post","put","patch"],e=>{Te.headers[e]={}});const Os=d.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Cs=e=>{const t={};let r,n,s;return e&&e.split(`
`).forEach(function(a){s=a.indexOf(":"),r=a.substring(0,s).trim().toLowerCase(),n=a.substring(s+1).trim(),!(!r||t[r]&&Os[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Pt=Symbol("internals");function ye(e){return e&&String(e).trim().toLowerCase()}function Ie(e){return e===!1||e==null?e:d.isArray(e)?e.map(Ie):String(e)}function Ps(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Ds=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ge(e,t,r,n,s){if(d.isFunction(n))return n.call(this,t,r);if(s&&(t=r),!!d.isString(t)){if(d.isString(n))return t.indexOf(n)!==-1;if(d.isRegExp(n))return n.test(t)}}function Fs(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function Is(e,t){const r=d.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(s,o,a){return this[n].call(this,t,s,o,a)},configurable:!0})})}let K=class{constructor(t){t&&this.set(t)}set(t,r,n){const s=this;function o(i,u,c){const l=ye(u);if(!l)throw new Error("header name must be a non-empty string");const h=d.findKey(s,l);(!h||s[h]===void 0||c===!0||c===void 0&&s[h]!==!1)&&(s[h||u]=Ie(i))}const a=(i,u)=>d.forEach(i,(c,l)=>o(c,l,u));if(d.isPlainObject(t)||t instanceof this.constructor)a(t,r);else if(d.isString(t)&&(t=t.trim())&&!Ds(t))a(Cs(t),r);else if(d.isObject(t)&&d.isIterable(t)){let i={},u,c;for(const l of t){if(!d.isArray(l))throw TypeError("Object iterator must return a key-value pair");i[c=l[0]]=(u=i[c])?d.isArray(u)?[...u,l[1]]:[u,l[1]]:l[1]}a(i,r)}else t!=null&&o(r,t,n);return this}get(t,r){if(t=ye(t),t){const n=d.findKey(this,t);if(n){const s=this[n];if(!r)return s;if(r===!0)return Ps(s);if(d.isFunction(r))return r.call(this,s,n);if(d.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=ye(t),t){const n=d.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||Ge(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let s=!1;function o(a){if(a=ye(a),a){const i=d.findKey(n,a);i&&(!r||Ge(n,n[i],i,r))&&(delete n[i],s=!0)}}return d.isArray(t)?t.forEach(o):o(t),s}clear(t){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const o=r[n];(!t||Ge(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const r=this,n={};return d.forEach(this,(s,o)=>{const a=d.findKey(n,o);if(a){r[a]=Ie(s),delete r[o];return}const i=t?Fs(o):String(o).trim();i!==o&&delete r[o],r[i]=Ie(s),n[i]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return d.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=t&&d.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(s=>n.set(s)),n}static accessor(t){const n=(this[Pt]=this[Pt]={accessors:{}}).accessors,s=this.prototype;function o(a){const i=ye(a);n[i]||(Is(s,a),n[i]=!0)}return d.isArray(t)?t.forEach(o):o(t),this}};K.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);d.reduceDescriptors(K.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});d.freezeMethods(K);function Xe(e,t){const r=this||Te,n=t||r,s=K.from(n.headers);let o=n.data;return d.forEach(e,function(i){o=i.call(r,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function yr(e){return!!(e&&e.__CANCEL__)}function pe(e,t,r){O.call(this,e??"canceled",O.ERR_CANCELED,t,r),this.name="CanceledError"}d.inherits(pe,O,{__CANCEL__:!0});function wr(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new O("Request failed with status code "+r.status,[O.ERR_BAD_REQUEST,O.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Ns(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function $s(e,t){e=e||10;const r=new Array(e),n=new Array(e);let s=0,o=0,a;return t=t!==void 0?t:1e3,function(u){const c=Date.now(),l=n[o];a||(a=c),r[s]=u,n[s]=c;let h=o,w=0;for(;h!==s;)w+=r[h++],h=h%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),c-a<t)return;const g=l&&c-l;return g?Math.round(w*1e3/g):void 0}}function Ls(e,t){let r=0,n=1e3/t,s,o;const a=(c,l=Date.now())=>{r=l,s=null,o&&(clearTimeout(o),o=null),e(...c)};return[(...c)=>{const l=Date.now(),h=l-r;h>=n?a(c,l):(s=c,o||(o=setTimeout(()=>{o=null,a(s)},n-h)))},()=>s&&a(s)]}const Me=(e,t,r=3)=>{let n=0;const s=$s(50,250);return Ls(o=>{const a=o.loaded,i=o.lengthComputable?o.total:void 0,u=a-n,c=s(u),l=a<=i;n=a;const h={loaded:a,total:i,progress:i?a/i:void 0,bytes:u,rate:c||void 0,estimated:c&&i&&l?(i-a)/c:void 0,event:o,lengthComputable:i!=null,[t?"download":"upload"]:!0};e(h)},r)},Dt=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Ft=e=>(...t)=>d.asap(()=>e(...t)),Ms=H.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,H.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(H.origin),H.navigator&&/(msie|trident)/i.test(H.navigator.userAgent)):()=>!0,Us=H.hasStandardBrowserEnv?{write(e,t,r,n,s,o){const a=[e+"="+encodeURIComponent(t)];d.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),d.isString(n)&&a.push("path="+n),d.isString(s)&&a.push("domain="+s),o===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function js(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Bs(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function _r(e,t,r){let n=!js(t);return e&&(n||r==!1)?Bs(e,t):t}const It=e=>e instanceof K?{...e}:e;function de(e,t){t=t||{};const r={};function n(c,l,h,w){return d.isPlainObject(c)&&d.isPlainObject(l)?d.merge.call({caseless:w},c,l):d.isPlainObject(l)?d.merge({},l):d.isArray(l)?l.slice():l}function s(c,l,h,w){if(d.isUndefined(l)){if(!d.isUndefined(c))return n(void 0,c,h,w)}else return n(c,l,h,w)}function o(c,l){if(!d.isUndefined(l))return n(void 0,l)}function a(c,l){if(d.isUndefined(l)){if(!d.isUndefined(c))return n(void 0,c)}else return n(void 0,l)}function i(c,l,h){if(h in t)return n(c,l);if(h in e)return n(void 0,c)}const u={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:i,headers:(c,l,h)=>s(It(c),It(l),h,!0)};return d.forEach(Object.keys({...e,...t}),function(l){const h=u[l]||s,w=h(e[l],t[l],l);d.isUndefined(w)&&h!==i||(r[l]=w)}),r}const vr=e=>{const t=de({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:i}=t;t.headers=a=K.from(a),t.url=hr(_r(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),i&&a.set("Authorization","Basic "+btoa((i.username||"")+":"+(i.password?unescape(encodeURIComponent(i.password)):"")));let u;if(d.isFormData(r)){if(H.hasStandardBrowserEnv||H.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((u=a.getContentType())!==!1){const[c,...l]=u?u.split(";").map(h=>h.trim()).filter(Boolean):[];a.setContentType([c||"multipart/form-data",...l].join("; "))}}if(H.hasStandardBrowserEnv&&(n&&d.isFunction(n)&&(n=n(t)),n||n!==!1&&Ms(t.url))){const c=s&&o&&Us.read(o);c&&a.set(s,c)}return t},qs=typeof XMLHttpRequest<"u",zs=qs&&function(e){return new Promise(function(r,n){const s=vr(e);let o=s.data;const a=K.from(s.headers).normalize();let{responseType:i,onUploadProgress:u,onDownloadProgress:c}=s,l,h,w,g,f;function m(){g&&g(),f&&f(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function E(){if(!p)return;const v=K.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),I={data:!i||i==="text"||i==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:v,config:e,request:p};wr(function(T){r(T),m()},function(T){n(T),m()},I),p=null}"onloadend"in p?p.onloadend=E:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(E)},p.onabort=function(){p&&(n(new O("Request aborted",O.ECONNABORTED,e,p)),p=null)},p.onerror=function(){n(new O("Network Error",O.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let P=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const I=s.transitional||pr;s.timeoutErrorMessage&&(P=s.timeoutErrorMessage),n(new O(P,I.clarifyTimeoutError?O.ETIMEDOUT:O.ECONNABORTED,e,p)),p=null},o===void 0&&a.setContentType(null),"setRequestHeader"in p&&d.forEach(a.toJSON(),function(P,I){p.setRequestHeader(I,P)}),d.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),i&&i!=="json"&&(p.responseType=s.responseType),c&&([w,f]=Me(c,!0),p.addEventListener("progress",w)),u&&p.upload&&([h,g]=Me(u),p.upload.addEventListener("progress",h),p.upload.addEventListener("loadend",g)),(s.cancelToken||s.signal)&&(l=v=>{p&&(n(!v||v.type?new pe(null,e,p):v),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const _=Ns(s.url);if(_&&H.protocols.indexOf(_)===-1){n(new O("Unsupported protocol "+_+":",O.ERR_BAD_REQUEST,e));return}p.send(o||null)})},Hs=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,s;const o=function(c){if(!s){s=!0,i();const l=c instanceof Error?c:this.reason;n.abort(l instanceof O?l:new pe(l instanceof Error?l.message:l))}};let a=t&&setTimeout(()=>{a=null,o(new O(`timeout ${t} of ms exceeded`,O.ETIMEDOUT))},t);const i=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:u}=n;return u.unsubscribe=()=>d.asap(i),u}},Ws=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,s;for(;n<r;)s=n+t,yield e.slice(n,s),n=s},Vs=async function*(e,t){for await(const r of Js(e))yield*Ws(r,t)},Js=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},Nt=(e,t,r,n)=>{const s=Vs(e,t);let o=0,a,i=u=>{a||(a=!0,n&&n(u))};return new ReadableStream({async pull(u){try{const{done:c,value:l}=await s.next();if(c){i(),u.close();return}let h=l.byteLength;if(r){let w=o+=h;r(w)}u.enqueue(new Uint8Array(l))}catch(c){throw i(c),c}},cancel(u){return i(u),s.return()}},{highWaterMark:2})},Ve=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",br=Ve&&typeof ReadableStream=="function",Zs=Ve&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Sr=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Ks=br&&Sr(()=>{let e=!1;const t=new Request(H.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),$t=64*1024,it=br&&Sr(()=>d.isReadableStream(new Response("").body)),Ue={stream:it&&(e=>e.body)};Ve&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Ue[t]&&(Ue[t]=d.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new O(`Response type '${t}' is not supported`,O.ERR_NOT_SUPPORT,n)})})})(new Response);const Gs=async e=>{if(e==null)return 0;if(d.isBlob(e))return e.size;if(d.isSpecCompliantForm(e))return(await new Request(H.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(d.isArrayBufferView(e)||d.isArrayBuffer(e))return e.byteLength;if(d.isURLSearchParams(e)&&(e=e+""),d.isString(e))return(await Zs(e)).byteLength},Xs=async(e,t)=>{const r=d.toFiniteNumber(e.getContentLength());return r??Gs(t)},Qs=Ve&&(async e=>{let{url:t,method:r,data:n,signal:s,cancelToken:o,timeout:a,onDownloadProgress:i,onUploadProgress:u,responseType:c,headers:l,withCredentials:h="same-origin",fetchOptions:w}=vr(e);c=c?(c+"").toLowerCase():"text";let g=Hs([s,o&&o.toAbortSignal()],a),f;const m=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let p;try{if(u&&Ks&&r!=="get"&&r!=="head"&&(p=await Xs(l,n))!==0){let I=new Request(t,{method:"POST",body:n,duplex:"half"}),x;if(d.isFormData(n)&&(x=I.headers.get("content-type"))&&l.setContentType(x),I.body){const[T,M]=Dt(p,Me(Ft(u)));n=Nt(I.body,$t,T,M)}}d.isString(h)||(h=h?"include":"omit");const E="credentials"in Request.prototype;f=new Request(t,{...w,signal:g,method:r.toUpperCase(),headers:l.normalize().toJSON(),body:n,duplex:"half",credentials:E?h:void 0});let _=await fetch(f,w);const v=it&&(c==="stream"||c==="response");if(it&&(i||v&&m)){const I={};["status","statusText","headers"].forEach(re=>{I[re]=_[re]});const x=d.toFiniteNumber(_.headers.get("content-length")),[T,M]=i&&Dt(x,Me(Ft(i),!0))||[];_=new Response(Nt(_.body,$t,T,()=>{M&&M(),m&&m()}),I)}c=c||"text";let P=await Ue[d.findKey(Ue,c)||"text"](_,e);return!v&&m&&m(),await new Promise((I,x)=>{wr(I,x,{data:P,headers:K.from(_.headers),status:_.status,statusText:_.statusText,config:e,request:f})})}catch(E){throw m&&m(),E&&E.name==="TypeError"&&/Load failed|fetch/i.test(E.message)?Object.assign(new O("Network Error",O.ERR_NETWORK,e,f),{cause:E.cause||E}):O.from(E,E&&E.code,e,f)}}),ct={http:ms,xhr:zs,fetch:Qs};d.forEach(ct,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Lt=e=>`- ${e}`,Ys=e=>d.isFunction(e)||e===null||e===!1,kr={getAdapter:e=>{e=d.isArray(e)?e:[e];const{length:t}=e;let r,n;const s={};for(let o=0;o<t;o++){r=e[o];let a;if(n=r,!Ys(r)&&(n=ct[(a=String(r)).toLowerCase()],n===void 0))throw new O(`Unknown adapter '${a}'`);if(n)break;s[a||"#"+o]=n}if(!n){const o=Object.entries(s).map(([i,u])=>`adapter ${i} `+(u===!1?"is not supported by the environment":"is not available in the build"));let a=t?o.length>1?`since :
`+o.map(Lt).join(`
`):" "+Lt(o[0]):"as no adapter specified";throw new O("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return n},adapters:ct};function Qe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new pe(null,e)}function Mt(e){return Qe(e),e.headers=K.from(e.headers),e.data=Xe.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),kr.getAdapter(e.adapter||Te.adapter)(e).then(function(n){return Qe(e),n.data=Xe.call(e,e.transformResponse,n),n.headers=K.from(n.headers),n},function(n){return yr(n)||(Qe(e),n&&n.response&&(n.response.data=Xe.call(e,e.transformResponse,n.response),n.response.headers=K.from(n.response.headers))),Promise.reject(n)})}const xr="1.11.0",Je={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Je[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Ut={};Je.transitional=function(t,r,n){function s(o,a){return"[Axios v"+xr+"] Transitional option '"+o+"'"+a+(n?". "+n:"")}return(o,a,i)=>{if(t===!1)throw new O(s(a," has been removed"+(r?" in "+r:"")),O.ERR_DEPRECATED);return r&&!Ut[a]&&(Ut[a]=!0,console.warn(s(a," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,a,i):!0}};Je.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function eo(e,t,r){if(typeof e!="object")throw new O("options must be an object",O.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let s=n.length;for(;s-- >0;){const o=n[s],a=t[o];if(a){const i=e[o],u=i===void 0||a(i,o,e);if(u!==!0)throw new O("option "+o+" must be "+u,O.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new O("Unknown option "+o,O.ERR_BAD_OPTION)}}const Ne={assertOptions:eo,validators:Je},Q=Ne.validators;let ue=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Ct,response:new Ct}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=de(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:o}=r;n!==void 0&&Ne.assertOptions(n,{silentJSONParsing:Q.transitional(Q.boolean),forcedJSONParsing:Q.transitional(Q.boolean),clarifyTimeoutError:Q.transitional(Q.boolean)},!1),s!=null&&(d.isFunction(s)?r.paramsSerializer={serialize:s}:Ne.assertOptions(s,{encode:Q.function,serialize:Q.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Ne.assertOptions(r,{baseUrl:Q.spelling("baseURL"),withXsrfToken:Q.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let a=o&&d.merge(o.common,o[r.method]);o&&d.forEach(["delete","get","head","post","put","patch","common"],f=>{delete o[f]}),r.headers=K.concat(a,o);const i=[];let u=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(r)===!1||(u=u&&m.synchronous,i.unshift(m.fulfilled,m.rejected))});const c=[];this.interceptors.response.forEach(function(m){c.push(m.fulfilled,m.rejected)});let l,h=0,w;if(!u){const f=[Mt.bind(this),void 0];for(f.unshift(...i),f.push(...c),w=f.length,l=Promise.resolve(r);h<w;)l=l.then(f[h++],f[h++]);return l}w=i.length;let g=r;for(h=0;h<w;){const f=i[h++],m=i[h++];try{g=f(g)}catch(p){m.call(this,p);break}}try{l=Mt.call(this,g)}catch(f){return Promise.reject(f)}for(h=0,w=c.length;h<w;)l=l.then(c[h++],c[h++]);return l}getUri(t){t=de(this.defaults,t);const r=_r(t.baseURL,t.url,t.allowAbsoluteUrls);return hr(r,t.params,t.paramsSerializer)}};d.forEach(["delete","get","head","options"],function(t){ue.prototype[t]=function(r,n){return this.request(de(n||{},{method:t,url:r,data:(n||{}).data}))}});d.forEach(["post","put","patch"],function(t){function r(n){return function(o,a,i){return this.request(de(i||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:a}))}}ue.prototype[t]=r(),ue.prototype[t+"Form"]=r(!0)});let to=class Er{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(s=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](s);n._listeners=null}),this.promise.then=s=>{let o;const a=new Promise(i=>{n.subscribe(i),o=i}).then(s);return a.cancel=function(){n.unsubscribe(o)},a},t(function(o,a,i){n.reason||(n.reason=new pe(o,a,i),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Er(function(s){t=s}),cancel:t}}};function ro(e){return function(r){return e.apply(null,r)}}function no(e){return d.isObject(e)&&e.isAxiosError===!0}const lt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(lt).forEach(([e,t])=>{lt[t]=e});function Tr(e){const t=new ue(e),r=rr(ue.prototype.request,t);return d.extend(r,ue.prototype,t,{allOwnKeys:!0}),d.extend(r,t,null,{allOwnKeys:!0}),r.create=function(s){return Tr(de(e,s))},r}const j=Tr(Te);j.Axios=ue;j.CanceledError=pe;j.CancelToken=to;j.isCancel=yr;j.VERSION=xr;j.toFormData=We;j.AxiosError=O;j.Cancel=j.CanceledError;j.all=function(t){return Promise.all(t)};j.spread=ro;j.isAxiosError=no;j.mergeConfig=de;j.AxiosHeaders=K;j.formToJSON=e=>gr(d.isHTMLForm(e)?new FormData(e):e);j.getAdapter=kr.getAdapter;j.HttpStatusCode=lt;j.default=j;const{Axios:Ia,AxiosError:Na,CanceledError:$a,isCancel:La,CancelToken:Ma,VERSION:Ua,all:ja,Cancel:Ba,isAxiosError:qa,spread:za,toFormData:Ha,AxiosHeaders:Wa,HttpStatusCode:Va,formToJSON:Ja,getAdapter:Za,mergeConfig:Ka}=j,te=[{ts_code:"000001.SZ",symbol:"000001",name:"平安银行",area:"深圳",industry:"银行",market:"main",list_date:"19910403",exchange:"SZSE",is_hs:"N"},{ts_code:"000002.SZ",symbol:"000002",name:"万科A",area:"深圳",industry:"房地产开发",market:"main",list_date:"19910129",exchange:"SZSE",is_hs:"S"},{ts_code:"600000.SH",symbol:"600000",name:"浦发银行",area:"上海",industry:"银行",market:"main",list_date:"19991110",exchange:"SSE",is_hs:"H"},{ts_code:"600519.SH",symbol:"600519",name:"贵州茅台",area:"贵州",industry:"白酒",market:"main",list_date:"20010827",exchange:"SSE",is_hs:"H"},{ts_code:"000858.SZ",symbol:"000858",name:"五粮液",area:"四川",industry:"白酒",market:"main",list_date:"19980427",exchange:"SZSE",is_hs:"S"}],jt=e=>te.filter(t=>t.name.includes(e)||t.symbol.includes(e)||t.ts_code.includes(e)),we=(e=30,t=100)=>{const r=[];let n=t;const s=new Date;s.setDate(s.getDate()-e);for(let o=0;o<e;o++){const a=new Date(s);a.setDate(a.getDate()+o);const i=n,u=(Math.random()-.5)*6,c=i+Math.random()*3,l=i-Math.random()*3,h=i+u,w=Math.floor(Math.random()*1e6)+1e5;n=h,r.push({date:a.toISOString().split("T")[0],open:+i.toFixed(2),high:+Math.max(c,i,h).toFixed(2),low:+Math.min(l,i,h).toFixed(2),close:+h.toFixed(2),volume:w,amount:+(w*h).toFixed(2)})}return r},ut={"000001.SZ":we(60,12.5),"000002.SZ":we(60,23.8),"600000.SH":we(60,10.2),"600519.SH":we(60,1650),"000858.SZ":we(60,185.5)},Ye={"000001.SZ":{code:"000001.SZ",name:"平安银行",price:12.56,change:.08,pct_change:.64,volume:1542e4,amount:19345e4,turnover_rate:.83,pe_ratio:5.2,pb_ratio:.65,market_cap:24356e7,timestamp:new Date().toISOString()},"600519.SH":{code:"600519.SH",name:"贵州茅台",price:1658.5,change:-15.6,pct_change:-.93,volume:892e3,amount:147854e4,turnover_rate:.07,pe_ratio:28.5,pb_ratio:10.2,market_cap:208245e7,timestamp:new Date().toISOString()}},so=e=>{const t={ma5:[],ma10:[],ma20:[],macd:[],kdj:[],rsi:[]};return e.forEach((r,n)=>{if(n>=4){const h=e.slice(n-4,n+1).reduce((w,g)=>w+g.close,0)/5;t.ma5.push({date:r.date,value:+h.toFixed(2)})}if(n>=9){const h=e.slice(n-9,n+1).reduce((w,g)=>w+g.close,0)/10;t.ma10.push({date:r.date,value:+h.toFixed(2)})}if(n>=19){const h=e.slice(n-19,n+1).reduce((w,g)=>w+g.close,0)/20;t.ma20.push({date:r.date,value:+h.toFixed(2)})}const s=Math.sin(n*.1)*2,o=Math.sin(n*.08)*1.5,a=(s-o)*2;t.macd.push({date:r.date,dif:+s.toFixed(3),dea:+o.toFixed(3),histogram:+a.toFixed(3)});const i=50+Math.sin(n*.15)*30,u=50+Math.sin(n*.12)*25,c=3*i-2*u;t.kdj.push({date:r.date,k:+Math.max(0,Math.min(100,i)).toFixed(2),d:+Math.max(0,Math.min(100,u)).toFixed(2),j:+Math.max(0,Math.min(100,c)).toFixed(2)});const l=50+Math.sin(n*.08)*20;t.rsi.push({date:r.date,value:+Math.max(0,Math.min(100,l)).toFixed(2)})}),t},Ar={};Object.keys(ut).forEach(e=>{Ar[e]=so(ut[e])});const et={admin:{id:1,username:"admin",email:"<EMAIL>",role:"admin",is_active:!0,created_at:"2024-01-01T00:00:00Z",profile:{nickname:"管理员",avatar:"https://avatars.dicebear.com/api/avataaars/admin.svg"}},user:{id:2,username:"user",email:"<EMAIL>",role:"user",is_active:!0,created_at:"2024-01-15T00:00:00Z",profile:{nickname:"普通用户",avatar:"https://avatars.dicebear.com/api/avataaars/user.svg"}},demo:{id:3,username:"demo",email:"<EMAIL>",role:"user",is_active:!0,created_at:"2024-02-01T00:00:00Z",profile:{nickname:"演示用户",avatar:"https://avatars.dicebear.com/api/avataaars/demo.svg"}}},Bt={admin:"admin123",user:"user123",demo:"demo123"},qt=e=>{const t={user_id:e.id,username:e.username,role:e.role,exp:Math.floor(Date.now()/1e3)+86400,iat:Math.floor(Date.now()/1e3)};return"mock-jwt-"+btoa(JSON.stringify(t))},oo=e=>{if(!e||!e.startsWith("mock-jwt-"))return null;try{const t=JSON.parse(atob(e.substring(9)));return t.exp<Math.floor(Date.now()/1e3)?null:t}catch{return null}},tt={1:[{stock_code:"600519.SH",added_at:"2024-01-10T10:00:00Z"},{stock_code:"000858.SZ",added_at:"2024-01-15T14:30:00Z"},{stock_code:"000001.SZ",added_at:"2024-02-01T09:15:00Z"}],2:[{stock_code:"000002.SZ",added_at:"2024-01-20T11:20:00Z"},{stock_code:"600000.SH",added_at:"2024-01-25T16:45:00Z"}],3:[{stock_code:"600519.SH",added_at:"2024-02-05T10:30:00Z"}]},fe=(e,t)=>e.sort(()=>Math.random()-.5).slice(0,Math.floor(Math.random()*5)+3).map(n=>({stock_code:n.ts_code,stock_name:n.name,signal_score:+(Math.random()*40+60).toFixed(2),signal_strength:Math.random()>.7?"strong":Math.random()>.4?"medium":"weak",entry_price:+(Math.random()*50+10).toFixed(2),target_price:+(Math.random()*20+5).toFixed(2),stop_loss:+(Math.random()*5+2).toFixed(2),volume_ratio:+(Math.random()*3+.5).toFixed(2),indicators:{rsi:+(Math.random()*30+40).toFixed(2),macd_signal:Math.random()>.5?"bullish":"bearish",ma_position:Math.random()>.6?"above":"below",volume_surge:Math.random()>.7},scan_time:new Date().toISOString(),strategy_used:t})),zt={strong_breakthrough:fe(te,"strong_breakthrough"),volume_surge:fe(te,"volume_surge"),ma_golden_cross:fe(te,"ma_golden_cross"),rsi_oversold:fe(te,"rsi_oversold"),macd_bullish:fe(te,"macd_bullish"),custom:fe(te,"custom")},ao=[{id:1,strategy:"strong_breakthrough",scan_time:"2024-08-15T09:30:00Z",result_count:6,avg_score:78.5,status:"completed"},{id:2,strategy:"volume_surge",scan_time:"2024-08-15T14:00:00Z",result_count:4,avg_score:82.3,status:"completed"},{id:3,strategy:"ma_golden_cross",scan_time:"2024-08-14T10:15:00Z",result_count:8,avg_score:71.2,status:"completed"},{id:4,strategy:"custom",scan_time:"2024-08-14T15:45:00Z",result_count:0,avg_score:0,status:"failed",error:"参数配置错误"}],rt={strong_breakthrough:{volume_ratio_min:2,price_change_min:5,rsi_max:70,timeframe:"1d",lookback_days:5},volume_surge:{volume_ratio_min:3,avg_volume_days:20,price_change_min:2,timeframe:"1d"},ma_golden_cross:{ma_short:5,ma_long:20,cross_days:3,volume_confirm:!0},rsi_oversold:{rsi_max:30,rsi_days:14,recovery_min:5,volume_ratio_min:1.5},macd_bullish:{macd_fast:12,macd_slow:26,macd_signal:9,histogram_positive:!0,volume_confirm:!0}},ee=[{id:1,name:"每日强势突破扫描",strategy:"strong_breakthrough",cron_expression:"0 30 9 * * MON-FRI",enabled:!0,last_run:"2024-08-15T09:30:00Z",next_run:"2024-08-16T09:30:00Z",status:"active",created_at:"2024-08-01T10:00:00Z"},{id:2,name:"午盘量价齐升检查",strategy:"volume_surge",cron_expression:"0 0 14 * * MON-FRI",enabled:!0,last_run:"2024-08-15T14:00:00Z",next_run:"2024-08-16T14:00:00Z",status:"active",created_at:"2024-08-05T15:30:00Z"},{id:3,name:"周末技术面综合扫描",strategy:"custom",cron_expression:"0 0 10 * * SAT",enabled:!1,last_run:"2024-08-10T10:00:00Z",next_run:"2024-08-17T10:00:00Z",status:"paused",created_at:"2024-07-20T12:00:00Z"}],Ht=(e=100+Math.random()*300)=>new Promise(t=>setTimeout(t,e)),$=(e,t="success")=>({data:e,status:"success",message:t});class io{constructor(){this.baseURL="/api/v1",this.currentUser=null,this.init()}init(){const t=localStorage.getItem("access_token");if(t){const r=oo(t);r&&(this.currentUser=et[r.username])}}async request(t,r,n=null,s={}){await Ht(),console.log(`[Mock API] ${t.toUpperCase()} ${r}`,n);try{return await this.routeRequest(t.toLowerCase(),r,n,s)}catch(o){throw console.error(`[Mock API Error] ${t.toUpperCase()} ${r}:`,o),o}}async routeRequest(t,r,n,s){const a=r.replace(this.baseURL,"").replace(/^\//,"").split("/");if(a[0]==="auth")return this.handleAuth(t,a.slice(1),n);if(a[0]==="stocks")return this.handleStocks(t,a.slice(1),n,s);if(a[0]==="indicators")return this.handleIndicators(t,a.slice(1),n,s);if(a[0]==="analytics")return this.handleAnalytics(t,a.slice(1),n,s);if(a[0]==="scanner"||a[0]==="scan")return this.handleScanner(t,a.slice(1),n,s);if(a[0]==="watchlist")return this.handleWatchlist(t,a.slice(1),n,s);if(a[0]==="scheduled-tasks")return this.handleScheduledTasks(t,a.slice(1),n,s);if(a[0]==="users")return this.handleUsers(t,a.slice(1),n,s);throw new Error(`API endpoint not found: ${r}`)}async handleAuth(t,r,n){const s=r[0];if(s==="login"&&t==="post"){const{username:o,password:a}=n;if(!o||!a)throw new Error("用户名和密码不能为空");if(!Bt[o]||Bt[o]!==a)throw new Error("用户名或密码错误");const i=et[o];if(!i.is_active)throw new Error("用户账户已被禁用");const u=qt(i);return localStorage.setItem("access_token",u),this.currentUser=i,$({access_token:u,token_type:"bearer",user:i},"登录成功")}if(s==="logout"&&t==="post")return localStorage.removeItem("access_token"),this.currentUser=null,$(null,"退出成功");if(s==="refresh"&&t==="post"){if(!this.currentUser)throw new Error("未登录");const o=qt(this.currentUser);return localStorage.setItem("access_token",o),$({access_token:o,token_type:"bearer"},"Token刷新成功")}if(s==="me"&&t==="get"){if(!this.currentUser)throw new Error("未登录");return $(this.currentUser)}throw new Error(`Auth action not supported: ${s}`)}async handleStocks(t,r,n,s){var o,a,i,u;if(r[0]==="list"&&t==="get"){const c=((o=s.params)==null?void 0:o.search)||((a=s.params)==null?void 0:a.keyword);return $(c?jt(c):te)}if(r[0]==="search"&&t==="get"){const c=((i=s.params)==null?void 0:i.q)||((u=s.params)==null?void 0:u.keyword);return $(c?jt(c):[])}if(r[0]==="realtime"&&t==="get"){const c=r[1];if(!c)throw new Error("股票代码不能为空");const l=Ye[c];if(!l)throw new Error("未找到该股票的实时数据");return $(l)}if(r[0]==="realtime"&&r[1]==="batch"&&t==="post"){const{codes:c}=n;if(!c||!Array.isArray(c))throw new Error("股票代码列表格式错误");const l=c.map(h=>Ye[h]).filter(Boolean);return $(l)}throw new Error(`Stock API not supported: ${r.join("/")}`)}async handleIndicators(t,r,n,s){const o=r[0];if(!o)throw new Error("股票代码不能为空");const a=Ar[o];if(!a)throw new Error("未找到该股票的指标数据");const i=r[1]||"all";if(i==="all")return $(a);if(a[i])return $(a[i]);throw new Error(`指标类型不支持: ${i}`)}async handleAnalytics(t,r,n,s){if(r[0]==="kline"&&t==="get"){const o=r[1];if(!o)throw new Error("股票代码不能为空");const a=ut[o];if(!a)throw new Error("未找到该股票的K线数据");let i=a;const{start_date:u,end_date:c,limit:l}=s.params||{};return u&&(i=i.filter(h=>h.date>=u)),c&&(i=i.filter(h=>h.date<=c)),l&&(i=i.slice(-parseInt(l))),$(i)}throw new Error(`Analytics API not supported: ${r.join("/")}`)}async handleScanner(t,r,n,s){var o;if(r[0]==="run"&&t==="post"){const{strategy:a,params:i}=n;if(!a)throw new Error("扫描策略不能为空");await Ht(1e3+Math.random()*2e3);const u=zt[a]||zt.custom;return $({strategy:a,results:u,scan_time:new Date().toISOString(),total_count:u.length,execution_time:Math.random()*3+.5},"扫描完成")}if(r[0]==="history"&&t==="get")return $(ao);if(r[0]==="params"&&t==="get"){const a=(o=s.params)==null?void 0:o.strategy;return a&&rt[a]?$(rt[a]):$(rt)}throw new Error(`Scanner API not supported: ${r.join("/")}`)}async handleWatchlist(t,r,n,s){if(!this.currentUser)throw new Error("请先登录");const o=this.currentUser.id;let a=tt[o]||[];if(t==="get"){const i=a.map(u=>{const c=te.find(h=>h.ts_code===u.stock_code),l=Ye[u.stock_code];return{...u,stock_info:c,realtime_data:l}});return $(i)}if(t==="post"){const{stock_code:i}=n;if(!i)throw new Error("股票代码不能为空");if(a.find(l=>l.stock_code===i))throw new Error("该股票已在自选股中");const c={stock_code:i,added_at:new Date().toISOString()};return a.push(c),tt[o]=a,$(c,"添加成功")}if(t==="delete"){const i=r[0];if(!i)throw new Error("股票代码不能为空");const u=a.findIndex(c=>c.stock_code===i);if(u===-1)throw new Error("该股票不在自选股中");return a.splice(u,1),tt[o]=a,$(null,"删除成功")}throw new Error(`Watchlist API not supported: ${t} ${r.join("/")}`)}async handleScheduledTasks(t,r,n,s){if(!this.currentUser)throw new Error("请先登录");if(t==="get")return $(ee);if(t==="post"){const o={id:ee.length+1,...n,status:"active",created_at:new Date().toISOString(),last_run:null,next_run:null};return ee.push(o),$(o,"任务创建成功")}if(r[0]&&t==="put"){const o=parseInt(r[0]),a=ee.findIndex(i=>i.id===o);if(a===-1)throw new Error("任务不存在");return ee[a]={...ee[a],...n,updated_at:new Date().toISOString()},$(ee[a],"任务更新成功")}if(r[0]&&t==="delete"){const o=parseInt(r[0]),a=ee.findIndex(i=>i.id===o);if(a===-1)throw new Error("任务不存在");return ee.splice(a,1),$(null,"任务删除成功")}throw new Error(`Scheduled tasks API not supported: ${t} ${r.join("/")}`)}async handleUsers(t,r,n,s){if(!this.currentUser||this.currentUser.role!=="admin")throw new Error("权限不足");if(t==="get")return $(Object.values(et));throw new Error(`Users API not supported: ${t} ${r.join("/")}`)}async get(t,r={}){return this.request("get",t,null,r)}async post(t,r,n={}){return this.request("post",t,r,n)}async put(t,r,n={}){return this.request("put",t,r,n)}async delete(t,r={}){return this.request("delete",t,null,r)}}const co=new io,Rr=!0;console.log("[API Client] 原型模式:",Rr);j.create({baseURL:"/api/v1",timeout:3e4,headers:{"Content-Type":"application/json"},withCredentials:!0});class lo{constructor(){this.client=co,this.isPrototype=Rr}async get(t,r={}){try{const n=await this.client.get(t,r);return this.isPrototype,n}catch(n){throw this.handleError(n),n}}async post(t,r,n={}){try{const s=await this.client.post(t,r,n);return this.isPrototype,s}catch(s){throw this.handleError(s),s}}async put(t,r,n={}){try{const s=await this.client.put(t,r,n);return this.isPrototype,s}catch(s){throw this.handleError(s),s}}async delete(t,r={}){try{const n=await this.client.delete(t,r);return this.isPrototype,n}catch(n){throw this.handleError(n),n}}handleError(t){this.isPrototype?(console.error("[Mock API Error]:",t.message),De.error(t.message)):console.error("[API Error]:",t)}async request(t){const{method:r,url:n,data:s,...o}=t;switch(r==null?void 0:r.toLowerCase()){case"get":return this.get(n,o);case"post":return this.post(n,s,o);case"put":return this.put(n,s,o);case"delete":return this.delete(n,o);default:throw new Error(`不支持的请求方法: ${r}`)}}}const S=new lo,uo={getStockList(e={}){return S.get("/stocks/list/",{params:e})},getStockInfo(e){return S.get(`/stocks/list/${e}`)},searchStocks(e,t=20){return S.get("/stocks/list/",{params:{search:e,limit:t}})},enrichStockData(){return S.post("/stocks/list/enrich")},refreshStockList(e={}){return S.post("/stocks/list/refresh",{data_source:e.data_source||null})}},fo={getKlineData(e,t="D",r={}){return S.get(`/analytics/kline/${e}`,{params:{freq:t,...r}})},getHistoricalData(e,t,r,n){return S.get(`/stocks/kline/${e}/${t}/${r}/${n}`)},getRealtimeData(e){return S.get(`/stocks/realtime/${e}`)},getBatchRealtimeData(e){return S.post("/stocks/realtime/batch",{codes:e})}},mo={searchStocks(e,t={}){const r={q:e,limit:t.limit||20,...t};return S.get("/stocks/search/search",{params:r})},quickSearchStocks(e,t=10){return S.get("/stocks/search/quick-search",{params:{q:e,limit:t}})},getPopularStocks(e={}){const t={limit:e.limit||10,...e};return S.get("/stocks/search/popular",{params:t})}},ho={getWatchlist(e=!0){return S.get("/watchlist/",{params:{include_details:e}})},addToWatchlist(e){return S.post("/watchlist/",{stock_code:e})},removeFromWatchlist(e){return S.delete(`/watchlist/${e}`)},bulkUpdateWatchlist(e){return S.put("/watchlist/",{stock_codes:e})}},po={getIndicator(e,t,r={}){return S.get(`/indicators/${t}/${e}`,{params:r})},getAdvancedIndicator(e,t,r={}){return S.get(`/indicators/advanced/${t}/${e}`,{params:r})},getBatchIndicator(e,t,r={}){return S.post(`/indicators/${t}/batch`,{stock_codes:e,...r})}},ae={startScan(e){return S({url:"scan/start",method:"post",data:e})},stopScan(e){return S({url:`scan/${e}/stop`,method:"post"})},getProgress(e){return S({url:`scan/${e}/progress`,method:"get"})},getResults(e,t=1,r=20){return S({url:`scan/${e}/results`,method:"get",params:{page:t,page_size:r}})},getTask(e){return S({url:`scan/${e}`,method:"get"})},getActiveTasks(){return S({url:"scan/active",method:"get"})}},go={getKlineData(e,t="D",r={}){return S.get(`/analytics/index-kline/${e}`,{params:{freq:t,...r}})},getHistoricalData(e,t,r,n){return S.get(`/analytics/index-kline/${e}`,{params:{freq:t,start_date:r,end_date:n}})},getRealTimeData(e){return S.get(`/indices/${e}`,{params:{include_latest_data:!0}})},getBatchIndexInfo(e){const t=e.map(r=>S.get(`/indices/${r}`,{params:{include_latest_data:!0}}));return Promise.all(t)}},yo={getIndexList(e={}){return S.get("/indices/",{params:e})},getIndexInfo(e){return S.get(`/indices/${e}`)},searchIndices(e,t=20){return S.get("/indices/",{params:{search:e,limit:t}})},refreshIndexList(e={}){return S.post("/indices/refresh",{data_source:e.data_source||null})}},wo={getIndicator(e,t,r={}){return S.get(`/index-indicators/${t}/${e}`,{params:r})},getMacd(e,t={}){return S.get(`/index-indicators/macd/${e}`,{params:t})},getKdj(e,t={}){return S.get(`/index-indicators/kdj/${e}`,{params:t})},getRsi(e,t={}){return S.get(`/index-indicators/rsi/${e}`,{params:t})},getVolumeAnalysis(e,t={}){return S.get(`/index-indicators/volume/${e}`,{params:t})},getBatchIndicators(e,t,r={}){const n=t.map(s=>this.getIndicator(e,s,r).catch(o=>(console.warn(`获取指数指标 ${s} 失败:`,o),null)));return Promise.all(n)}},_o={login:e=>S.post("/auth/login",e),refreshToken:e=>S.post("/auth/refresh",{token:e}),getCurrentUser:()=>S.get("/auth/me"),logout:()=>S.post("/auth/logout"),getUserList:(e=1,t=20)=>S.get("/users/list",{params:{page:e,page_size:t}}),createUser:e=>S.post("/users/",e),updateUser:(e,t)=>S.put(`/users/${e}`,t),toggleUserStatus:(e,t)=>S.patch(`/users/${e}/status`,{is_active:t}),deleteUser:e=>S.delete(`/users/${e}`),resetPassword:(e,t)=>S.patch(`/users/${e}/password`,{password:t})},ne={stockList:uo,stockData:fo,stockSearch:mo,watchlist:ho,indicator:po,scanner:ae,indexData:go,indexList:yo,indexIndicator:wo,auth:_o},vo=Se("stockData",()=>{const e=F([]),t=F(null),r=F([]),n=F({}),s=F([]),o=F([]),a=F(!1),i=F(null),u=F(new Map),c=F(!1),l=U(()=>e.value.length),h=U(()=>{var b;return((b=t.value)==null?void 0:b.code)||null}),w=U({get(){return o.value.length>0?o.value:e.value.filter(b=>s.value.includes(b.code))},set(b){o.value=b}}),g=async(b={})=>{a.value=!0,i.value=null;try{const A=await ne.stockList.getStockList(b);return console.log("股票列表",A.data),e.value=A.data.stocks||[],A}catch(A){throw i.value=A.message||"获取股票列表失败",A}finally{a.value=!1}},f=async b=>{a.value=!0,i.value=null;try{const A=await ne.stockList.getStockInfo(b);return t.value=A.data,A}catch(A){throw i.value=A.message||"获取股票详情失败",A}finally{a.value=!1}},m=async(b,A="1d",y=200)=>{a.value=!0,i.value=null;try{const R=await ne.stockData.getKlineData(b,A,{limit:y});return r.value=R.data,R}catch(R){throw i.value=R.message||"获取K线数据失败",R}finally{a.value=!1}},p=async(b,A=[])=>{a.value=!0,i.value=null;try{const y=A.map(W=>ne.indicator.getIndicator(b,W)),R=await Promise.all(y);return n.value={...n.value,[b]:R.map((W,Pr)=>({type:A[Pr],data:W.data}))},R}catch(y){throw i.value=y.message||"获取技术指标失败",y}finally{a.value=!1}},E=async b=>{try{await ne.watchlist.addToWatchlist(b),s.value.includes(b)||(s.value.push(b),P()),await I()}catch(A){throw i.value=A.message||"添加自选股失败",A}},_=async b=>{try{await ne.watchlist.removeFromWatchlist(b);const A=s.value.indexOf(b);A>-1&&(s.value.splice(A,1),P());const y=o.value.findIndex(R=>R.code===b);y>-1&&o.value.splice(y,1)}catch(A){throw i.value=A.message||"移除自选股失败",A}},v=async b=>{s.value.includes(b)?await _(b):await E(b)},P=()=>{localStorage.setItem("watchlist",JSON.stringify(s.value))},I=async()=>{try{const b=await ne.watchlist.getWatchlist();if(b.data&&b.success){const A=b.data||[];s.value=A.map(y=>y.stock_code),console.log("获取自选股列表:",A),o.value=A.map(y=>{const R=y.stock_info;return R?{code:R.code,name:R.name,exchange:R.exchange,industry:R.industry||"未分类",price:R.price||0,changePercent:R.change_percent||0,volume:R.volume||0,addedAt:y.added_at,sortOrder:y.sort_order}:{code:y.stock_code,name:y.stock_code,exchange:"unknown",industry:"未分类",price:0,changePercent:0,volume:0,addedAt:y.added_at,sortOrder:y.sort_order}})}else b.data&&b.data.stocks?s.value=b.data.stocks.map(A=>A.code):b.data&&Array.isArray(b.data)&&(s.value=b.data);P()}catch(b){console.warn("从服务器获取自选股失败，使用本地数据:",b.message);const A=localStorage.getItem("watchlist");if(A)try{s.value=JSON.parse(A)}catch(y){console.error("解析收藏列表失败:",y),s.value=[]}}};return{stocks:e,currentStock:t,klineData:r,indicators:n,watchlist:s,watchlistStocksData:o,loading:a,error:i,realtimeData:u,isRealtimeConnected:c,stockCount:l,currentStockCode:h,watchlistStocks:w,fetchStocks:g,fetchStockDetail:f,fetchKlineData:m,fetchIndicators:p,addToWatchlist:E,removeFromWatchlist:_,toggleWatchlist:v,loadWatchlist:I,updateRealtimeData:(b,A)=>{u.value.set(b,{...A,timestamp:Date.now()})},getRealtimeData:b=>u.value.get(b)||null,setRealtimeConnection:b=>{c.value=b},clearData:()=>{e.value=[],t.value=null,r.value=[],n.value={},i.value=null},setCurrentStock:b=>{t.value=b},updateStock:(b,A)=>{const y=e.value.findIndex(R=>R.code===b);y>-1&&(e.value[y]={...e.value[y],...A})}}}),Ae=Se("ui",()=>{const e=F(!1),t=F(!1),r=F("dashboard"),n=F([]),s=F({stockDetail:!1,settings:!1,addStock:!1}),o=F([]),a=()=>{e.value=!e.value},i=v=>{e.value=v},u=v=>{t.value=v},c=v=>{r.value=v},l=v=>{s.value.hasOwnProperty(v)&&(s.value[v]=!0)},h=v=>{s.value.hasOwnProperty(v)&&(s.value[v]=!1)},w=()=>{Object.keys(s.value).forEach(v=>{s.value[v]=!1})},g=v=>{const P=Date.now().toString();o.value.push({id:P,type:"info",duration:3e3,...v}),v.duration!==0&&setTimeout(()=>{f(P)},v.duration||3e3)},f=v=>{const P=o.value.findIndex(I=>I.id===v);P>-1&&o.value.splice(P,1)};return{sidebarCollapsed:e,loading:t,activeTab:r,selectedStocks:n,modals:s,notifications:o,toggleSidebar:a,setSidebarCollapsed:i,setLoading:u,setActiveTab:c,openModal:l,closeModal:h,closeAllModals:w,addNotification:g,removeNotification:f,clearNotifications:()=>{o.value=[]},toggleStockSelection:v=>{const P=n.value.indexOf(v);P>-1?n.value.splice(P,1):n.value.push(v)},clearStockSelection:()=>{n.value=[]},selectAllStocks:v=>{n.value=[...v]}}}),bo=Se("scanner",()=>{const e=F(null),t=F([]),r=F(0),n=F(!1);let s=null;const o=U(()=>{var m,p;return((m=e.value)==null?void 0:m.status)==="pending"||((p=e.value)==null?void 0:p.status)==="running"}),a=async()=>{var m,p,E,_;try{const v=await ae.getActiveTasks();if(v&&v.length>0){const P=v[0];return e.value={id:P.task_id,status:P.status,created_at:P.created_at,progress:{current:((m=P.progress)==null?void 0:m.current)||0,total:((p=P.progress)==null?void 0:p.total)||0,percentage:((E=P.progress)==null?void 0:E.percentage)||0,message:(_=P.progress)==null?void 0:_.message}},["pending","running"].includes(P.status)&&h(),P.status==="completed"&&await l(),!0}return!1}catch(v){return console.error("Failed to init session:",v),!1}},i=async m=>{try{w();const p=await ae.startScan(m);return e.value={id:p.task_id,status:p.status,created_at:p.created_at,progress:{current:0,total:0,percentage:0,message:"准备中..."}},t.value=[],r.value=0,console.log(`Starting new scan task: ${p.task_id}`),h(),p}catch(p){throw console.error("Failed to start scan:",p),p}},u=async()=>{var m;try{if(!((m=e.value)!=null&&m.id))throw new Error("没有正在运行的扫描任务");await ae.stopScan(e.value.id),w(),e.value&&(e.value.status="cancelled",e.value.progress.message="已取消")}catch(p){throw console.error("Failed to stop scan:",p),p}},c=async()=>{var m,p,E,_,v,P,I;try{if(!((m=e.value)!=null&&m.id))return;const x=await ae.getProgress(e.value.id);e.value={...e.value,status:x.status,progress:{current:x.current,total:x.total,percentage:x.percentage,message:x.message}},["completed","failed","cancelled"].includes(x.status)&&(w(),x.status==="completed"&&await l())}catch(x){if(console.error("Failed to fetch progress:",x),((p=x.response)==null?void 0:p.status)===404){console.log("Current task not found, checking for new active tasks..."),w();try{const T=await ae.getActiveTasks();if(T&&T.length>0){const M=T[0];M.task_id!==((E=e.value)==null?void 0:E.id)&&(console.log(`Switching to new active task: ${M.task_id}`),e.value={id:M.task_id,status:M.status,created_at:M.created_at,progress:{current:((_=M.progress)==null?void 0:_.current)||0,total:((v=M.progress)==null?void 0:v.total)||0,percentage:((P=M.progress)==null?void 0:P.percentage)||0,message:(I=M.progress)==null?void 0:I.message}},["pending","running"].includes(M.status)?h():M.status==="completed"&&await l())}else e.value=null}catch(T){console.error("Failed to get active tasks:",T),e.value&&(e.value.status="failed",e.value.progress.message="任务不存在")}}}},l=async(m=1,p=20)=>{var E;try{if(!((E=e.value)!=null&&E.id))return;n.value=!0;const _=await ae.getResults(e.value.id,m,p);console.log(`Fetched results for task ${e.value.id}:`,_),t.value=_.results,r.value=_.total_count}catch(_){console.error("Failed to fetch results:",_),t.value=[],r.value=0}finally{n.value=!1}},h=()=>{w(),c(),s=setInterval(()=>{c()},2e3)},w=()=>{s&&(clearInterval(s),s=null)};return{currentTask:e,results:t,totalCount:r,loadingResults:n,isScanning:o,initSession:a,startScan:i,stopScan:u,fetchProgress:c,fetchResults:l,clearTask:()=>{w(),e.value=null,t.value=[],r.value=0},cleanup:()=>{w()}}}),wt=Se("auth",()=>{const e=F(null),t=F(localStorage.getItem("access_token")),r=F(!1),n=F(!1),s=U(()=>!!t.value&&!!e.value),o=U(()=>{var m;return((m=e.value)==null?void 0:m.is_admin)||!1});let a=null;async function i(m){r.value=!0;try{const p=await S.post("/auth/login",m);return t.value=p.access_token,e.value=p.user,localStorage.setItem("access_token",p.access_token),m.remember_me&&localStorage.setItem("remember_user","true"),l(p.expires_in),{success:!0}}catch(p){return{success:!1,message:p.message||"登录失败"}}finally{r.value=!1}}async function u(){try{t.value&&await S.post("/auth/logout")}catch(m){console.error("登出API调用失败:",m)}t.value=null,e.value=null,localStorage.removeItem("access_token"),localStorage.removeItem("remember_user"),a&&(clearTimeout(a),a=null)}async function c(){if(!t.value)return!1;try{const m=await S.post("/auth/refresh",{token:t.value});return t.value=m.access_token,localStorage.setItem("access_token",m.access_token),l(m.expires_in),!0}catch(m){return console.error("Token刷新失败:",m),await u(),!1}}function l(m){a&&clearTimeout(a);const p=(m-3600)*1e3;p>0&&(a=setTimeout(()=>{c()},p))}async function h(){if(t.value)try{const m=await S.get("/auth/me");e.value=m}catch(m){console.error("获取用户信息失败:",m),await u()}}async function w(){if(!t.value)return!1;try{return await h(),!0}catch(m){return console.error("Token验证失败:",m),await u(),!1}}async function g(){try{t.value&&await w()&&localStorage.getItem("remember_user")&&l(24*3600)}catch(m){console.error("认证初始化失败:",m)}finally{n.value=!0}}function f(){t.value=null,e.value=null,localStorage.removeItem("access_token"),localStorage.removeItem("remember_user"),a&&(clearTimeout(a),a=null)}return{user:e,token:t,isLoading:r,isInitialized:n,isAuthenticated:s,isAdmin:o,login:i,logout:u,refreshToken:c,fetchUser:h,validateToken:w,initialize:g,clearAuth:f}}),Or=Mr(),Wt=Object.freeze(Object.defineProperty({__proto__:null,default:Or,useAuthStore:wt,useScannerStore:bo,useStockDataStore:vo,useThemeStore:ht,useUiStore:Ae},Symbol.toStringTag,{value:"Module"})),z={__name:"Icon",props:{collection:{type:String,default:"carbon"},name:{type:String,required:!0},size:{type:[Number,String],default:null},className:{type:String,default:""}},setup(e){return(t,r)=>e.name.startsWith("i-")?(C(),L("i",bt({key:0,class:[e.name,e.className],style:{fontSize:`${e.size||24}px`}},t.$attrs),null,16)):(C(),J(D(Zr),bt({key:1,icon:`${e.collection}:${e.name}`,class:e.className,style:{fontSize:`${e.size||24}px`}},t.$attrs),null,16,["icon","class","style"]))}},Cr=[{path:"/",redirect:"/analysis",meta:{hidden:!0}},{path:"/login",name:"Login",component:()=>G(()=>import("./index-DQncb-nM.js"),__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url),meta:{title:"用户登录",requiresAuth:!1,hidden:!0,icon:"login"}},{path:"/analysis",name:"Analysis",component:()=>G(()=>import("./index-C6qRbeXw.js"),__vite__mapDeps([7,1,2,8,9,10,11,6,12]),import.meta.url),meta:{title:"股票分析",requiresAuth:!0,icon:"chart-line"}},{path:"/watchlist",name:"Watchlist",component:()=>G(()=>import("./index-IUI9w0dn.js"),__vite__mapDeps([13,1,8,14,15,2,16]),import.meta.url),meta:{title:"自选股",requiresAuth:!0,icon:"star"}},{path:"/data",name:"DataManagement",component:()=>G(()=>import("./index-lSW7P6EN.js"),__vite__mapDeps([17,1,18,19,2,20]),import.meta.url),meta:{title:"数据管理",requiresAuth:!0,icon:"data-base"}},{path:"/settings",name:"Settings",component:()=>G(()=>import("./index-CeGQYd4B.js"),__vite__mapDeps([21,1]),import.meta.url),meta:{title:"设置",requiresAuth:!0,icon:"settings"}},{path:"/scanner",name:"Scanner",component:()=>G(()=>import("./index-CzYcfKL2.js"),__vite__mapDeps([22,1,23,18,19,2,24,5,25,26,27,11,6,12]),import.meta.url),meta:{title:"技术指标",requiresAuth:!0,icon:"scan-alt"}},{path:"/scheduled-tasks",name:"ScheduledTasks",component:()=>G(()=>import("./index-oiosCFXF.js"),__vite__mapDeps([28,29,30,2,1,23,18,19,24,5,14,15,25,26,31,11,4,6,12,32]),import.meta.url),meta:{title:"定时任务",requiresAuth:!0,icon:"time"}},{path:"/task-history",name:"TaskHistory",component:()=>G(()=>import("./index-BfUCavJo.js"),__vite__mapDeps([33,23,18,1,19,2,24,5,29,30,34,4,11]),import.meta.url),meta:{title:"任务历史",requiresAuth:!0,icon:"task"}},{path:"/technical-indicators",name:"TechnicalIndicators",component:()=>G(()=>import("./index-BpoXCZp9.js"),__vite__mapDeps([35,1,14,15,2,36]),import.meta.url),meta:{title:"技术指标",requiresAuth:!0,hidden:!0,icon:"chart-line"}},{path:"/user-management",name:"UserManagement",component:()=>G(()=>import("./index-ZuRl6Ynu.js"),__vite__mapDeps([37,23,18,1,19,2,24,5,14,15,38,11,4,32,6]),import.meta.url),meta:{title:"用户管理",requiresAuth:!0,requiresAdmin:!0,icon:"user-admin"}},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>G(()=>import("./index-BbcJuyos.js"),__vite__mapDeps([39,1]),import.meta.url),meta:{title:"页面未找到",hidden:!0,icon:"error"}}],_t=Ur({history:jr(),routes:Cr,scrollBehavior(e,t,r){return r||{top:0}}});_t.beforeEach(async(e,t,r)=>{if(e.meta.title&&(document.title=`${e.meta.title} - 股票量化分析系统`),e.meta.requiresAuth){const{useAuthStore:n}=await G(async()=>{const{useAuthStore:o}=await Promise.resolve().then(()=>Wt);return{useAuthStore:o}},void 0,import.meta.url),s=n();if(s.isInitialized||await s.initialize(),!s.isAuthenticated){r({path:"/login",query:{redirect:e.fullPath}});return}if(e.meta.requiresAdmin&&!s.isAdmin){console.warn("权限不足：需要管理员权限"),r({path:"/"});return}}if(e.path==="/login"){const{useAuthStore:n}=await G(async()=>{const{useAuthStore:o}=await Promise.resolve().then(()=>Wt);return{useAuthStore:o}},void 0,import.meta.url),s=n();if(s.isInitialized||await s.initialize(),s.isAuthenticated){r({path:"/"});return}}r()});_t.afterEach((e,t)=>{console.log(`导航到: ${e.path}`)});const Re=(e,t)=>{const r=e.__vccOpts||e;for(const[n,s]of t)r[n]=s;return r},So={class:"sidebar-header h-80px px-4 flex items-center border-b border-border-color"},ko={class:"flex items-center space-x-3"},xo={class:"w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center"},Eo={key:0,class:"flex-1"},To={class:"sidebar-nav p-4"},Ao={class:"space-y-2"},Ro={key:0,class:"font-medium"},Oo={class:"sidebar-footer p-4 mt-4 border-t border-border-color"},Co={class:"space-y-2"},Po={key:0,class:"font-medium"},Do=["title"],Fo={key:0,class:"font-medium"},Io={__name:"Sidebar",setup(e){const t=ht(),r=Ae(),n=wt(),s=Kt(),o=U(()=>Cr.filter(i=>{var u,c,l;return!((u=i.meta)!=null&&u.hidden||i.path==="/settings"||i.path==="/login"||(c=i.meta)!=null&&c.requiresAdmin&&!n.isAdmin||(l=i.meta)!=null&&l.requiresAuth&&!n.isAuthenticated)}).map(i=>({name:i.name,label:i.meta&&i.meta.title?i.meta.title:"未命名",path:i.path,iconName:i.meta&&i.meta.icon?i.meta.icon:a(i.path)})));function a(i){return i==="/"?"chart-line":i==="/watchlist"?"star":i==="/data"?"data-base":"document"}return(i,u)=>{const c=ft("router-link");return C(),L("div",{class:ie(["sidebar",{collapsed:D(r).sidebarCollapsed}])},[k("div",So,[k("div",ko,[k("div",xo,[N(z,{name:"chart-line",class:"text-white text-lg"})]),D(r).sidebarCollapsed?q("",!0):(C(),L("div",Eo,u[2]||(u[2]=[k("h1",{class:"text-lg font-bold text-text-primary"},"量化分析",-1),k("p",{class:"text-xs text-text-muted"},"专业版",-1)])))])]),k("nav",To,[k("div",Ao,[(C(!0),L($e,null,Le(o.value,l=>(C(),J(c,{key:l.name,to:l.path,class:ie(["nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors",{active:i.$route.path===l.path}])},{default:B(()=>[N(z,{name:l.iconName,class:"w-5 text-sm"},null,8,["name"]),D(r).sidebarCollapsed?q("",!0):(C(),L("span",Ro,V(l.label),1))]),_:2},1032,["to","class"]))),128))])]),k("div",Oo,[k("div",Co,[k("button",{onClick:u[0]||(u[0]=l=>D(s).push("/settings")),class:ie(["nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors hover:bg-bg-tertiary",{active:i.$route.path==="/settings","justify-start":!D(r).sidebarCollapsed,"justify-center":D(r).sidebarCollapsed}]),title:"设置"},[N(z,{name:"settings",class:"w-5 text-sm"}),D(r).sidebarCollapsed?q("",!0):(C(),L("span",Po,"设置"))],2),k("button",{onClick:u[1]||(u[1]=(...l)=>D(t).toggleTheme&&D(t).toggleTheme(...l)),class:ie(["w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors hover:bg-bg-tertiary",{"justify-start":!D(r).sidebarCollapsed,"justify-center":D(r).sidebarCollapsed}]),title:D(t).isDark?"切换浅色模式":"切换深色模式"},[N(z,{name:D(t).isDark?"light":"moon",class:"w-5 text-sm"},null,8,["name"]),D(r).sidebarCollapsed?q("",!0):(C(),L("span",Fo,V(D(t).isDark?"浅色模式":"深色模式"),1))],10,Do)])])],2)}}},No=Re(Io,[["__scopeId","data-v-1c498225"]]),$o={class:"navbar h-80px bg-bg-secondary border-b border-border-color px-6 z-1500 flex items-center"},Lo={class:"flex items-center justify-between w-full flex-1"},Mo={class:"flex items-center space-x-4"},Uo={class:"breadcrumb"},jo={class:"flex items-center space-x-2 text-sm"},Bo={key:1,class:"text-text-primary font-medium"},qo={class:"flex items-center space-x-4"},zo={class:"relative search-container"},Ho={key:0,class:"absolute top-full left-0 right-0 mt-2 bg-bg-secondary border border-border-color rounded-lg shadow-xl z-9999"},Wo={key:0,class:"px-4 py-3 text-center text-text-muted"},Vo=["onClick"],Jo={class:"flex justify-between items-center"},Zo={class:"flex-1"},Ko={class:"font-medium"},Go={class:"text-sm text-text-muted"},Xo={key:0,class:"ml-2 text-xs bg-bg-tertiary px-2 py-0.5 rounded"},Qo={class:"text-right ml-4"},Yo={key:0,class:"text-xs text-text-muted"},ea={key:1,class:"text-xs text-accent-primary"},ta={key:1,class:"px-4 py-3 text-center text-text-muted"},ra={class:"flex items-center justify-center space-x-2"},na={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex-center"},sa={class:"user-avatar-container"},oa={class:"user-avatar"},aa={class:"user-info"},ia={class:"user-name"},ca={class:"user-role"},la={__name:"Navbar",setup(e){const t=Br(),r=Kt(),n=Ae(),s=wt(),{isFullscreen:o,toggle:a}=En(),i=F(""),u=F([]),c=F(null),l=F(!1),h=F(0),w={"/":"仪表盘","/stocks":"股票列表","/watchlist":"自选股","/analysis":"技术分析","/strategies":"量化策略","/settings":"设置"},g=U(()=>{const x=[];if(t.path==="/")x.push({label:"仪表盘",path:"/"});else if(t.path.startsWith("/stocks/"))x.push({label:"股票列表",path:"/stocks"}),t.params.code&&x.push({label:t.params.code});else if(t.path.startsWith("/analysis/"))x.push({label:"技术分析",path:"/analysis"}),t.params.code&&x.push({label:`${t.params.code} 分析`});else{const T=w[t.path];T&&x.push({label:T,path:t.path})}return x}),f=async()=>{if(!i.value.trim()){u.value=[],l.value=!1;return}l.value=!0;try{const x=await ne.stockSearch.quickSearchStocks(i.value,10);u.value=x.data||[]}catch(x){console.error("搜索失败:",x),u.value=[]}finally{l.value=!1}},m=()=>{c.value&&clearTimeout(c.value),c.value=setTimeout(()=>{f()},300)},p=x=>{r.push({name:"Analysis",query:{stock:x.code,name:x.name}}),i.value="",u.value=[],l.value=!1},E=()=>{console.log("切换通知面板")},_=()=>{r.push({path:"/login",query:{redirect:t.fullPath}})},v=async x=>{switch(x){case"profile":De.info("个人资料功能开发中");break;case"user-management":r.push("/user-management");break;case"settings":r.push("/settings");break;case"logout":await P();break}},P=async()=>{try{await en.confirm("确定要退出登录吗？","确认登出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await s.logout(),De.success("已退出登录"),r.push("/login")}catch(x){x!=="cancel"&&(console.error("登出失败:",x),De.error("登出失败"))}};je(i,x=>{x.trim()?m():(u.value=[],l.value=!1,c.value&&clearTimeout(c.value))});const I=x=>{x.target.closest(".search-container")||(u.value=[],l.value=!1)};return Be(()=>{document.addEventListener("click",I)}),qr(()=>{document.removeEventListener("click",I),c.value&&clearTimeout(c.value)}),(x,T)=>{const M=ft("router-link"),re=Kr,se=Yr,Oe=Qr,b=Gr,A=Xr;return C(),L("div",$o,[k("div",Lo,[k("div",Mo,[k("button",{onClick:T[0]||(T[0]=(...y)=>D(n).toggleSidebar&&D(n).toggleSidebar(...y)),class:"p-2 rounded-lg hover:bg-bg-tertiary transition-colors"},[N(z,{name:"menu",class:"text-xl"})]),k("nav",Uo,[k("ol",jo,[(C(!0),L($e,null,Le(g.value,(y,R)=>(C(),L("li",{key:R},[y.path&&R<g.value.length-1?(C(),J(M,{key:0,to:y.path,class:"text-text-muted hover:text-text-primary transition-colors"},{default:B(()=>[oe(V(y.label),1)]),_:2},1032,["to"])):(C(),L("span",Bo,V(y.label),1)),T[3]||(T[3]=oe()),R<g.value.length-1?(C(),J(z,{key:2,name:"chevron-right",class:"text-text-muted mx-2",size:16})):q("",!0)]))),128))])])]),k("div",qo,[k("div",zo,[zr(k("input",{"onUpdate:modelValue":T[1]||(T[1]=y=>i.value=y),type:"text",placeholder:"搜索股票...",class:"search-input w-64 pl-10 pr-4 py-2",onKeyup:Wr(f,["enter"]),onInput:m},null,544),[[Hr,i.value]]),N(z,{name:"search",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-text-muted",size:16}),i.value.trim()&&(l.value||u.value.length>0)?(C(),L("div",Ho,[N(re,{"max-height":"240px"},{default:B(()=>[l.value?(C(),L("div",Wo,T[4]||(T[4]=[k("div",{class:"flex items-center justify-center space-x-2"},[k("div",{class:"animate-spin rounded-full h-4 w-4 border-2 border-accent-primary border-t-transparent"}),k("span",null,"搜索中...")],-1)]))):q("",!0),(C(!0),L($e,null,Le(u.value,y=>(C(),L("div",{key:y.code,onClick:R=>p(y),class:"px-4 py-3 hover:bg-bg-tertiary cursor-pointer border-b border-border-color last:border-b-0"},[k("div",Jo,[k("div",Zo,[k("div",Ko,V(y.name),1),k("div",Go,[oe(V(y.full_code||y.code)+" ",1),y.exchange?(C(),L("span",Xo,V(y.exchange),1)):q("",!0)])]),k("div",Qo,[y.industry?(C(),L("div",Yo,V(y.industry),1)):q("",!0),y.match_type?(C(),L("div",ea,V(y.match_type==="code"?"代码匹配":y.match_type==="name"?"名称匹配":"行业匹配"),1)):q("",!0)])])],8,Vo))),128)),!l.value&&u.value.length===0?(C(),L("div",ta,[k("div",ra,[N(z,{name:"search",size:16}),T[5]||(T[5]=k("span",null,"未找到相关股票",-1))])])):q("",!0)]),_:1})])):q("",!0)]),k("button",{class:"relative p-2 rounded-lg hover:bg-bg-tertiary transition-colors",onClick:E},[T[6]||(T[6]=k("i",{class:"i-carbon-notification text-xl"},null,-1)),h.value>0?(C(),L("span",na,V(h.value),1)):q("",!0)]),D(s).isAuthenticated?(C(),J(b,{key:0,trigger:"click",placement:"bottom-end",onCommand:v},{dropdown:B(()=>[N(Oe,null,{default:B(()=>[N(se,{command:"profile"},{default:B(()=>[N(z,{name:"user",class:"mr-2"}),T[7]||(T[7]=oe(" 个人资料 ",-1))]),_:1,__:[7]}),D(s).isAdmin?(C(),J(se,{key:0,command:"user-management"},{default:B(()=>[N(z,{name:"user-admin",class:"mr-2"}),T[8]||(T[8]=oe(" 用户管理 ",-1))]),_:1,__:[8]})):q("",!0),N(se,{command:"settings"},{default:B(()=>[N(z,{name:"settings",class:"mr-2"}),T[9]||(T[9]=oe(" 系统设置 ",-1))]),_:1,__:[9]}),N(se,{divided:"",command:"logout"},{default:B(()=>[N(z,{name:"logout",class:"mr-2 text-red-500"}),T[10]||(T[10]=k("span",{class:"text-red-500"},"退出登录",-1))]),_:1,__:[10]})]),_:1})]),default:B(()=>{var y;return[k("div",sa,[k("div",oa,[N(z,{name:"user",class:"text-lg"})]),k("div",aa,[k("div",ia,V((y=D(s).user)==null?void 0:y.username),1),k("div",ca,V(D(s).isAdmin?"管理员":"用户"),1)]),N(z,{name:"chevron-down",class:"text-sm text-text-muted ml-2"})])]}),_:1})):(C(),J(A,{key:1,type:"primary",size:"small",onClick:_},{default:B(()=>T[11]||(T[11]=[oe(" 登录 ",-1)])),_:1,__:[11]})),D(s).isAuthenticated?(C(),J(M,{key:2,to:"/settings",class:"p-2 rounded-lg hover:bg-bg-tertiary transition-colors"},{default:B(()=>[N(z,{name:"settings",class:"text-xl"})]),_:1})):q("",!0),k("button",{onClick:T[2]||(T[2]=(...y)=>D(a)&&D(a)(...y)),class:"p-2 rounded-lg hover:bg-bg-tertiary transition-colors"},[N(z,{name:D(o)?"minimize":"maximize",class:"text-xl"},null,8,["name"])])])])])}}},ua=Re(la,[["__scopeId","data-v-ef757d65"]]),da={class:"notification-container fixed top-4 right-4 z-50 space-y-2"},fa={class:"flex items-start space-x-3"},ma={class:"flex-1"},ha={key:0,class:"font-semibold mb-1"},pa={class:"text-sm"},ga=["onClick"],ya={__name:"NotificationContainer",setup(e){const t=Ae(),r=s=>{const o={success:"bg-green-500/10 border border-green-500/20 text-green-400",error:"bg-red-500/10 border border-red-500/20 text-red-400",warning:"bg-yellow-500/10 border border-yellow-500/20 text-yellow-400",info:"bg-blue-500/10 border border-blue-500/20 text-blue-400"};return o[s]||o.info},n=s=>{const o={success:"i-carbon-checkmark-filled",error:"i-carbon-error-filled",warning:"i-carbon-warning-filled",info:"i-carbon-information-filled"};return o[s]||o.info};return(s,o)=>(C(),J(Gt,{to:"body"},[k("div",da,[N(Vr,{name:"notification",tag:"div"},{default:B(()=>[(C(!0),L($e,null,Le(D(t).notifications,a=>(C(),L("div",{key:a.id,class:ie(["notification-item p-4 rounded-lg shadow-xl max-w-sm",r(a.type)])},[k("div",fa,[k("i",{class:ie([n(a.type),"text-lg mt-0.5"])},null,2),k("div",ma,[a.title?(C(),L("h4",ha,V(a.title),1)):q("",!0),k("p",pa,V(a.message),1)]),k("button",{onClick:i=>D(t).removeNotification(a.id),class:"text-gray-400 hover:text-gray-600 transition-colors"},o[0]||(o[0]=[k("i",{class:"i-carbon-close text-sm"},null,-1)]),8,ga)])],2))),128))]),_:1})])]))}},wa=Re(ya,[["__scopeId","data-v-8b54314f"]]),_a={class:"loading-overlay fixed inset-0 z-50 flex-center bg-black/20 backdrop-blur-sm"},va={class:"loading-spinner glass-card p-8 rounded-2xl flex-center flex-col space-y-4"},ba={class:"text-text-secondary font-medium"},Sa={__name:"LoadingOverlay",props:{message:{type:String,default:"加载中..."}},setup(e){return(t,r)=>(C(),J(Gt,{to:"body"},[k("div",_a,[k("div",va,[r[0]||(r[0]=k("div",{class:"spinner w-12 h-12 border-4 border-accent-primary/20 border-t-accent-primary rounded-full animate-spin"},null,-1)),k("p",ba,V(e.message),1)])])]))}},ka=Re(Sa,[["__scopeId","data-v-bbdc237f"]]),xa={key:1,class:"min-h-screen flex bg-bg-primary"},Ea={class:"flex-1 flex flex-col"},Ta={class:"flex-1 p-6 overflow-auto bg-bg-primary"},Aa={__name:"App",setup(e){const t=ht(),r=Ae(),n=wn(yn),s=U(()=>n.greater("md").value);return Be(()=>{t.initTheme(),s.value||r.setSidebarCollapsed(!0)}),(o,a)=>{const i=ft("router-view");return C(),L("div",{id:"app",class:ie(D(t).themeClass)},[o.$route.name==="Login"?(C(),J(i,{key:0},{default:B(({Component:u,route:c})=>[N(St,{name:"fade",mode:"out-in"},{default:B(()=>[(C(),J(kt(u),{key:c.path}))]),_:2},1024)]),_:1})):(C(),L("div",xa,[!D(r).sidebarCollapsed||s.value?(C(),J(No,{key:0})):q("",!0),k("div",Ea,[N(ua),k("main",Ta,[N(i,null,{default:B(({Component:u,route:c})=>[N(St,{name:"fade",mode:"out-in"},{default:B(()=>[(C(),J(kt(u),{key:c.path}))]),_:2},1024)]),_:1})])])])),N(wa),D(r).loading?(C(),J(ka,{key:2})):q("",!0)],2)}}},Ra=Re(Aa,[["__scopeId","data-v-9f2a954c"]]),vt=Jr(Ra);vt.use(Or);vt.use(_t);vt.mount("#app");export{Re as _,z as a,ne as b,vo as c,bo as d,ht as e,Pa as f,S as g,_o as h,ae as s,wt as u};
