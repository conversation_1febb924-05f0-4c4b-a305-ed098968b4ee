import{az as me,r as D,c as z,w as W,x as f,y as b,B as e,O as d,I as X,L as y,F as w,S as ve,K as O,M as J,ab as N,H as h,E as se,G as ee,P as le,a8 as ne,n as pe,j as re,u as R,Q as ge,U as be}from"./vendor-Dq0JXR-b.js";import{_ as K,d as ie,e as de,a as V,f as _e,g as xe,s as fe}from"./index-BSF2zNx2.js";import{C as ye}from"./CommonTable-DDC4hMKa.js";import{o as ke,c as ue,k as $e,p as we,q as he,r as je,l as Se,E as A,s as Pe,t as De}from"./ui-CHSsU9Lc.js";/* empty css                   *//* empty css                 *//* empty css                        */import{P as Me}from"./PeriodIndicatorMatrix-CdnFxisq.js";/* empty css                    */import"./CommonPagination-IzNmwDoz.js";function Ve(u,M="YYYY-MM-DD"){return u?ke(u).format(M):""}const Ce={class:"card hover:transform-none hover:border-border-color bg-bg-secondary p-6 rounded-lg shadow-lg"},Te={class:"flex items-center justify-between mb-6"},Ue={key:0,class:"text-sm text-text-secondary mt-1"},Ee={key:0,class:"text-sm text-text-secondary"},Ie={class:"font-medium text-text-primary"},ze={class:"text-text-primary"},Ae={class:"text-text-secondary text-sm"},Je={class:"font-medium text-text-primary text-center"},Oe={class:"flex justify-center gap-1"},Re={class:"indicator-card"},Ne={class:"indicator-values"},Be={class:"value-row"},Le={class:"value-number primary"},Ye={class:"value-row"},Fe={class:"value-number primary"},We={class:"value-row"},Ke={class:"value-number secondary"},qe={key:0,class:"indicator-badge golden"},He={key:1,class:"indicator-badge death"},Ge={class:"indicator-card"},Qe={class:"indicator-values"},Xe={class:"value-row"},Ze={class:"value-number primary"},et={class:"value-row"},tt={class:"value-number secondary"},at={key:0,class:"indicator-badge breakout"},st={class:"indicator-card"},lt={class:"indicator-values bollinger-bands"},ot={class:"band-row upper"},nt={class:"band-value"},rt={class:"band-row middle"},it={class:"band-value"},dt={class:"band-row lower"},ut={class:"band-value"},ct={class:"text-sm"},mt={class:"text-text-secondary text-sm"},vt={__name:"ScannerResults",setup(u){const M=me(),v=ie(),C=de(),c=D(1),l=D(20),s=z(()=>v.results),j=z(()=>v.totalCount),r=z(()=>v.loadingResults),S=z(()=>s.value&&s.value.length>0&&s.value[0].end_date?s.value[0].end_date:null),m=[{prop:"stock_code",label:"代码",minWidth:120,slot:"stock_code"},{prop:"stock_name",label:"名称",minWidth:160,slot:"stock_name"},{prop:"price",label:"最新价",minWidth:100,align:"center",slot:"price"},{prop:"change_percent",label:"涨跌幅",minWidth:100,align:"center",slot:"change_percent"},{prop:"period",label:"周期",minWidth:80,align:"center",slot:"period"},{prop:"signals",label:"信号",minWidth:120,align:"center",slot:"signals"},{prop:"kdj",label:"KDJ指标",minWidth:140,align:"center",slot:"kdj"},{prop:"volume_pressure",label:"成交量压力",minWidth:140,align:"center",slot:"volume_pressure"},{prop:"bollinger",label:"布林带",minWidth:140,align:"center",slot:"bollinger"},{prop:"trigger_reason",label:"触发原因",minWidth:160,align:"left",slot:"trigger_reason"},{prop:"scan_time",label:"扫描时间",minWidth:120,slot:"scan_time"}],k=z(()=>({total:j.value,currentPage:c.value,pageSize:l.value,pageSizes:[10,20,50,100],layout:"total, sizes, prev, pager, next"}));W(()=>{var o;return(o=v.currentTask)==null?void 0:o.status},(o,t)=>{o==="completed"&&t==="running"&&(c.value=1,$())});const $=()=>{var o;(o=v.currentTask)!=null&&o.id&&v.fetchResults(c.value,l.value)},a=({page:o,size:t})=>{t&&t!==l.value?(l.value=t,c.value=1):c.value=o,$()},p=o=>{M.push({path:"/analysis",query:{stock:o.stock_code,name:o.stock_name,industry:o.industry}})},E=o=>(o==null?void 0:o.toFixed(2))||"-",F=o=>o==null?"-":`${o>0?"+":""}${o.toFixed(2)}%`,I=o=>(o==null?void 0:o.toFixed(2))||"-",q=o=>Ve(o,"MM-DD HH:mm"),H=o=>o>0?"text-red-500":o<0?"text-green-500":"text-text-secondary",B=o=>{const t=C.isDark;switch(o){case"buy":return t?"bg-green-100 text-green-700":"bg-green-50 text-green-800";case"sell":return t?"bg-red-100 text-red-700":"bg-red-50 text-red-800";case"stop_loss":return t?"bg-yellow-100 text-yellow-700":"bg-yellow-50 text-yellow-800";default:return t?"bg-gray-100 text-gray-700":"bg-gray-50 text-gray-800"}},G=o=>{switch(o){case"buy":return"买入";case"sell":return"卖出";case"stop_loss":return"止损";default:return o}},P=o=>{const t=o.indicator_data;return!t||!t.prev_kdj_k||!t.prev_kdj_d?!1:t.prev_kdj_k<t.prev_kdj_d&&t.kdj_k>t.kdj_d},g=o=>{const t=o.indicator_data;return!t||!t.prev_kdj_k||!t.prev_kdj_d?!1:t.prev_kdj_k>t.prev_kdj_d&&t.kdj_k<t.kdj_d},n=o=>{const t=o.indicator_data;return!t||!t.volume_pressure||!t.volume_pressure_avg?!1:t.volume_pressure>t.volume_pressure_avg},_=o=>{const t=o.indicator_data;if(!t||!t.close_price||!t.bollinger_lower||!t.bollinger_upper)return"text-text-secondary";const{close_price:T,bollinger_lower:i,bollinger_upper:x,bollinger_middle:U}=t;return T<=i?"text-red-600":T>=x?"text-green-600":T>U?"text-blue-600":"text-yellow-600"},Q=o=>{const t=o.indicator_data;if(!t||!t.close_price||!t.bollinger_lower||!t.bollinger_upper)return"-";const{close_price:T,bollinger_lower:i,bollinger_upper:x,bollinger_middle:U,bollinger_distance_pct:Y}=t;return T<=i?`下轨 (${I(Y)}%)`:T>=x?"上轨突破":T>U?"中轨上方":"中轨下方"},L=o=>{const t=[];P(o)&&t.push("KDJ金叉信号"),n(o)&&t.push("成交量压力突破");const T=o.indicator_data;return T&&T.bollinger_distance_pct<=5&&t.push("布林带下轨机会"),t.length>0?t:["条件匹配"]},te=o=>({d:"日线",w:"周线",m:"月线",y:"年线"})[o]||o,ae=o=>({d:"period-badge--daily",w:"period-badge--weekly",m:"period-badge--monthly",y:"period-badge--yearly"})[o]||"period-badge--default";return(o,t)=>{const T=ue;return b(),f("div",Ce,[e("div",Te,[e("div",null,[t[0]||(t[0]=e("h3",{class:"text-xl font-semibold text-text-primary"}," 扫描结果 ",-1)),S.value?(b(),f("div",Ue," 数据截止: "+y(S.value),1)):X("",!0)]),j.value>0?(b(),f("div",Ee," 共 "+y(j.value)+" 条记录 ",1)):X("",!0)]),d(ye,{data:s.value,columns:m,loading:r.value,pagination:k.value,"show-pagination":j.value>0,onRowClick:p,onPageChange:a},{stock_code:w(({value:i})=>[e("span",Ie,y(i),1)]),stock_name:w(({value:i})=>[e("span",ze,y(i),1)]),industry:w(({value:i})=>[e("span",Ae,y(i||"-"),1)]),price:w(({value:i})=>[e("span",Je,y(E(i)),1)]),change_percent:w(({value:i})=>[e("span",{class:h(["font-medium",H(i)])},y(F(i)),3)]),period:w(({value:i})=>[e("span",{class:h(["period-badge",ae(i)])},y(te(i)),3)]),signals:w(({row:i})=>[e("div",Oe,[(b(!0),f(J,null,N(i.signals,x=>(b(),f("span",{key:x,class:h(["px-2 py-1 text-xs rounded-full font-medium",B(x)])},y(G(x)),3))),128))])]),kdj:w(({row:i})=>{var x,U,Y;return[e("div",Re,[e("div",Ne,[e("div",Be,[t[1]||(t[1]=e("span",{class:"value-label"},"K",-1)),e("span",Le,y(I((x=i.indicator_data)==null?void 0:x.kdj_k)),1)]),e("div",Ye,[t[2]||(t[2]=e("span",{class:"value-label"},"D",-1)),e("span",Fe,y(I((U=i.indicator_data)==null?void 0:U.kdj_d)),1)]),e("div",We,[t[3]||(t[3]=e("span",{class:"value-label"},"J",-1)),e("span",Ke,y(I((Y=i.indicator_data)==null?void 0:Y.kdj_j)),1)])]),P(i)?(b(),f("div",qe,t[4]||(t[4]=[e("span",{class:"badge-text"},"金叉",-1)]))):g(i)?(b(),f("div",He,t[5]||(t[5]=[e("span",{class:"badge-text"},"死叉",-1)]))):X("",!0)])]}),volume_pressure:w(({row:i})=>{var x,U;return[e("div",Ge,[e("div",Qe,[e("div",Xe,[t[6]||(t[6]=e("span",{class:"value-label"},"当前",-1)),e("span",Ze,y(I((x=i.indicator_data)==null?void 0:x.volume_pressure)),1)]),e("div",et,[t[7]||(t[7]=e("span",{class:"value-label"},"平均",-1)),e("span",tt,y(I((U=i.indicator_data)==null?void 0:U.volume_pressure_avg)),1)]),t[8]||(t[8]=e("div",{class:"value-row placeholder"},[e("span",{class:"value-label"}," "),e("span",{class:"value-number"}," ")],-1))]),n(i)?(b(),f("div",at,t[9]||(t[9]=[e("span",{class:"badge-text"},"突破",-1)]))):X("",!0)])]}),bollinger:w(({row:i})=>{var x,U,Y;return[e("div",st,[e("div",lt,[e("div",ot,[t[10]||(t[10]=e("span",{class:"band-label"},"上轨",-1)),e("span",nt,y(I((x=i.indicator_data)==null?void 0:x.bollinger_upper)),1)]),e("div",rt,[t[11]||(t[11]=e("span",{class:"band-label"},"中轨",-1)),e("span",it,y(I((U=i.indicator_data)==null?void 0:U.bollinger_middle)),1)]),e("div",dt,[t[12]||(t[12]=e("span",{class:"band-label"},"下轨",-1)),e("span",ut,y(I((Y=i.indicator_data)==null?void 0:Y.bollinger_lower)),1)])]),e("div",{class:h(["indicator-badge position",_(i)])},y(Q(i)),3)])]}),trigger_reason:w(({row:i})=>[e("div",ct,[(b(!0),f(J,null,N(L(i),x=>(b(),f("div",{key:x,class:"text-blue-600 text-xs mb-1"}," • "+y(x),1))),128))])]),scan_time:w(({value:i})=>[e("span",mt,y(q(i)),1)]),actions:w(({row:i})=>[d(T,{type:"text",size:"small",onClick:ve(x=>p(i),["stop"]),class:"text-primary hover:text-primary-dark"},{default:w(()=>t[13]||(t[13]=[O(" 详情 ",-1)])),_:2,__:[13]},1032,["onClick"])]),_:1},8,["data","loading","pagination","show-pagination"])])}}},pt=K(vt,[["__scopeId","data-v-b09f3a60"]]),gt={class:"parameter-section"},bt={class:"section-title"},_t={class:"parameter-grid"},xt={class:"parameter-item"},ft={class:"parameter-item"},yt={class:"parameter-item"},kt={key:1,class:"parameter-item"},$t={class:"parameter-item"},wt={class:"parameter-item"},ht={class:"parameter-item"},jt={class:"parameter-item"},St={class:"parameter-item"},Pt={__name:"ParameterSection",props:{indicator:{type:String,required:!0},parameters:{type:Object,required:!0}},emits:["update"],setup(u,{emit:M}){const v=u,C=M,c=j=>({kdj:"chart-line",volume_pressure:"chart-column",bollinger:"chart-area",macd:"chart-line-data"})[j]||"chart-line",l=j=>({kdj:"KDJ 指标参数",volume_pressure:"成交量压力参数",bollinger:"布林带参数",macd:"MACD 指标参数"})[j]||j,s=(j,r)=>{C("update",v.indicator,{...v.parameters,[j]:r})};return(j,r)=>{const S=$e;return b(),f("div",gt,[e("div",bt,[d(V,{name:c(u.indicator),class:"mr-2 text-primary"},null,8,["name"]),e("span",null,y(l(u.indicator)),1)]),e("div",_t,[u.indicator==="kdj"?(b(),f(J,{key:0},[e("div",xt,[r[9]||(r[9]=e("label",null,"周期 (n)",-1)),d(S,{"model-value":u.parameters.n,"onUpdate:modelValue":r[0]||(r[0]=m=>s("n",m)),min:5,max:100,size:"small","controls-position":"right"},null,8,["model-value"])]),e("div",ft,[r[10]||(r[10]=e("label",null,"K值平滑 (m1)",-1)),d(S,{"model-value":u.parameters.m1,"onUpdate:modelValue":r[1]||(r[1]=m=>s("m1",m)),min:1,max:20,size:"small","controls-position":"right"},null,8,["model-value"])]),e("div",yt,[r[11]||(r[11]=e("label",null,"D值平滑 (m2)",-1)),d(S,{"model-value":u.parameters.m2,"onUpdate:modelValue":r[2]||(r[2]=m=>s("m2",m)),min:1,max:20,size:"small","controls-position":"right"},null,8,["model-value"])])],64)):u.indicator==="volume_pressure"?(b(),f("div",kt,[r[12]||(r[12]=e("label",null,"EMA周期",-1)),d(S,{"model-value":u.parameters.ema_period,"onUpdate:modelValue":r[3]||(r[3]=m=>s("ema_period",m)),min:3,max:50,size:"small","controls-position":"right"},null,8,["model-value"])])):u.indicator==="bollinger"?(b(),f(J,{key:2},[e("div",$t,[r[13]||(r[13]=e("label",null,"窗口大小",-1)),d(S,{"model-value":u.parameters.window,"onUpdate:modelValue":r[4]||(r[4]=m=>s("window",m)),min:5,max:100,size:"small","controls-position":"right"},null,8,["model-value"])]),e("div",wt,[r[14]||(r[14]=e("label",null,"标准差倍数",-1)),d(S,{"model-value":u.parameters.std_dev,"onUpdate:modelValue":r[5]||(r[5]=m=>s("std_dev",m)),min:.5,max:5,step:.1,precision:1,size:"small","controls-position":"right"},null,8,["model-value"])])],64)):u.indicator==="macd"?(b(),f(J,{key:3},[e("div",ht,[r[15]||(r[15]=e("label",null,"快线周期",-1)),d(S,{"model-value":u.parameters.fast_period,"onUpdate:modelValue":r[6]||(r[6]=m=>s("fast_period",m)),min:5,max:50,size:"small","controls-position":"right"},null,8,["model-value"])]),e("div",jt,[r[16]||(r[16]=e("label",null,"慢线周期",-1)),d(S,{"model-value":u.parameters.slow_period,"onUpdate:modelValue":r[7]||(r[7]=m=>s("slow_period",m)),min:10,max:100,size:"small","controls-position":"right"},null,8,["model-value"])]),e("div",St,[r[17]||(r[17]=e("label",null,"信号线周期",-1)),d(S,{"model-value":u.parameters.signal_period,"onUpdate:modelValue":r[8]||(r[8]=m=>s("signal_period",m)),min:3,max:30,size:"small","controls-position":"right"},null,8,["model-value"])])],64)):X("",!0)])])}}},oe=K(Pt,[["__scopeId","data-v-48ae5b95"]]),Z={kdj:{n:9,m1:3,m2:3},volume_pressure:{ema_period:10},bollinger:{window:20,std_dev:2}};function ce(){const u=_e("scanner-indicator-parameters",Z),M=()=>{u.value={...Z}},v=(l,s)=>{u.value[l]&&(u.value[l]={...u.value[l],...s})},C=l=>u.value[l]||Z[l];return{indicatorParameters:u,resetParameters:M,updateIndicatorParameters:v,getIndicatorParameters:C,formatParameterText:l=>{const s=C(l);switch(l){case"kdj":return`n=${s.n}, m1=${s.m1}, m2=${s.m2}`;case"volume_pressure":return`EMA=${s.ema_period}`;case"bollinger":return`窗口=${s.window}, 标准差=${s.std_dev}`;default:return""}},DEFAULT_PARAMETERS:Z}}const Dt={class:"parameter-config"},Mt={class:"mode-indicator mb-4"},Vt={key:0},Ct={key:1},Tt={class:"flex items-center"},Ut={class:"period-content"},Et={class:"dialog-footer"},It={__name:"ParameterConfigDialog",props:{modelValue:{type:Boolean,default:!1},scanMode:{type:String,default:"traditional"},selectedPeriods:{type:Array,default:()=>["d"]}},emits:["update:modelValue","confirm"],setup(u,{emit:M}){const v=u,C=M,{indicatorParameters:c,periodIndicatorParameters:l,updateIndicatorParameters:s,updatePeriodIndicatorParameters:j,DEFAULT_PARAMETERS:r,DEFAULT_PERIOD_PARAMETERS:S}=ce(),m=D(v.modelValue),k=D("d"),$=z(()=>[{value:"d",label:"日线",icon:"calendar-today"},{value:"w",label:"周线",icon:"calendar-week"},{value:"m",label:"月线",icon:"calendar-month"}].filter(_=>v.selectedPeriods.includes(_.value))),a=D({kdj:{...c.value.kdj},volume_pressure:{...c.value.volume_pressure},bollinger:{...c.value.bollinger},macd:{...c.value.macd}}),p=D({d:{kdj:{...l.value.d.kdj},volume_pressure:{...l.value.d.volume_pressure},bollinger:{...l.value.d.bollinger},macd:{...l.value.d.macd}},w:{kdj:{...l.value.w.kdj},volume_pressure:{...l.value.w.volume_pressure},bollinger:{...l.value.w.bollinger},macd:{...l.value.w.macd}},m:{kdj:{...l.value.m.kdj},volume_pressure:{...l.value.m.volume_pressure},bollinger:{...l.value.m.bollinger},macd:{...l.value.m.macd}}});W(()=>v.modelValue,n=>{m.value=n,n&&(E(),$.value.length>0&&(k.value=$.value[0].value))}),W(m,n=>{C("update:modelValue",n)});const E=()=>{a.value={kdj:{...c.value.kdj},volume_pressure:{...c.value.volume_pressure},bollinger:{...c.value.bollinger},macd:{...c.value.macd}},p.value={d:{kdj:{...l.value.d.kdj},volume_pressure:{...l.value.d.volume_pressure},bollinger:{...l.value.d.bollinger},macd:{...l.value.d.macd}},w:{kdj:{...l.value.w.kdj},volume_pressure:{...l.value.w.volume_pressure},bollinger:{...l.value.w.bollinger},macd:{...l.value.w.macd}},m:{kdj:{...l.value.m.kdj},volume_pressure:{...l.value.m.volume_pressure},bollinger:{...l.value.m.bollinger},macd:{...l.value.m.macd}}}},F=(n,_)=>{a.value[n]={...a.value[n],..._}},I=(n,_,Q)=>{p.value[n][_]={...p.value[n][_],...Q}},q=(n,_)=>{p.value[_]=JSON.parse(JSON.stringify(p.value[n])),A.success(`已从${B(n)}复制参数到${B(_)}`)},H=n=>{p.value[n]=JSON.parse(JSON.stringify(r)),A.success(`已重置${B(n)}参数为默认值`)},B=n=>({d:"日线",w:"周线",m:"月线"})[n]||n,G=()=>{v.scanMode==="traditional"?a.value=JSON.parse(JSON.stringify(r)):p.value=JSON.parse(JSON.stringify(S)),A.success("已重置为默认参数")},P=()=>{m.value=!1},g=()=>{try{if(v.scanMode==="traditional")s("kdj",a.value.kdj),s("volume_pressure",a.value.volume_pressure),s("bollinger",a.value.bollinger),s("macd",a.value.macd),A.success("传统模式参数配置已保存"),C("confirm",a.value);else{for(const n of["d","w","m"])for(const _ of["kdj","volume_pressure","bollinger","macd"])j(n,_,p.value[n][_]);A.success("多周期参数配置已保存"),C("confirm",p.value)}m.value=!1}catch(n){A.error("保存参数失败"),console.error("Parameter save error:",n)}};return(n,_)=>{const Q=we,L=ue,te=je,ae=he,o=Se;return b(),se(o,{modelValue:m.value,"onUpdate:modelValue":_[1]||(_[1]=t=>m.value=t),title:"指标参数配置",width:"800px","before-close":P,"append-to-body":""},{footer:w(()=>[e("div",Et,[d(L,{onClick:G,type:"info",plain:""},{default:w(()=>_[3]||(_[3]=[O(" 重置全部默认 ",-1)])),_:1,__:[3]}),_[6]||(_[6]=e("div",{class:"flex-1"},null,-1)),d(L,{onClick:P},{default:w(()=>_[4]||(_[4]=[O("取消",-1)])),_:1,__:[4]}),d(L,{type:"primary",onClick:g},{default:w(()=>_[5]||(_[5]=[O("确认",-1)])),_:1,__:[5]})])]),default:w(()=>[e("div",Dt,[e("div",Mt,[d(Q,{type:u.scanMode==="traditional"?"success":"primary",size:"large"},{default:w(()=>[O(y(u.scanMode==="traditional"?"传统模式配置":"多周期模式配置"),1)]),_:1},8,["type"])]),u.scanMode==="traditional"?(b(),f("div",Vt,[(b(),f(J,null,N(["kdj","volume_pressure","bollinger","macd"],t=>d(oe,{key:t,indicator:t,parameters:a.value[t],onUpdate:F},null,8,["indicator","parameters"])),64))])):(b(),f("div",Ct,[d(ae,{modelValue:k.value,"onUpdate:modelValue":_[0]||(_[0]=t=>k.value=t),type:"card",class:"period-tabs"},{default:w(()=>[(b(!0),f(J,null,N($.value,t=>(b(),se(te,{key:t.value,label:t.label,name:t.value},{label:w(()=>[e("div",Tt,[d(V,{name:t.icon,class:"mr-2"},null,8,["name"]),O(" "+y(t.label)+"参数 ",1)])]),default:w(()=>{var T,i;return[e("div",Ut,[e("div",{class:h(["copy-controls mb-4",(T=n.$themeStore)!=null&&T.isDark?"copy-controls--dark":"copy-controls--light"])},[e("span",{class:h(["text-sm mr-3",(i=n.$themeStore)!=null&&i.isDark?"text-gray-400":"text-gray-600"])},"快速配置：",2),(b(!0),f(J,null,N($.value.filter(x=>x.value!==t.value),x=>(b(),se(L,{key:x.value,size:"small",type:"info",plain:"",onClick:U=>q(x.value,t.value)},{default:w(()=>[O(" 从"+y(x.label)+"复制 ",1)]),_:2},1032,["onClick"]))),128)),d(L,{size:"small",type:"warning",plain:"",onClick:x=>H(t.value)},{default:w(()=>_[2]||(_[2]=[O(" 重置为默认 ",-1)])),_:2,__:[2]},1032,["onClick"])],2),(b(),f(J,null,N(["kdj","volume_pressure","bollinger","macd"],x=>d(oe,{key:`${t.value}-${x}`,indicator:x,parameters:p.value[t.value][x],onUpdate:U=>I(t.value,x,U)},null,8,["indicator","parameters","onUpdate"])),64))])]}),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]))])]),_:1},8,["modelValue"])}}},zt=K(It,[["__scopeId","data-v-9da95295"]]),At={class:"scan-mode-selector"},Jt={class:"mode-selection-layer mb-6"},Ot={class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},Rt={class:"flex items-center justify-between mb-3"},Nt={class:"flex items-center space-x-3"},Bt={class:"text-xs text-gray-500"},Lt={class:"flex items-center space-x-2 mb-1"},Yt={class:"flex items-center space-x-2"},Ft={class:"flex items-center justify-between mb-3"},Wt={class:"flex items-center space-x-3"},Kt={class:"text-xs text-gray-500"},qt={class:"flex items-center space-x-2 mb-1"},Ht={class:"flex items-center space-x-2"},Gt={class:"strategy-selection-layer mb-6"},Qt={class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},Xt={class:"flex items-center justify-between mb-3"},Zt={class:"flex items-center space-x-3"},ea={class:"text-xs text-gray-500"},ta={class:"flex items-center space-x-2 mb-1"},aa={class:"flex items-center space-x-2 mb-1"},sa={class:"flex items-center space-x-2"},la={class:"flex items-center justify-between mb-3"},oa={class:"flex items-center space-x-3"},na={class:"text-xs text-gray-500"},ra={class:"flex items-center space-x-2 mb-1"},ia={class:"flex items-center space-x-2 mb-1"},da={class:"flex items-center space-x-2"},ua={class:"period-selection mb-6"},ca={class:"flex flex-wrap gap-3"},ma=["value"],va={__name:"ScanModeSelector",props:{modelValue:{type:Object,default:()=>({scanMode:"traditional",scanStrategy:"parallel",periods:["d"]})}},emits:["update:modelValue"],setup(u,{emit:M}){const v=u,C=M,c=D(v.modelValue.scanMode||"traditional"),l=D(v.modelValue.scanStrategy||"parallel"),s=D([...v.modelValue.periods]),j=[{value:"d",label:"日线",icon:"calendar"},{value:"w",label:"周线",icon:"calendar"},{value:"m",label:"月线",icon:"calendar"}],r=$=>{c.value=$,$==="traditional"?s.value=["d"]:s.value.length===1&&s.value[0]==="d"&&(s.value=["d","w"]),m()},S=$=>{l.value=$,m()},m=()=>{C("update:modelValue",{scanMode:c.value,scanStrategy:l.value,periods:[...s.value]})},k=D(!1);return W(s,()=>{k.value||(s.value.length===0&&(s.value=["d"]),m())},{deep:!0}),W(()=>v.modelValue,$=>{k.value=!0,c.value=$.scanMode||"traditional",l.value=$.scanStrategy||"parallel",s.value=[...$.periods||["d"]],pe(()=>{k.value=!1})},{deep:!0,immediate:!0}),($,a)=>(b(),f("div",At,[e("div",Jt,[e("div",Ot,[e("div",{class:h(["mode-card",{"mode-card--active":c.value==="traditional"}]),onClick:a[0]||(a[0]=p=>r("traditional"))},[e("div",Rt,[e("div",Nt,[e("div",{class:h(["radio-button",{"radio-button--active":c.value==="traditional"}])},[e("div",{class:h(["radio-dot",{"radio-dot--active":c.value==="traditional"}])},null,2)],2),a[5]||(a[5]=e("h4",{class:"text-lg font-semibold"},"传统扫描",-1))]),a[6]||(a[6]=e("div",{class:"mode-badge mode-badge--basic"},"基础",-1))]),a[9]||(a[9]=e("p",{class:"text-sm text-gray-400 mb-3"},"单周期基础指标快速筛选",-1)),e("div",Bt,[e("div",Lt,[d(V,{name:"chart-line",class:"text-green-400"}),a[7]||(a[7]=e("span",null,"仅日线指标分析",-1))]),e("div",Yt,[d(V,{name:"lightning",class:"text-yellow-400"}),a[8]||(a[8]=e("span",null,"速度最快，适合初筛",-1))])])],2),e("div",{class:h(["mode-card",{"mode-card--active":c.value==="multi_period"}]),onClick:a[1]||(a[1]=p=>r("multi_period"))},[e("div",Ft,[e("div",Wt,[e("div",{class:h(["radio-button",{"radio-button--active":c.value==="multi_period"}])},[e("div",{class:h(["radio-dot",{"radio-dot--active":c.value==="multi_period"}])},null,2)],2),a[10]||(a[10]=e("h4",{class:"text-lg font-semibold"},"多周期扫描",-1))]),a[11]||(a[11]=e("div",{class:"mode-badge mode-badge--advanced"},"高级",-1))]),a[14]||(a[14]=e("p",{class:"text-sm text-gray-400 mb-3"},"主周期完整分析，辅助周期上升通道确认",-1)),e("div",Kt,[e("div",qt,[d(V,{name:"layers",class:"text-purple-400"}),a[12]||(a[12]=e("span",null,"智能分层筛选策略",-1))]),e("div",Ht,[d(V,{name:"trending-up",class:"text-green-400"}),a[13]||(a[13]=e("span",null,"主周期完整计算+辅助周期趋势确认",-1))])])],2)])]),ee(e("div",Gt,[a[27]||(a[27]=e("label",{class:"block text-sm text-gray-400 mb-3"},"选择多周期实现策略",-1)),e("div",Qt,[e("div",{class:h(["strategy-card",{"strategy-card--active":l.value==="parallel"}]),onClick:a[2]||(a[2]=p=>S("parallel"))},[e("div",Xt,[e("div",Zt,[e("div",{class:h(["radio-button radio-button--purple",{"radio-button--active":l.value==="parallel"}])},[e("div",{class:h(["radio-dot",{"radio-dot--active":l.value==="parallel"}])},null,2)],2),a[15]||(a[15]=e("h4",{class:"text-lg font-semibold"},"并行计算",-1))]),a[16]||(a[16]=e("div",{class:"strategy-badge strategy-badge--comprehensive"},"全面分析",-1))]),a[20]||(a[20]=e("p",{class:"text-sm text-gray-400 mb-3"},"同时分析所有周期，主周期完整指标",-1)),e("div",ea,[e("div",ta,[d(V,{name:"connection-signal",class:"text-orange-400"}),a[17]||(a[17]=e("span",null,"并行处理所有周期",-1))]),e("div",aa,[d(V,{name:"chart-column",class:"text-blue-400"}),a[18]||(a[18]=e("span",null,"主周期全指标+辅助周期趋势",-1))]),e("div",sa,[d(V,{name:"lightning",class:"text-yellow-400"}),a[19]||(a[19]=e("span",null,"速度快，结果全面",-1))])])],2),e("div",{class:h(["strategy-card",{"strategy-card--active":l.value==="cascade"}]),onClick:a[3]||(a[3]=p=>S("cascade"))},[e("div",la,[e("div",oa,[e("div",{class:h(["radio-button radio-button--purple",{"radio-button--active":l.value==="cascade"}])},[e("div",{class:h(["radio-dot",{"radio-dot--active":l.value==="cascade"}])},null,2)],2),a[21]||(a[21]=e("h4",{class:"text-lg font-semibold"},"多层级复筛",-1))]),a[22]||(a[22]=e("div",{class:"strategy-badge strategy-badge--optimized"},"性能优化",-1))]),a[26]||(a[26]=e("p",{class:"text-sm text-gray-400 mb-3"},"先筛主周期，通过后验证辅助周期",-1)),e("div",na,[e("div",ra,[d(V,{name:"data-share",class:"text-blue-400"}),a[23]||(a[23]=e("span",null,"分层过滤，逐步筛选",-1))]),e("div",ia,[d(V,{name:"rocket",class:"text-green-400"}),a[24]||(a[24]=e("span",null,"主周期完整+辅助周期确认",-1))]),e("div",da,[d(V,{name:"arrow-up",class:"text-green-400"}),a[25]||(a[25]=e("span",null,"性能优化，精确筛选",-1))])])],2)])],512),[[le,c.value==="multi_period"]]),ee(e("div",ua,[a[28]||(a[28]=e("label",{class:"block text-sm text-gray-400 mb-3"},"选择扫描周期",-1)),e("div",ca,[(b(),f(J,null,N(j,p=>e("label",{key:p.value,class:"period-checkbox"},[ee(e("input",{type:"checkbox",value:p.value,"onUpdate:modelValue":a[4]||(a[4]=E=>s.value=E),class:"sr-only"},null,8,ma),[[ne,s.value]]),e("div",{class:h(["period-item",{"period-item--active":s.value.includes(p.value)}])},[d(V,{name:p.icon,class:"text-sm mr-2"},null,8,["name"]),e("span",null,y(p.label),1)],2)])),64))])],512),[[le,c.value==="multi_period"]])]))}},pa=K(va,[["__scopeId","data-v-c8366a14"]]),ga=async()=>{try{return(await xe.get("/trading/latest-trading-date")).data}catch(u){throw console.error("获取最近交易日失败:",u),u}},ba={class:"date-picker-container w-full"},_a={class:"date-picker-wrapper w-full"},xa={__name:"DatePicker",props:{modelValue:{type:String,default:null},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:"选择回测日期"}},emits:["update:modelValue"],setup(u,{emit:M}){const v=u,C=M,c=D(v.modelValue),l=D(""),s=D(!1),j=z(()=>c.value?`历史回测：基于 ${c.value} 及之前的数据进行分析`:"选择历史日期进行回测分析，默认使用最近交易日数据");z(()=>!c.value&&l.value?`默认：${l.value}`:c.value?`回测截止：${c.value}`:"请选择日期");const r=k=>{const $=new Date;return $.setHours(23,59,59,999),k>$},S=k=>{c.value=k,C("update:modelValue",k)},m=async()=>{try{s.value=!0;const k=await ga();l.value=k.date,c.value||(c.value=k.date,C("update:modelValue",k.date))}catch(k){console.error("获取最近交易日失败:",k)}finally{s.value=!1}};return W(()=>v.modelValue,k=>{c.value=k},{immediate:!0}),re(()=>{m()}),(k,$)=>(b(),f("div",ba,[d(R(De),{content:j.value,placement:"top","show-after":500},{default:w(()=>[e("div",_a,[d(R(Pe),{modelValue:c.value,"onUpdate:modelValue":$[0]||($[0]=a=>c.value=a),type:"date",placeholder:u.placeholder,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",disabled:u.disabled,clearable:!0,disabledDate:r,size:"large",onChange:S,class:"custom-date-picker w-full"},{prefix:w(()=>[d(V,{name:"calendar"})]),_:1},8,["modelValue","placeholder","disabled"])])]),_:1},8,["content"])]))}},fa=K(xa,[["__scopeId","data-v-ccce2e53"]]),ya={class:"scanner-page"},ka={class:"grid grid-cols-1 gap-6 mb-6"},$a={class:"card p-6"},wa={class:"mb-6"},ha={class:"grid grid-cols-1 xl:grid-cols-4 gap-6"},ja={class:"xl:col-span-3"},Sa={key:0,class:"h-full grid grid-cols-1 md:grid-cols-4 gap-4"},Pa=["value","disabled"],Da={class:"text-center"},Ma={key:1},Va={class:"xl:col-span-1"},Ca={class:"space-y-3"},Ta=["disabled"],Ua={class:"flex gap-2 items-center"},Ea=["disabled"],Ia=["disabled"],za={class:"space-y-2"},Aa={class:"flex items-center justify-between text-sm"},Ja={class:"text-text-secondary"},Oa={class:"text-primary font-medium"},Ra={__name:"index",setup(u){const M=de(),v=ie(),{formatParameterText:C,indicatorParameters:c,periodIndicatorParameters:l}=ce(),s=D({scanMode:"traditional",scanStrategy:"parallel",periods:["d"]}),j=D(null),r=D(!1),S=[{value:"kdj",label:"KDJ指标",desc:"动量振荡器",icon:"chart-line"},{value:"volume_pressure",label:"成交量压力",desc:"成交量分析",icon:"volume-file-storage"},{value:"macd",label:"MACD指标",desc:"趋势指标",icon:"chart-line-data"},{value:"bollinger",label:"布林带",desc:"波动率指标",icon:"chart-area"}],m=D({indicators:["kdj","volume_pressure"]}),k=D({d:["kdj","volume_pressure"],w:[],m:[]}),$=D(!1),a=D(!1),p=z(()=>v.currentTask),E=z(()=>v.isScanning),F=z(()=>{var P;return(P=p.value)!=null&&P.progress?Math.round(p.value.progress.percentage||0):0}),I=z(()=>{var n;if(!((n=p.value)!=null&&n.progress))return"0 / 0";const{current:P,total:g}=p.value.progress;return`${P} / ${g}`}),q=z(()=>{if(!p.value)return"bg-gray-400";switch(p.value.status){case"completed":return"bg-green-500";case"failed":return"bg-red-500";default:return"bg-gray-400"}}),H=async()=>{let P;if(s.value.scanMode==="traditional"){if(m.value.indicators.length===0){A.warning("请选择至少一个扫描指标");return}P={indicators:m.value.indicators,parameters:c.value,scan_mode:s.value.scanMode,scan_strategy:s.value.scanStrategy,periods:s.value.periods,adjust:"n",end_date:j.value}}else{if(!Object.values(k.value).some(n=>n.length>0)){A.warning("请为至少一个周期选择指标");return}P={indicators:[],period_indicators:k.value,period_parameters:l.value,scan_mode:s.value.scanMode,scan_strategy:s.value.scanStrategy,periods:s.value.periods,adjust:"n",end_date:j.value}}try{$.value=!0,await v.startScan(P),A.success("扫描任务已启动")}catch(g){g.message&&A.error(g.message)}finally{$.value=!1}},B=async()=>{try{a.value=!0,await v.stopScan(),A.success("扫描任务已停止")}catch(P){A.error(P.message||"停止失败")}finally{a.value=!1}},G=P=>{console.log("参数配置已更新:",P)};return re(async()=>{if(await v.initSession()&&v.currentTask)try{const g=await fe.getTask(v.currentTask.id);g&&g.end_date&&(j.value=g.end_date)}catch(g){console.warn("无法恢复任务配置:",g)}}),ge(()=>{v.cleanup()}),(P,g)=>(b(),f("div",ya,[e("div",ka,[e("div",$a,[e("div",wa,[g[6]||(g[6]=e("h2",{class:"text-2xl font-semibold text-text-primary mb-4"}," 扫描模式 ",-1)),d(pa,{modelValue:s.value,"onUpdate:modelValue":g[0]||(g[0]=n=>s.value=n),disabled:E.value},null,8,["modelValue","disabled"])]),g[8]||(g[8]=e("div",{class:"flex items-end justify-start mb-4 gap-2"},[e("h2",{class:"text-2xl font-semibold text-text-primary"},"扫描指标"),e("div",{class:"text-gray-500 text-sm"}," 基于服务商提供的历史数据进行技术指标回测分析 ")],-1)),e("div",ha,[e("div",ja,[s.value.scanMode==="traditional"?(b(),f("div",Sa,[(b(),f(J,null,N(S,n=>e("label",{key:n.value,class:h(["relative cursor-pointer",{"cursor-not-allowed opacity-50":E.value}])},[ee(e("input",{type:"checkbox","onUpdate:modelValue":g[1]||(g[1]=_=>m.value.indicators=_),value:n.value,disabled:E.value,class:"sr-only peer"},null,8,Pa),[[ne,m.value.indicators]]),e("div",{class:h(["indicator-card",{"indicator-card--active":m.value.indicators.includes(n.value),"indicator-card--disabled":E.value}])},[e("div",Da,[d(V,{name:n.icon,class:h(["text-2xl mb-2 transition-colors",m.value.indicators.includes(n.value)?"text-primary":R(M).isDark?"text-white text-opacity-90":"text-gray-800"])},null,8,["name","class"]),e("div",{class:h(["font-medium text-sm transition-colors",m.value.indicators.includes(n.value)?"text-primary":R(M).isDark?"text-white text-opacity-90":"text-gray-800"])},y(n.label),3),e("div",{class:h(["text-xs mt-1 transition-colors",m.value.indicators.includes(n.value)?"text-primary opacity-80":R(M).isDark?"text-white opacity-60":"text-gray-500"])},y(R(C)(n.value)),3)])],2)],2)),64))])):(b(),f("div",Ma,[d(Me,{modelValue:k.value,"onUpdate:modelValue":g[2]||(g[2]=n=>k.value=n),"scan-mode":s.value.scanMode,"selected-periods":s.value.periods,disabled:E.value},null,8,["modelValue","scan-mode","selected-periods","disabled"])]))]),e("div",Va,[e("div",Ca,[e("button",{onClick:g[3]||(g[3]=n=>r.value=!0),disabled:E.value,class:h(["w-full px-4 py-3 rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center border-0",R(M).isDark?"bg-white bg-opacity-10 text-white hover:bg-opacity-20":"bg-gray-100 text-gray-700 hover:bg-gray-200"])},[d(V,{name:"settings",class:"mr-2"}),g[7]||(g[7]=O(" 参数配置 ",-1))],10,Ta),e("div",Ua,[d(fa,{modelValue:j.value,"onUpdate:modelValue":g[4]||(g[4]=n=>j.value=n),disabled:E.value,placeholder:"选择截止日期",class:"h-48px flex-1"},null,8,["modelValue","disabled"]),E.value?(b(),f("button",{key:1,onClick:B,disabled:a.value,class:"px-3 py-3 min-w-80px bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"},[d(V,{name:"stop",class:"text-lg"})],8,Ia)):(b(),f("button",{key:0,onClick:H,disabled:$.value||m.value.indicators.length===0,class:"btn-primary px-3 py-3 flex items-center justify-center min-w-80px"},[d(V,{name:"play",class:"text-lg"})],8,Ea))]),e("div",za,[e("div",Aa,[e("span",Ja,y(p.value?I.value:"0 / 0"),1),e("span",Oa,y(p.value?F.value:0)+"%",1)]),e("div",{class:h(["w-full rounded-full h-2 overflow-hidden",R(M).isDark?"bg-bg-primary":"bg-gray-200"])},[e("div",{class:h(["h-full transition-all duration-300 ease-out rounded-full",p.value?q.value:"bg-gray-400"]),style:be({width:`${p.value?F.value:0}%`})},null,6)],2)])])])]),d(pt,{class:"mt-6"}),d(zt,{modelValue:r.value,"onUpdate:modelValue":g[5]||(g[5]=n=>r.value=n),"scan-mode":s.value.scanMode,"selected-periods":s.value.periods,onConfirm:G},null,8,["modelValue","scan-mode","selected-periods"])])])]))}},Qa=K(Ra,[["__scopeId","data-v-42c75f60"]]);export{Qa as default};
