import{g as t}from"./index-BSF2zNx2.js";const a={getTasks(e){return t.get("/scheduled-tasks/",{params:e})},createTask(e){return t.post("/scheduled-tasks/",e)},getTask(e){return t.get(`/scheduled-tasks/${e}`)},updateTask(e,s){return t.put(`/scheduled-tasks/${e}`,s)},deleteTask(e){return t.delete(`/scheduled-tasks/${e}`)},executeTask(e){return t.post(`/scheduled-tasks/${e}/execute`)},toggleTaskStatus(e,s){return t.put(`/scheduled-tasks/${e}/toggle`,null,{params:{is_active:s}})},getExecutions(e,s){return t.get(`/scheduled-tasks/${e}/executions`,{params:s})},getAllExecutions(e){return t.get("/task-executions/",{params:e})},getExecutionDetail(e){return t.get(`/task-executions/${e}`)},deleteExecution(e){return t.delete(`/task-executions/${e}`)},cancelExecution(e){return t.post(`/task-executions/${e}/cancel`)},getAllExecutionsAdmin(e){return t.get("/task-executions/admin/all",{params:e})}};export{a as s};
