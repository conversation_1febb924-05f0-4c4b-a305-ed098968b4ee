import{_ as C,a as r}from"./index-BSF2zNx2.js";import{c as S,x as c,y as a,B as n,M as o,ab as b,E as m,I as i,L as u,H as y,O as _,K as g}from"./vendor-Dq0JXR-b.js";const j={class:"matrix-header"},w={class:"period-header"},D={class:"flex items-center justify-center"},N={class:"indicator-cell"},O={class:"font-medium"},F={key:0,class:"text-xs text-gray-500"},K={key:0,class:"checkbox-wrapper"},A=["checked","onChange","disabled"],E={key:0,class:"secondary-indicator"},J={class:"text-xs text-green-400"},L={key:1,class:"unavailable-indicator"},p={class:"text-xs text-gray-500"},z={class:"matrix-note"},H={class:"text-sm text-gray-400"},T={__name:"PeriodIndicatorMatrix",props:{modelValue:{type:Object,default:()=>({})},scanMode:{type:String,default:"traditional"},selectedPeriods:{type:Array,default:()=>["d"]},disabled:{type:Boolean,default:!1},type:{type:String,default:"normal"}},emits:["update:modelValue"],setup(l,{emit:f}){const v=l,P=f,M=[{value:"kdj",label:"KDJ指标",desc:"动量振荡器",icon:"chart-line"},{value:"volume_pressure",label:"成交量压力",desc:"成交量分析",icon:"chart-column"},{value:"macd",label:"MACD指标",desc:"趋势指标",icon:"chart-line-data"},{value:"bollinger",label:"布林带",desc:"波动率指标",icon:"chart-area"}],x=S(()=>[{value:"d",label:"日线",icon:"calendar"},{value:"w",label:"周线",icon:"calendar"},{value:"m",label:"月线",icon:"calendar"}].filter(s=>v.selectedPeriods.includes(s.value))),h=(d,s)=>{var e;return((e=v.modelValue[s])==null?void 0:e.includes(d))||!1},V=(d,s,e)=>{const t={...v.modelValue};t[s]||(t[s]=[]),e?t[s].includes(d)||(t[s]=[...t[s],d]):t[s]=t[s].filter(k=>k!==d),P("update:modelValue",t)},I=d=>x.value.some(e=>h(d,e.value))?"text-primary":"text-gray-400";return(d,s)=>(a(),c("div",{class:y(["period-indicator-matrix",{"period-indicator-matrix--small":l.type==="small"}])},[n("div",j,[s[0]||(s[0]=n("div",{class:"corner-cell"},"指标",-1)),(a(!0),c(o,null,b(x.value,(e,t)=>(a(),c("div",{key:e.value,class:"period-cell"},[n("div",w,[n("div",D,[l.type!=="small"?(a(),m(r,{key:0,name:e.icon,class:"text-sm mr-1"},null,8,["name"])):i("",!0),n("span",null,u(e.label),1),l.scanMode==="multi_period"&&l.selectedPeriods.length>1?(a(),c("span",{key:1,class:y(["role-badge ml-1",t===0?"role-badge--primary":"role-badge--secondary"])},u(t===0?"主":"辅"),3)):i("",!0)])])]))),128))]),(a(),c(o,null,b(M,e=>n("div",{key:e.value,class:"matrix-row"},[n("div",N,[l.type!=="small"?(a(),m(r,{key:0,name:e.icon,class:y(["text-lg mr-2",I(e.value)])},null,8,["name","class"])):i("",!0),n("div",null,[n("div",O,u(e.label),1),l.type!=="small"?(a(),c("div",F,u(e.desc),1)):i("",!0)])]),(a(!0),c(o,null,b(x.value,(t,k)=>(a(),c("div",{key:t.value,class:"checkbox-cell"},[k===0||l.scanMode==="traditional"?(a(),c("label",K,[n("input",{type:"checkbox",checked:h(e.value,t.value),onChange:B=>V(e.value,t.value,B.target.checked),disabled:l.disabled||l.scanMode==="traditional"&&t.value!=="d",class:"sr-only"},null,40,A),n("div",{class:y(["custom-checkbox",{"custom-checkbox--checked":h(e.value,t.value),"custom-checkbox--disabled":l.disabled||l.scanMode==="traditional"&&t.value!=="d"}])},[_(r,{name:"checkmark",class:"check-icon"})],2)])):(a(),c(o,{key:1},[e.value==="kdj"?(a(),c("div",E,[l.type!=="small"?(a(),m(r,{key:0,name:"chart-line-smooth",class:"text-green-400 text-sm"})):i("",!0),n("span",J,u(l.type==="small"?"可用":"上升通道"),1)])):(a(),c("div",L,[l.type!=="small"?(a(),m(r,{key:0,name:"subtract-alt",class:"text-gray-500 text-sm"})):i("",!0),n("span",p,u(l.type==="small"?"-":"不适用"),1)]))],64))]))),128))])),64)),n("div",z,[l.type!=="small"?(a(),m(r,{key:0,name:"information",class:"text-blue-400 mr-2"})):i("",!0),n("span",H,[l.scanMode==="traditional"?(a(),c(o,{key:0},[g(" 传统模式下只能选择日线指标 ")],64)):l.selectedPeriods.length===1?(a(),c(o,{key:1},[g(" 单周期模式：完整指标分析 ")],64)):(a(),c(o,{key:2},[g(" 多周期模式：主周期完整分析，辅助周期仅KDJ上升通道确认 ")],64))])])],2))}},G=C(T,[["__scopeId","data-v-e66f5e40"]]);export{G as P};
