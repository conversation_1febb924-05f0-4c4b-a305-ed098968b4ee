import{r as D,x as M,y as A,B as n,I as K,U as at,L as I,H as O,W as q,w as G,O as _,F as U,K as E,c as st,j as bt,n as lt,aA as wt,Q as St,G as it,aC as rt,M as dt,ab as ct}from"./vendor-Dq0JXR-b.js";import{_ as X,b as L,a as kt}from"./index-BSF2zNx2.js";/* empty css                   *//* empty css                 *//* empty css                        */import{k as Ct,c as $t,l as _t,E as ut}from"./ui-CHSsU9Lc.js";import{s as B}from"./searchStats-Dfdmz4M0.js";import{i as Y}from"./charts-ClPwX7rX.js";const Mt={key:0,class:"chart-overlay"},At={class:"text-center"},Dt={class:"text-red-400 mb-3"},It={key:1,class:"chart-overlay"},Vt={class:"text-center"},Pt={class:"text-gray-400"},zt={key:2,class:"chart-overlay"},Ft={class:"text-center"},Tt={class:"text-gray-400"},jt={__name:"ChartContainer",props:{height:{type:String,default:"400px"},loading:{type:Boolean,default:!1},error:{type:String,default:null},hasData:{type:Boolean,default:!1},emptyText:{type:String,default:"暂无数据"},loadingText:{type:String,default:"加载中..."},emptyIcon:{type:String,default:"i-carbon-chart-line text-blue-400"},showRetry:{type:Boolean,default:!0}},emits:["retry"],setup(u,{expose:t,emit:c}){const m=D();return t({chartRef:m}),(a,s)=>(A(),M("div",{class:"chart-container",style:at({height:u.height})},[n("div",{ref_key:"chartRef",ref:m,class:"w-full h-full",style:at({opacity:u.hasData&&!u.loading&&!u.error?1:0})},null,4),u.error?(A(),M("div",Mt,[n("div",At,[s[1]||(s[1]=n("i",{class:"i-carbon-warning text-4xl mb-3 text-red-400"},null,-1)),n("p",Dt,I(u.error),1),u.showRetry?(A(),M("button",{key:0,class:"btn-primary px-4 py-2 text-sm",onClick:s[0]||(s[0]=l=>a.$emit("retry"))}," 重新加载 ")):K("",!0)])])):!u.hasData&&!u.loading?(A(),M("div",It,[n("div",Vt,[n("i",{class:O([u.emptyIcon,"text-4xl mb-3 opacity-50"])},null,2),n("p",Pt,I(u.emptyText),1)])])):u.loading?(A(),M("div",zt,[n("div",Ft,[s[2]||(s[2]=n("div",{class:"animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-3"},null,-1)),n("p",Tt,I(u.loadingText),1)])])):K("",!0)],4))}},Z=X(jt,[["__scopeId","data-v-c787b755"]]),Kt={class:"indicator-settings"},Lt={class:"settings-content"},Rt={key:0,class:"setting-group"},Ot={class:"setting-row"},Ht={class:"setting-row"},Ut={class:"setting-row"},Bt={key:1,class:"setting-group"},Et={class:"setting-row"},qt={class:"setting-row"},Nt={class:"setting-row"},Wt={key:2,class:"setting-group"},Zt={class:"setting-row"},Jt={key:3,class:"setting-group"},Gt={class:"setting-row"},Xt={class:"setting-row"},Yt={class:"setting-row"},Qt={class:"dialog-footer"},te={__name:"IndicatorSettings",props:{modelValue:{type:Boolean,default:!1},indicatorType:{type:String,default:"macd"},currentSettings:{type:Object,default:()=>({})}},emits:["update:modelValue","confirm"],setup(u,{emit:t}){const c=u,m=t,a=D(c.modelValue),s=q({fastPeriod:12,slowPeriod:26,signalPeriod:9}),l=q({kPeriod:9,dPeriod:3,jPeriod:3}),e=q({period:14}),i=q({ma5:5,ma10:10,ma20:20}),r={macd:{fastPeriod:12,slowPeriod:26,signalPeriod:9},kdj:{kPeriod:9,dPeriod:3,jPeriod:3},rsi:{period:14},ma:{ma5:5,ma10:10,ma20:20}};G(()=>c.modelValue,b=>{a.value=b,b&&o()}),G(a,b=>{m("update:modelValue",b)});const o=()=>{const b=c.currentSettings;switch(c.indicatorType){case"macd":Object.assign(s,{...r.macd,...b});break;case"kdj":Object.assign(l,{...r.kdj,...b});break;case"rsi":Object.assign(e,{...r.rsi,...b});break;case"ma":Object.assign(i,{...r.ma,...b});break}},g=()=>{switch(c.indicatorType){case"macd":Object.assign(s,r.macd);break;case"kdj":Object.assign(l,r.kdj);break;case"rsi":Object.assign(e,r.rsi);break;case"ma":Object.assign(i,r.ma);break}},v=()=>{switch(c.indicatorType){case"macd":return{...s};case"kdj":return{...l};case"rsi":return{...e};case"ma":return{...i};default:return{}}},C=()=>{m("confirm",{type:c.indicatorType,settings:v()}),a.value=!1},y=()=>{a.value=!1};return(b,d)=>{const x=Ct,$=$t,k=_t;return A(),M("div",Kt,[_(k,{modelValue:a.value,"onUpdate:modelValue":d[10]||(d[10]=f=>a.value=f),title:"技术指标参数设置",width:"500px","before-close":y},{footer:U(()=>[n("div",Qt,[_($,{onClick:y},{default:U(()=>d[25]||(d[25]=[E("取消",-1)])),_:1,__:[25]}),_($,{onClick:g},{default:U(()=>d[26]||(d[26]=[E("重置默认",-1)])),_:1,__:[26]}),_($,{type:"primary",onClick:C},{default:U(()=>d[27]||(d[27]=[E("确定",-1)])),_:1,__:[27]})])]),default:U(()=>[n("div",Lt,[u.indicatorType==="macd"?(A(),M("div",Rt,[d[14]||(d[14]=n("h4",{class:"setting-title"},"MACD参数",-1)),n("div",Ot,[d[11]||(d[11]=n("label",null,"快线周期:",-1)),_(x,{modelValue:s.fastPeriod,"onUpdate:modelValue":d[0]||(d[0]=f=>s.fastPeriod=f),min:1,max:100},null,8,["modelValue"])]),n("div",Ht,[d[12]||(d[12]=n("label",null,"慢线周期:",-1)),_(x,{modelValue:s.slowPeriod,"onUpdate:modelValue":d[1]||(d[1]=f=>s.slowPeriod=f),min:1,max:200},null,8,["modelValue"])]),n("div",Ut,[d[13]||(d[13]=n("label",null,"信号线周期:",-1)),_(x,{modelValue:s.signalPeriod,"onUpdate:modelValue":d[2]||(d[2]=f=>s.signalPeriod=f),min:1,max:100},null,8,["modelValue"])])])):K("",!0),u.indicatorType==="kdj"?(A(),M("div",Bt,[d[18]||(d[18]=n("h4",{class:"setting-title"},"KDJ参数",-1)),n("div",Et,[d[15]||(d[15]=n("label",null,"K值周期:",-1)),_(x,{modelValue:l.kPeriod,"onUpdate:modelValue":d[3]||(d[3]=f=>l.kPeriod=f),min:1,max:100},null,8,["modelValue"])]),n("div",qt,[d[16]||(d[16]=n("label",null,"D值周期:",-1)),_(x,{modelValue:l.dPeriod,"onUpdate:modelValue":d[4]||(d[4]=f=>l.dPeriod=f),min:1,max:100},null,8,["modelValue"])]),n("div",Nt,[d[17]||(d[17]=n("label",null,"J值周期:",-1)),_(x,{modelValue:l.jPeriod,"onUpdate:modelValue":d[5]||(d[5]=f=>l.jPeriod=f),min:1,max:100},null,8,["modelValue"])])])):K("",!0),u.indicatorType==="rsi"?(A(),M("div",Wt,[d[20]||(d[20]=n("h4",{class:"setting-title"},"RSI参数",-1)),n("div",Zt,[d[19]||(d[19]=n("label",null,"RSI周期:",-1)),_(x,{modelValue:e.period,"onUpdate:modelValue":d[6]||(d[6]=f=>e.period=f),min:1,max:100},null,8,["modelValue"])])])):K("",!0),u.indicatorType==="ma"?(A(),M("div",Jt,[d[24]||(d[24]=n("h4",{class:"setting-title"},"移动平均线参数",-1)),n("div",Gt,[d[21]||(d[21]=n("label",null,"MA5周期:",-1)),_(x,{modelValue:i.ma5,"onUpdate:modelValue":d[7]||(d[7]=f=>i.ma5=f),min:1,max:100},null,8,["modelValue"])]),n("div",Xt,[d[22]||(d[22]=n("label",null,"MA10周期:",-1)),_(x,{modelValue:i.ma10,"onUpdate:modelValue":d[8]||(d[8]=f=>i.ma10=f),min:1,max:100},null,8,["modelValue"])]),n("div",Yt,[d[23]||(d[23]=n("label",null,"MA20周期:",-1)),_(x,{modelValue:i.ma20,"onUpdate:modelValue":d[9]||(d[9]=f=>i.ma20=f),min:1,max:100},null,8,["modelValue"])])])):K("",!0)])]),_:1},8,["modelValue"])])}}},ee=X(te,[["__scopeId","data-v-7bd255ab"]]),J=(u,t)=>{const c=[];if(u.length<3)return Array(u.length).fill(null);const m=Math.min(t,Math.max(3,Math.floor(u.length/2)));for(let a=0;a<u.length;a++)if(a<m-1){const s=Math.min(a+1,m),l=u.slice(Math.max(0,a-s+1),a+1).reduce((e,i)=>e+i,0);c.push((l/s).toFixed(2))}else{const s=u.slice(a-m+1,a+1).reduce((l,e)=>l+e,0);c.push((s/m).toFixed(2))}return c},oe=(u,t=20,c=2)=>{if(u.length<3)return{middle:Array(u.length).fill(null),upper:Array(u.length).fill(null),lower:Array(u.length).fill(null)};const m=Math.min(t,Math.max(3,Math.floor(u.length/2))),a={middle:[],upper:[],lower:[]};for(let s=0;s<u.length;s++)if(s<m-1){const l=Math.min(s+1,m),e=u.slice(Math.max(0,s-l+1),s+1),i=e.reduce((g,v)=>g+v,0)/e.length,r=e.reduce((g,v)=>g+Math.pow(v-i,2),0)/e.length,o=Math.sqrt(r);a.middle.push(i.toFixed(2)),a.upper.push((i+c*o).toFixed(2)),a.lower.push((i-c*o).toFixed(2))}else{const l=u.slice(s-m+1,s+1),e=l.reduce((o,g)=>o+g,0)/m,i=l.reduce((o,g)=>o+Math.pow(g-e,2),0)/m,r=Math.sqrt(i);a.middle.push(e.toFixed(2)),a.upper.push((e+c*r).toFixed(2)),a.lower.push((e-c*r).toFixed(2))}return a},H=u=>u?u>=1e8?(u/1e8).toFixed(2)+"亿":u>=1e4?(u/1e4).toFixed(2)+"万":u.toString():"0",ne=u=>u?u>=1e8?(u/1e8).toFixed(2)+"亿":u.toString():"0",pt=u=>({D:"D",D1:"D",W:"W",W1:"W",M:"M",M1:"M"})[u]||"D",ae=u=>({ma5:Math.min(5,Math.max(3,Math.floor(u/6))),ma10:Math.min(10,Math.max(5,Math.floor(u/3))),ma20:Math.min(20,Math.max(10,Math.floor(u/2)))});class se{constructor(){this.loading=!1,this.error=null,this.chartData=null,this.stockInfo=null}isIndexCode(t){return[/^000001\.SH$/,/^399001\.SZ$/,/^399006\.SZ$/,/^000300\.SH$/,/^000905\.SH$/,/^000688\.SH$/].some(m=>m.test(t))}async loadStockAnalysis(t,c,m){if(t)return this.isIndexCode(t)?(console.log("检测到指数代码，使用指数数据处理:",t),await this.loadIndexAnalysis(t,c,m)):(console.log("检测到股票代码，使用股票数据处理:",t),await this.loadStockAnalysisInternal(t,c,m))}async loadStockAnalysisInternal(t,c,m){var a,s,l;console.log("开始加载股票分析数据:",t),this.loading=!0,this.error=null;try{const e=new Date,i=new Date;i.setDate(e.getDate()-parseInt(m));const r=i.toISOString().split("T")[0],o=e.toISOString().split("T")[0],g=pt(c),[v,C]=await Promise.all([L.stockData.getKlineData(t,g,{start_date:r,end_date:o,with_indicators:!0,indicators:["macd"]}).then(y=>y.data).catch(y=>(console.warn("K线数据获取失败，使用备用方案:",y),L.stockData.getKlineData(t,g,{start_date:r,end_date:o}))),L.indicator.getIndicator(t,"macd",{start_date:r,end_date:o,freq:g}).then(y=>y.data).catch(y=>(console.warn("MACD指标获取失败:",y),null))]);if(!v||!v.kline_data)throw new Error("无法获取K线数据");return this.chartData={kline:v,macd:C||((a=v.indicators)==null?void 0:a.macd),volume:v.kline_data},await this.loadStockInfo(t,v),console.log("股票分析数据加载成功",{klineData:this.chartData.kline,macdData:this.chartData.macd,volumeData:this.chartData.volume,stockInfo:this.stockInfo}),this.chartData}catch(e){throw console.error("加载股票分析数据失败:",e),this.error=((l=(s=e.response)==null?void 0:s.data)==null?void 0:l.message)||e.message||"加载股票分析数据失败",e}finally{this.loading=!1}}async loadStockInfo(t,c=null){var m;try{console.log("开始获取股票基本信息:",t);let a="未知股票",s=null;try{const{data:l}=await L.stockList.getStockInfo(t);console.log("股票基本信息:",l),a=l.name||"未知股票",s=l.total_shares}catch(l){console.warn("获取股票基本信息失败，使用默认名称:",l);const i=[{code:"000001",name:"平安银行"},{code:"000002",name:"万科A"},{code:"000858",name:"五粮液"},{code:"002230",name:"科大讯飞"},{code:"002415",name:"海康威视"},{code:"600000",name:"浦发银行"},{code:"600036",name:"招商银行"},{code:"600519",name:"贵州茅台"},{code:"600887",name:"伊利股份"},{code:"300072",name:"三聚环保"}].find(r=>r.code===t);a=(i==null?void 0:i.name)||"未知股票"}if(c&&c.kline_data&&c.kline_data.length>0){const l=c.kline_data,e=l[l.length-1],i=l.length>1?l[l.length-2]:null;let r=0,o=0;e.change_pct!==void 0&&e.change_pct!==null?(r=parseFloat(e.change_pct),i&&i.close&&(o=e.close-i.close)):i&&i.close&&e.close&&(o=e.close-i.close,r=o/i.close*100);let g=null;s&&e.close&&(g=s*e.close),this.stockInfo={code:t,name:a,price:((m=e.close)==null?void 0:m.toFixed(2))||"0.00",changePercent:r.toFixed(2),changeAmount:o.toFixed(2),volume:H(e.volume),marketCap:ne(g)},console.log("从K线数据提取的股票信息:",this.stockInfo,"原始change_pct:",e.change_pct)}else this.stockInfo={code:t,name:a,price:"0.00",changePercent:"0.00",changeAmount:"0.00",volume:"0万手",marketCap:"0亿"},console.log("使用默认股票信息:",this.stockInfo)}catch(a){console.error("获取股票信息失败:",a),this.stockInfo={code:t,name:"未知股票",price:"0.00",changePercent:"0.00",changeAmount:"0.00",volume:"0万手",marketCap:"0亿"}}}async loadHotStocks(){try{return(await L.stockSearch.getPopularStocks({limit:8})).data||[]}catch(t){return console.error("获取热门股票失败:",t),[{code:"000001",name:"平安银行",industry:"银行"},{code:"000002",name:"万科A",industry:"房地产"},{code:"000858",name:"五粮液",industry:"白酒"},{code:"002230",name:"科大讯飞",industry:"人工智能"},{code:"002415",name:"海康威视",industry:"安防"},{code:"600036",name:"招商银行",industry:"银行"},{code:"600519",name:"贵州茅台",industry:"白酒"},{code:"600887",name:"伊利股份",industry:"乳业"}]}}async loadIndexAnalysis(t,c,m){var a,s,l;console.log("开始加载指数分析数据:",t),this.loading=!0,this.error=null;try{const e=new Date,i=new Date;i.setDate(e.getDate()-parseInt(m));const r=i.toISOString().split("T")[0],o=e.toISOString().split("T")[0],g=pt(c),[v,C]=await Promise.all([L.indexData.getKlineData(t,g,{start_date:r,end_date:o,with_indicators:!0,indicators:["macd"]}).then(y=>y.data).catch(y=>(console.warn("指数K线数据获取失败，使用备用方案:",y),L.indexData.getKlineData(t,g,{start_date:r,end_date:o}))),L.indexIndicator.getIndicator(t,"macd",{start_date:r,end_date:o,freq:g}).then(y=>y.data).catch(y=>(console.warn("指数MACD指标获取失败:",y),null))]);if(!v||!v.kline_data)throw new Error("无法获取指数K线数据");return this.chartData={kline:v,macd:C||((a=v.indicators)==null?void 0:a.macd),volume:v.kline_data},await this.loadIndexInfo(t,v),console.log("指数分析数据加载成功",{klineData:this.chartData.kline,macdData:this.chartData.macd,volumeData:this.chartData.volume,stockInfo:this.stockInfo}),this.chartData}catch(e){throw console.error("加载指数分析数据失败:",e),this.error=((l=(s=e.response)==null?void 0:s.data)==null?void 0:l.message)||e.message||"加载指数分析数据失败",e}finally{this.loading=!1}}async loadIndexInfo(t,c=null){var m,a;try{console.log("开始获取指数基本信息:",t);let s="未知指数";try{const l=await L.indexData.getRealTimeData(t);console.log("指数基本信息:",l),s=((m=l.data)==null?void 0:m.name)||"未知指数"}catch(l){console.warn("获取指数基本信息失败，使用默认名称:",l);const i=[{code:"000001.SH",name:"上证指数"},{code:"399001.SZ",name:"深证成指"},{code:"399006.SZ",name:"创业板指"},{code:"000300.SH",name:"沪深300"},{code:"000905.SH",name:"中证500"},{code:"000688.SH",name:"科创50"}].find(r=>r.code===t);s=(i==null?void 0:i.name)||"未知指数"}if(c&&c.kline_data&&c.kline_data.length>0){const l=c.kline_data,e=l[l.length-1],i=l.length>1?l[l.length-2]:null;let r=0,o=0;e.change_pct!==void 0&&e.change_pct!==null?(r=parseFloat(e.change_pct),i&&i.close&&(o=e.close-i.close)):i&&i.close&&e.close&&(o=e.close-i.close,r=o/i.close*100),this.stockInfo={code:t,name:s,price:((a=e.close)==null?void 0:a.toFixed(2))||"0.00",changePercent:r.toFixed(2),changeAmount:o.toFixed(2),volume:H(e.volume),marketCap:"-"},console.log("从K线数据提取的指数信息:",this.stockInfo,"原始change_pct:",e.change_pct)}else this.stockInfo={code:t,name:s,price:"0.00",changePercent:"0.00",changeAmount:"0.00",volume:"0万手",marketCap:"-"},console.log("使用默认指数信息:",this.stockInfo)}catch(s){console.error("获取指数信息失败:",s),this.stockInfo={code:t,name:"未知指数",price:"0.00",changePercent:"0.00",changeAmount:"0.00",volume:"0万手",marketCap:"-"}}}getChartData(){return this.chartData}getStockInfo(){return this.stockInfo}getError(){return this.error}isLoading(){return this.loading}}const Q=()=>({trigger:"axis",show:!0,backgroundColor:"rgba(0, 0, 0, 0.9)",borderColor:"#4a90e2",borderWidth:1,borderRadius:8,textStyle:{color:"#fff",fontSize:12,lineHeight:18},padding:[12,16],extraCssText:"max-width: 300px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5); z-index: 9999;",confine:!0,position:function(u,t,c,m,a){const s=a.contentSize[0],l=a.contentSize[1],e=a.viewSize[0];a.viewSize[1];let i=u[0]+10,r=u[1]-l-10;return i+s>e&&(i=u[0]-s-10),r<0&&(r=u[1]+10),[i,r]}}),le=(u,t,c,m,a,s,l,e)=>function(i){try{const r=i[0],o=r.dataIndex,g=t[o];if(!g)return"";const[v,C,y,b]=g;let d=0;if(o>0){const V=t[o-1][1];d=C-V}else d=C-v;let x=0;const $=u[o];if($&&$.change_pct!==void 0&&$.change_pct!==null)x=parseFloat($.change_pct).toFixed(2);else if(o>0){const V=t[o-1][1];x=((C-V)/V*100).toFixed(2)}else x=(d/v*100).toFixed(2);const k=parseFloat(x)>=0,f=($==null?void 0:$.volume)||0;let P=`
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${r.axisValue}</div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">开盘价</div>
            <div style="color: #fff; font-weight: bold;">¥${v}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">收盘价</div>
            <div style="color: ${k?"#ef4444":"#22c55e"}; font-weight: bold;">¥${C}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">最高价</div>
            <div style="color: #fff; font-weight: bold;">¥${b}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">最低价</div>
            <div style="color: #fff; font-weight: bold;">¥${y}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">涨跌额</div>
            <div style="color: ${k?"#ef4444":"#22c55e"}; font-weight: bold;">
              ${k?"+":""}${d.toFixed(2)}
            </div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">涨跌幅</div>
            <div style="color: ${k?"#ef4444":"#22c55e"}; font-weight: bold;">
              ${k?"+":""}${x}%
            </div>
          </div>
        </div>
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #999; font-size: 11px;">成交量</div>
          <div style="color: #4ecdc4; font-weight: bold;">${H(f)}</div>
        </div>
      `;if(e==="ma"){const V=c[o],S=m[o],F=a[o];P+=`
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px; margin-bottom: 4px;">移动平均线</div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
              ${V?`<div><span style="color: #ff6b6b;">MA${l.ma5}:</span> <span style="color: #fff;">¥${V}</span></div>`:""}
              ${S?`<div><span style="color: #4ecdc4;">MA${l.ma10}:</span> <span style="color: #fff;">¥${S}</span></div>`:""}
              ${F?`<div><span style="color: #45b7d1;">MA${l.ma20}:</span> <span style="color: #fff;">¥${F}</span></div>`:""}
            </div>
          </div>
        `}if(e==="bollinger"){const V=s.upper[o],S=s.middle[o],F=s.lower[o];P+=`
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px; margin-bottom: 4px;">布林带</div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
              ${V?`<div><span style="color: #ff6b6b;">上轨:</span> <span style="color: #fff;">¥${V}</span></div>`:""}
              ${S?`<div><span style="color: #4ecdc4;">中轨:</span> <span style="color: #fff;">¥${S}</span></div>`:""}
              ${F?`<div><span style="color: #22c55e;">下轨:</span> <span style="color: #fff;">¥${F}</span></div>`:""}
            </div>
          </div>
        `}return P}catch(r){return console.error("K线图表 tooltip 格式化错误:",r),'<div style="color: #fff;">数据加载中...</div>'}},ie=(u,t,c,m)=>function(a){try{if(!a||a.length===0)return"";const s=a[0].axisValue,l=a[0].dataIndex,e=t[l],i=c[l],r=m[l],o=r>=0;let g=`
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${s}</div>
          <div style="color: #999; font-size: 11px;">MACD 指标</div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">DIF (快线)</div>
            <div style="color: #ff6b6b; font-weight: bold;">${(e==null?void 0:e.toFixed(4))||"0.0000"}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">DEA (慢线)</div>
            <div style="color: #4ecdc4; font-weight: bold;">${(i==null?void 0:i.toFixed(4))||"0.0000"}</div>
          </div>
        </div>
        <div style="border-top: 1px solid #333; padding-top: 8px;">
          <div style="color: #999; font-size: 11px;">HISTOGRAM (柱状线)</div>
          <div style="color: ${o?"#ef4444":"#22c55e"}; font-weight: bold;">
            ${o?"+":""}${(r==null?void 0:r.toFixed(4))||"0.0000"}
          </div>
        </div>
      `;if(e&&i){const v=e>i?"多头信号":"空头信号",C=e>i?"#ef4444":"#22c55e";g+=`
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px;">趋势信号</div>
            <div style="color: ${C}; font-weight: bold;">${v}</div>
          </div>
        `}return g}catch(s){return console.error("MACD 图表 tooltip 格式化错误:",s),'<div style="color: #fff;">数据加载中...</div>'}},re=u=>function(t){try{const c=t[0],m=c.dataIndex,a=u[m];if(!a)return"";const s=m>0?u[m-1]:null,l=s?a.volume-s.volume:0,e=s?l/s.volume*100:0,r=a.close-a.open>=0,o=(a.high+a.low+a.close+a.open)/4,g=a.volume*o;let v=`
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${c.axisValue}</div>
          <div style="color: #999; font-size: 11px;">成交量分析</div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">成交量</div>
            <div style="color: #4ecdc4; font-weight: bold;">${H(a.volume)}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">成交金额</div>
            <div style="color: #ff9f43; font-weight: bold;">${H(g)}元</div>
          </div>
        </div>
      `;if(s){const x=l>=0;v+=`
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
              <div>
                <div style="color: #999; font-size: 11px;">量比变化</div>
                <div style="color: ${x?"#ef4444":"#22c55e"}; font-weight: bold;">
                  ${x?"+":""}${H(l)}
                </div>
              </div>
              <div>
                <div style="color: #999; font-size: 11px;">量比幅度</div>
                <div style="color: ${x?"#ef4444":"#22c55e"}; font-weight: bold;">
                  ${x?"+":""}${e.toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        `}const b=`${a.volume>((s==null?void 0:s.volume)||0)?"放量":"缩量"}${r?"上涨":"下跌"}`;let d="#4ecdc4";return b==="放量上涨"?d="#ef4444":b==="放量下跌"?d="#22c55e":b==="缩量上涨"?d="#ff9f43":b==="缩量下跌"&&(d="#9b59b6"),v+=`
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #999; font-size: 11px;">量价关系</div>
          <div style="color: ${d}; font-weight: bold;">${b}</div>
        </div>
      `,v}catch(c){return console.error("成交量图表 tooltip 格式化错误:",c),'<div style="color: #fff;">数据加载中...</div>'}};class de{constructor(t,c){this.chartContainer=t,this.getIndicatorSettings=c,this.chart=null}render(t,c){var d,x,$,k,f,P,V;if(console.log("K线图表渲染器开始渲染:",{container:this.chartContainer,chartRef:(d=this.chartContainer)==null?void 0:d.chartRef,chartRefValue:($=(x=this.chartContainer)==null?void 0:x.chartRef)==null?void 0:$.value,klineData:t}),!((k=this.chartContainer)!=null&&k.chartRef)||!t){console.warn("K线图表渲染条件不满足:",{hasContainer:!!this.chartContainer,hasChartRef:!!((f=this.chartContainer)!=null&&f.chartRef),hasChartRefValue:!!((V=(P=this.chartContainer)==null?void 0:P.chartRef)!=null&&V.value),hasKlineData:!!t});return}this.chart&&this.chart.dispose();const m=this.chartContainer.chartRef;console.log("初始化图表，DOM元素:",m),this.chart=Y(m);const a=t.kline_data||[];if(a.length===0){console.warn("K线数据为空");return}const s=a.map(S=>{const F=S.date||S.trade_date;return F.includes("T")?F.split("T")[0]:F}),l=a.map(S=>[S.open,S.close,S.low,S.high]),e=a.map(S=>S.close),i=e.length,r=ae(i),o=J(e,r.ma5),g=J(e,r.ma10),v=J(e,r.ma20),C=this.getIndicatorSettings("bollinger"),y=oe(e,C.period||20,C.stdDev||2),b={animation:!0,backgroundColor:"transparent",grid:{left:"50px",right:"50px",top:"60px",bottom:"60px"},xAxis:{type:"category",data:s,axisLine:{lineStyle:{color:"#666"}},axisLabel:{color:"#999"}},yAxis:{type:"value",scale:!0,axisLine:{lineStyle:{color:"#666"}},axisLabel:{color:"#999"},splitLine:{lineStyle:{color:"#333"}}},tooltip:{...Q(),formatter:le(a,l,o,g,v,y,r,c)},series:[{name:"K线",type:"candlestick",data:l,itemStyle:{color:"#ef4444",color0:"#22c55e",borderColor:"#ef4444",borderColor0:"#22c55e"}}]};if(c==="ma"&&b.series.push({name:`MA${r.ma5}`,type:"line",data:o,smooth:!0,lineStyle:{color:"#ff6b6b",width:1},showSymbol:!1},{name:`MA${r.ma10}`,type:"line",data:g,smooth:!0,lineStyle:{color:"#4ecdc4",width:1},showSymbol:!1},{name:`MA${r.ma20}`,type:"line",data:v,smooth:!0,lineStyle:{color:"#45b7d1",width:1},showSymbol:!1}),c==="bollinger"){const S=Math.min(C.period||20,Math.max(3,Math.floor(i/2)));b.series.push({name:`布林上轨(${S})`,type:"line",data:y.upper,smooth:!0,lineStyle:{color:"#ff6b6b",width:1,type:"dashed"},showSymbol:!1},{name:`布林中轨(${S})`,type:"line",data:y.middle,smooth:!0,lineStyle:{color:"#4ecdc4",width:1},showSymbol:!1},{name:`布林下轨(${S})`,type:"line",data:y.lower,smooth:!0,lineStyle:{color:"#22c55e",width:1,type:"dashed"},showSymbol:!1})}this.chart.setOption(b),console.log("K线图表选项设置完成")}resize(){this.chart&&this.chart.resize()}dispose(){this.chart&&(this.chart.dispose(),this.chart=null)}}class ce{constructor(t){this.chartContainer=t,this.chart=null}render(t){var r;if(!((r=this.chartContainer)!=null&&r.chartRef)||!t){console.warn("MACD图表渲染条件不满足");return}this.chart&&this.chart.dispose();const c=this.chartContainer.chartRef;this.chart=Y(c);let m=[],a=[],s=[],l=[];const e=o=>o&&o.includes("T")?o.split("T")[0]:o;t.data&&Array.isArray(t.data)?(m=t.data.map(o=>e(o.date)),a=t.data.map(o=>parseFloat(o.MACD_12_26_9||o.dif||o.diff||0)),s=t.data.map(o=>parseFloat(o.MACDs_12_26_9||o.dea||o.signal||0)),l=t.data.map(o=>parseFloat(o.MACDh_12_26_9||o.histogram||o.hist||0))):t.date&&Array.isArray(t.date)?(m=t.date.map(e),a=t.dif||t.diff||[],s=t.dea||t.signal||[],l=t.macd||t.histogram||t.hist||[]):Array.isArray(t)&&(m=t.map(o=>e(o.date)),a=t.map(o=>parseFloat(o.MACD_12_26_9||o.dif||o.diff||0)),s=t.map(o=>parseFloat(o.MACDs_12_26_9||o.dea||o.signal||0)),l=t.map(o=>parseFloat(o.MACDh_12_26_9||o.histogram||o.hist||0)));const i={animation:!0,backgroundColor:"transparent",grid:{left:"50px",right:"50px",top:"30px",bottom:"50px"},xAxis:{type:"category",data:m,axisLine:{lineStyle:{color:"#666"}},axisLabel:{color:"#999",fontSize:11}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#666"}},axisLabel:{color:"#999",fontSize:11},splitLine:{lineStyle:{color:"#333"}}},tooltip:{...Q(),formatter:ie(m,a,s,l)},legend:{textStyle:{color:"#999"},top:5},series:[{name:"DIF",type:"line",data:a,lineStyle:{color:"#ff6b6b",width:1},showSymbol:!1},{name:"DEA",type:"line",data:s,lineStyle:{color:"#4ecdc4",width:1},showSymbol:!1},{name:"HISTOGRAM",type:"bar",data:l,itemStyle:{color:function(o){return o.value>=0?"#ef4444":"#22c55e"}}}]};this.chart.setOption(i)}resize(){this.chart&&this.chart.resize()}dispose(){this.chart&&(this.chart.dispose(),this.chart=null)}}class ue{constructor(t){this.chartContainer=t,this.chart=null}render(t){var l;if(!((l=this.chartContainer)!=null&&l.chartRef)||!t){console.warn("成交量图表渲染条件不满足");return}this.chart&&this.chart.dispose();const c=this.chartContainer.chartRef;this.chart=Y(c);const m=t.map(e=>{const i=e.date;return i&&i.includes("T")?i.split("T")[0]:i}),a=t.map(e=>e.volume);if(m.length===0||a.length===0){console.warn("成交量数据为空");return}const s={animation:!0,backgroundColor:"transparent",grid:{left:"50px",right:"50px",top:"30px",bottom:"50px"},xAxis:{type:"category",data:m,axisLine:{lineStyle:{color:"#666"}},axisLabel:{color:"#999",fontSize:11}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#666"}},axisLabel:{color:"#999",fontSize:11,formatter:function(e){return e>=1e8?(e/1e8).toFixed(1)+"亿":e>=1e4?(e/1e4).toFixed(1)+"万":e}},splitLine:{lineStyle:{color:"#333"}}},tooltip:{...Q(),formatter:re(t)},series:[{name:"成交量",type:"bar",data:a,itemStyle:{color:function(e){const i=e.dataIndex;if(i===0)return"#4ecdc4";const r=t[i],o=t[i-1];return r.close>=o.close?"#ef4444":"#22c55e"}}}]};this.chart.setOption(s)}resize(){this.chart&&this.chart.resize()}dispose(){this.chart&&(this.chart.dispose(),this.chart=null)}}const pe={class:"analysis-page"},me={class:"grid grid-cols-1 xl:grid-cols-4 gap-6"},he={class:"xl:col-span-1"},fe={class:"card p-6 mb-6"},ge={class:"space-y-4"},ve={label:"大盘指数"},ye=["value"],xe={key:0,label:"热门推荐"},be=["value"],we={key:0},Se=["value"],ke=["disabled"],Ce={key:0,class:"card p-6"},$e={class:"space-y-3"},_e={class:"flex justify-between"},Me={class:"flex justify-between"},Ae={class:"flex justify-between"},De={class:"flex justify-between"},Ie={class:"flex justify-between"},Ve={class:"flex justify-between"},Pe={class:"xl:col-span-3"},ze={class:"card p-6 mb-6"},Fe={class:"flex items-center justify-between mb-4"},Te={class:"flex space-x-2"},je={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ke={class:"card p-6"},Le={class:"flex items-center justify-between mb-4"},Re={class:"card p-6"},Oe={__name:"index",setup(u){const t=wt(),c=D(""),m=D("D"),a=D("120"),s=D("bollinger"),l=new se,e=D(!1),i=D(null),r=D(null),o=D(null),g=()=>{e.value=l.isLoading(),i.value=l.getError(),r.value=l.getChartData(),o.value=l.getStockInfo()},v=D([]),C=st(()=>B.getPopularStocks(5)),y=st(()=>{const h=C.value,p=v.value,z=[...h],j=new Set(h.map(T=>T.code));for(const T of p)if(!j.has(T.code)&&(z.push({code:T.code,name:T.name,industry:T.industry||"未分类",searchCount:0}),z.length>=10))break;return z}),b=D([{code:"000001.SH",name:"上证指数"},{code:"399001.SZ",name:"深证成指"},{code:"399006.SZ",name:"创业板指"},{code:"000300.SH",name:"沪深300"},{code:"000905.SH",name:"中证500"},{code:"000688.SH",name:"科创50"}]),d=D(null),x=D(null),$=D(null);let k=null,f=null,P=null;const V=D(!1),S=D("macd"),F=D({}),tt=h=>{const p=localStorage.getItem(`indicator_settings_${h}`);return p?JSON.parse(p):{}},mt=h=>{if(typeof h=="object"&&h.target&&(h=h.target.value),!h)return;c.value=h;const p=y.value.find(z=>z.code===h);p&&B.incrementSearchCount(p.code,p.name,p.industry||"未分类"),c.value&&R()},ht=()=>{c.value&&R()},N=h=>{s.value=h,W()},ft=h=>{S.value=h,F.value=tt(h),V.value=!0},gt=h=>{localStorage.setItem(`indicator_settings_${h.type}`,JSON.stringify(h.settings)),c.value&&(["ma","bollinger"].includes(h.type)?W():R())},R=async()=>{if(c.value)try{console.log("开始加载分析数据",c.value),await l.loadStockAnalysis(c.value,m.value,a.value),g(),console.log("数据加载完成，chartData:",r.value),console.log("图表容器状态:",{kline:!!d.value,macd:!!x.value,volume:!!$.value}),await lt(),console.log("nextTick 后，开始渲染图表"),await vt()}catch(h){console.error("加载分析数据失败:",h),g(),ut.error(h.message||"加载失败")}},vt=async()=>{if(r.value)try{await new Promise(h=>setTimeout(h,100)),await Promise.all([W(),yt(),xt()])}catch(h){console.error("图表渲染失败:",h),ut.error("图表渲染失败")}},W=async()=>{var h,p,z;if(console.log("准备渲染K线图表:",{containerExists:!!d.value,hasData:!!((h=r.value)!=null&&h.kline),data:(p=r.value)==null?void 0:p.kline}),!d.value){console.warn("K线图表容器未找到");return}if(!((z=r.value)!=null&&z.kline)){console.warn("K线数据为空");return}try{k||(console.log("创建K线渲染器"),k=new de(d.value,tt)),console.log("开始渲染K线图表",r.value.kline),k.render(r.value.kline,s.value),console.log("K线图表渲染完成")}catch(j){console.error("K线图表渲染失败:",j)}},yt=async()=>{var h;if(!x.value){console.warn("MACD图表容器未找到");return}if(!((h=r.value)!=null&&h.macd)){console.warn("MACD数据为空");return}try{f||(f=new ce(x.value)),console.log("开始渲染MACD图表",r.value.macd),f.render(r.value.macd),console.log("MACD图表渲染完成")}catch(p){console.error("MACD图表渲染失败:",p)}},xt=async()=>{var h;if(!$.value){console.warn("成交量图表容器未找到");return}if(!((h=r.value)!=null&&h.volume)){console.warn("成交量数据为空");return}try{P||(P=new ue($.value)),console.log("开始渲染成交量图表",r.value.volume),P.render(r.value.volume),console.log("成交量图表渲染完成")}catch(p){console.error("成交量图表渲染失败:",p)}},et=()=>{k==null||k.resize(),f==null||f.resize(),P==null||P.resize()},ot=async(h,p=null,z=null)=>{if(h){if(c.value=h,p)B.incrementSearchCount(h,p,z||"未分类");else{const j=y.value.find(T=>T.code===h);j?B.incrementSearchCount(h,j.name,j.industry||"未分类"):B.incrementSearchCount(h,h,"未分类")}await R()}};return G(()=>t.query,async(h,p)=>{h.stock&&h.stock!==(p==null?void 0:p.stock)&&await ot(h.stock,h.name,h.industry)},{immediate:!0,deep:!0}),bt(async()=>{v.value=await l.loadHotStocks(),g(),await lt(),t.query.stock&&!c.value&&await ot(t.query.stock,t.query.name,t.query.industry),window.addEventListener("resize",et)}),St(()=>{window.removeEventListener("resize",et),k==null||k.dispose(),f==null||f.dispose(),P==null||P.dispose()}),(h,p)=>{var z,j,T,nt;return A(),M("div",pe,[n("div",me,[n("div",he,[n("div",fe,[p[11]||(p[11]=n("h3",{class:"text-lg font-semibold mb-4"},"股票选择",-1)),n("div",ge,[n("div",null,[p[8]||(p[8]=n("label",{class:"block text-sm text-gray-400 mb-2"},"股票代码",-1)),it(n("select",{"onUpdate:modelValue":p[0]||(p[0]=w=>c.value=w),class:"input-field w-full",onChange:mt},[p[7]||(p[7]=n("option",{value:""},"选择股票或指数",-1)),n("optgroup",ve,[(A(!0),M(dt,null,ct(b.value,w=>(A(),M("option",{key:"index-"+w.code,value:w.code},I(w.code)+" "+I(w.name),9,ye))),128))]),y.value.length>0?(A(),M("optgroup",xe,[(A(!0),M(dt,null,ct(y.value,w=>(A(),M("option",{key:w.code,value:w.code},[E(I(w.code)+" "+I(w.name)+" ",1),w.searchCount>0?(A(),M("span",we,"("+I(w.searchCount)+"次搜索)",1)):K("",!0)],8,be))),128))])):K("",!0),c.value&&!y.value.some(w=>w.code===c.value)?(A(),M("option",{key:1,value:c.value,selected:!0},I(c.value)+" "+I(((z=o.value)==null?void 0:z.name)||"加载中..."),9,Se)):K("",!0)],544),[[rt,c.value]])]),n("div",null,[p[10]||(p[10]=n("label",{class:"block text-sm text-gray-400 mb-2"},"时间范围",-1)),it(n("select",{"onUpdate:modelValue":p[1]||(p[1]=w=>a.value=w),class:"input-field w-full",onChange:ht},p[9]||(p[9]=[n("option",{value:"30"},"最近30天",-1),n("option",{value:"60"},"最近60天",-1),n("option",{value:"120"},"最近120天",-1),n("option",{value:"250"},"最近250天",-1)]),544),[[rt,a.value]])]),n("button",{class:"btn-primary w-full flex items-center justify-center",onClick:R,disabled:!c.value||e.value},[_(kt,{name:"chart-line",class:"mr-2"}),E(" "+I(e.value?"分析中...":"开始分析"),1)],8,ke)])]),o.value?(A(),M("div",Ce,[p[18]||(p[18]=n("h3",{class:"text-lg font-semibold mb-4"},"基本信息",-1)),n("div",$e,[n("div",_e,[p[12]||(p[12]=n("span",{class:"text-gray-400"},"股票名称",-1)),n("span",null,I(o.value.name),1)]),n("div",Me,[p[13]||(p[13]=n("span",{class:"text-gray-400"},"股票代码",-1)),n("span",null,I(o.value.code),1)]),n("div",Ae,[p[14]||(p[14]=n("span",{class:"text-gray-400"},"当前价格",-1)),n("span",{class:O(o.value.changePercent>=0?"text-red-400":"text-green-400")}," ¥"+I(o.value.price),3)]),n("div",De,[p[15]||(p[15]=n("span",{class:"text-gray-400"},"涨跌幅",-1)),n("span",{class:O(o.value.changePercent>=0?"text-red-400":"text-green-400")},I(o.value.changePercent>=0?"+":"")+I(o.value.changePercent)+"% ",3)]),n("div",Ie,[p[16]||(p[16]=n("span",{class:"text-gray-400"},"成交量",-1)),n("span",null,I(o.value.volume),1)]),n("div",Ve,[p[17]||(p[17]=n("span",{class:"text-gray-400"},"市值",-1)),n("span",null,I(o.value.marketCap),1)])])])):K("",!0)]),n("div",Pe,[n("div",ze,[n("div",Fe,[p[19]||(p[19]=n("h3",{class:"text-lg font-semibold"},"K线图表",-1)),n("div",Te,[n("button",{class:O(["px-3 py-1 text-sm rounded",s.value==="none"?"bg-blue-600 text-white":"text-gray-400 hover:text-white"]),onClick:p[2]||(p[2]=w=>N("none"))}," 主图 ",2),n("button",{class:O(["px-3 py-1 text-sm rounded",s.value==="ma"?"bg-blue-600 text-white":"text-gray-400 hover:text-white"]),onClick:p[3]||(p[3]=w=>N("ma"))}," 均线 ",2),n("button",{class:O(["px-3 py-1 text-sm rounded",s.value==="bollinger"?"bg-blue-600 text-white":"text-gray-400 hover:text-white"]),onClick:p[4]||(p[4]=w=>N("bollinger"))}," 布林带 ",2)])]),_(Z,{ref_key:"klineChartContainer",ref:d,height:"500px",loading:e.value,error:i.value,"has-data":!!((j=r.value)!=null&&j.kline)&&!!c.value,"empty-text":"请选择股票开始分析","loading-text":"K线图表加载中...","empty-icon":"i-carbon-chart-candlestick text-blue-400",onRetry:R},null,8,["loading","error","has-data"])]),n("div",je,[n("div",Ke,[n("div",Le,[p[20]||(p[20]=n("h3",{class:"text-lg font-semibold"},"MACD指标",-1)),n("button",{class:"text-sm text-blue-400 hover:text-blue-300",onClick:p[5]||(p[5]=w=>ft("macd"))}," 参数设置 ")]),_(Z,{ref_key:"macdChartContainer",ref:x,height:"300px",loading:e.value,error:i.value,"has-data":!!((T=r.value)!=null&&T.macd)&&!!c.value,"empty-text":"请选择股票开始分析","loading-text":"MACD指标加载中...","empty-icon":"i-carbon-chart-line text-purple-400",onRetry:R},null,8,["loading","error","has-data"])]),n("div",Re,[p[21]||(p[21]=n("div",{class:"flex items-center justify-between mb-4"},[n("h3",{class:"text-lg font-semibold"},"成交量"),n("button",{class:"text-sm text-blue-400 hover:text-blue-300"}," 详细分析 ")],-1)),_(Z,{ref_key:"volumeChartContainer",ref:$,height:"300px",loading:e.value,error:i.value,"has-data":!!((nt=r.value)!=null&&nt.volume)&&!!c.value,"empty-text":"请选择股票开始分析","loading-text":"成交量图表加载中...","empty-icon":"i-carbon-chart-column text-green-400",onRetry:R},null,8,["loading","error","has-data"])])])])]),_(ee,{modelValue:V.value,"onUpdate:modelValue":p[6]||(p[6]=w=>V.value=w),"indicator-type":S.value,"current-settings":F.value,onConfirm:gt},null,8,["modelValue","indicator-type","current-settings"])])}}},Je=X(Oe,[["__scopeId","data-v-192d2b68"]]);export{Je as default};
