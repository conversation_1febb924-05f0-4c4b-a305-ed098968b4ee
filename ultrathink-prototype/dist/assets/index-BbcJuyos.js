import{x as o,B as e,O as n,F as r,K as a,ae as l,y as i}from"./vendor-Dq0JXR-b.js";const x={class:"page-container flex-center min-h-96"},d={class:"text-center"},c={__name:"index",setup(m){return(p,t)=>{const s=l("router-link");return i(),o("div",x,[e("div",d,[t[1]||(t[1]=e("div",{class:"text-6xl mb-4"},"404",-1)),t[2]||(t[2]=e("h1",{class:"text-2xl font-bold mb-2"},"页面未找到",-1)),t[3]||(t[3]=e("p",{class:"text-text-muted mb-6"},"抱歉，您访问的页面不存在。",-1)),n(s,{to:"/",class:"btn-primary"},{default:r(()=>t[0]||(t[0]=[a(" 返回首页 ",-1)])),_:1,__:[0]})])])}}};export{c as default};
