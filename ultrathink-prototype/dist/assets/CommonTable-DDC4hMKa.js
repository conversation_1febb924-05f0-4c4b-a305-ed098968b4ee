import{_ as N}from"./index-BSF2zNx2.js";/* empty css                    */import{C as P}from"./CommonPagination-IzNmwDoz.js";import{C as $,B as F}from"./ui-CHSsU9Lc.js";import{r as u,w as c,x as g,y as n,O as T,E as l,I as p,F as h,M as b,ab as A,z as w,ad as E,L as m,A as H}from"./vendor-Dq0JXR-b.js";const M={class:"common-table-container"},O={key:1},R={key:2},W={__name:"CommonTable",props:{data:{type:Array,default:()=>[]},columns:{type:Array,required:!0},loading:{type:Boolean,default:!1},emptyText:{type:String,default:""},rowKey:{type:[String,Function],default:"id"},height:{type:[String,Number],default:void 0},maxHeight:{type:[String,Number],default:void 0},border:{type:Boolean,default:!1},stripe:{type:Boolean,default:!1},showHeader:{type:Boolean,default:!0},highlightCurrentRow:{type:Boolean,default:!1},headerCellClassName:{type:[String,Function],default:"common-table-header"},rowClassName:{type:[String,Function],default:"common-table-row"},showPagination:{type:Boolean,default:!0},pagination:{type:Object,default:()=>({total:0,currentPage:1,pageSize:20,pageSizes:[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper"})},actionsWidth:{type:[String,Number],default:void 0},actionsMinWidth:{type:[String,Number],default:void 0},actionsFixed:{type:[String,Boolean],default:"right"},actionsAlign:{type:String,default:"center"}},emits:["row-click","selection-change","sort-change","page-change","size-change"],setup(t,{expose:x,emit:C}){const f=t,y=C,o=u(f.pagination.currentPage||1),d=u(f.pagination.pageSize||20);c(()=>f.pagination.currentPage,a=>{a!==o.value&&(o.value=a)}),c(()=>f.pagination.pageSize,a=>{a!==d.value&&(d.value=a)});const S=(a,s,r)=>{y("row-click",a,s,r)},k=a=>{y("selection-change",a)},v=a=>{y("sort-change",a)},z=({page:a,size:s})=>{o.value=a,d.value=s,y("page-change",{page:a,size:s})};return x({currentPage:o,currentPageSize:d}),(a,s)=>{const r=$,B=F;return n(),g("div",M,[T(B,H({data:t.data,loading:t.loading,"empty-text":t.emptyText||(t.loading?"加载中...":"暂无数据"),"row-key":t.rowKey,"header-cell-class-name":t.headerCellClassName,"row-class-name":t.rowClassName,"max-height":t.maxHeight,height:t.height,border:t.border,stripe:t.stripe,"show-header":t.showHeader,"highlight-current-row":t.highlightCurrentRow,onRowClick:S,onSelectionChange:k,onSortChange:v,class:"common-table"},a.$attrs),{default:h(()=>[(n(!0),g(b,null,A(t.columns,e=>(n(),g(b,{key:e.prop||e.type},[e.type==="selection"?(n(),l(r,{key:0,type:e.type,width:e.width||55,fixed:e.fixed,selectable:e.selectable},null,8,["type","width","fixed","selectable"])):e.type==="index"?(n(),l(r,{key:1,type:e.type,width:e.width||60,fixed:e.fixed,label:e.label||"序号",index:e.index},null,8,["type","width","fixed","label","index"])):e.type==="expand"?(n(),l(r,{key:2,type:e.type,width:e.width||55,fixed:e.fixed},{default:h(i=>[w(a.$slots,"expand",{row:i.row,index:i.$index},void 0,!0)]),_:2},1032,["type","width","fixed"])):(n(),l(r,{key:3,prop:e.prop,label:e.label,width:e.width,"min-width":e.minWidth||(e.width?void 0:120),fixed:e.fixed,align:e.align||"left","header-align":e.headerAlign||e.align||"left",sortable:e.sortable,"sort-method":e.sortMethod,"sort-by":e.sortBy,"sort-orders":e.sortOrders,resizable:e.resizable!==!1,formatter:e.formatter,"show-overflow-tooltip":e.showOverflowTooltip!==!1,"class-name":e.className,"label-class-name":e.labelClassName},E({default:h(i=>[e.slot?w(a.$slots,e.slot,{key:0,row:i.row,column:i.column,index:i.$index,value:i.row[e.prop]},void 0,!0):e.formatter?(n(),g("span",O,m(e.formatter(i.row,i.column,i.row[e.prop],i.$index)),1)):(n(),g("span",R,m(i.row[e.prop]),1))]),_:2},[e.headerSlot?{name:"header",fn:h(i=>[w(a.$slots,e.headerSlot,{column:i.column,index:i.$index},void 0,!0)]),key:"0"}:void 0]),1032,["prop","label","width","min-width","fixed","align","header-align","sortable","sort-method","sort-by","sort-orders","resizable","formatter","show-overflow-tooltip","class-name","label-class-name"]))],64))),128)),a.$slots.actions?(n(),l(r,{key:0,label:"操作","min-width":t.actionsMinWidth||120,fixed:t.actionsFixed||"right",align:t.actionsAlign||"center","class-name":"actions-column"},{default:h(e=>[w(a.$slots,"actions",{row:e.row,index:e.$index},void 0,!0)]),_:3},8,["min-width","fixed","align"])):p("",!0)]),_:3},16,["data","loading","empty-text","row-key","header-cell-class-name","row-class-name","max-height","height","border","stripe","show-header","highlight-current-row"]),t.showPagination?(n(),l(P,{key:0,total:t.pagination.total,"current-page":o.value,"page-size":d.value,"page-sizes":t.pagination.pageSizes||[10,20,50,100],layout:t.pagination.layout||"total, sizes, prev, pager, next","pager-count":t.pagination.pagerCount||7,small:t.pagination.small,background:t.pagination.background!==!1,disabled:t.loading,"show-info":!0,onPageChange:z},null,8,["total","current-page","page-size","page-sizes","layout","pager-count","small","background","disabled"])):p("",!0)])}}},D=N(W,[["__scopeId","data-v-a486f4aa"]]);export{D as C};
