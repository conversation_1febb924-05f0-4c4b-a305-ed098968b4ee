import{_ as m,a as g}from"./index-BSF2zNx2.js";import{r as y,c as r,x as l,y as n,E as x,I as o,H as c,z as b}from"./vendor-Dq0JXR-b.js";const k=["disabled","title"],v={key:1,class:"loading-spinner"},p={__name:"IconButton",props:{icon:{type:String,required:!0},variant:{type:String,default:"primary",validator:t=>["primary","secondary","success","danger","warning","info","ghost"].includes(t)},size:{type:String,default:"md",validator:t=>["xs","sm","md","lg","xl"].includes(t)},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},title:{type:String,default:""}},emits:["click"],setup(t){const a=t,s=y(null),d=r(()=>({xs:"text-xs",sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"})[a.size]||"text-base"),u=r(()=>a.title?a.title:s.value&&s.value.textContent||"");return(e,i)=>(n(),l("button",{class:c(["icon-button",`icon-button--${t.variant}`,`icon-button--${t.size}`,{"icon-button--loading":t.loading,"icon-only":!e.$slots.default}]),disabled:t.disabled||t.loading,title:u.value,onClick:i[0]||(i[0]=f=>e.$emit("click",f))},[t.loading?o("",!0):(n(),x(g,{key:0,name:t.icon,class:c(d.value)},null,8,["name","class"])),t.loading?(n(),l("div",v)):o("",!0),e.$slots.default?(n(),l("span",{key:2,ref_key:"slotRef",ref:s,class:"button-text"},[b(e.$slots,"default",{},void 0,!0)],512)):o("",!0)],10,k))}},$=m(p,[["__scopeId","data-v-679fcac6"]]);export{$ as I};
