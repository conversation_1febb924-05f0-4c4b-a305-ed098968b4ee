import{s as L}from"./el-select-BLKpJAwO.js";import{E as U,t as be,c as X,u as $e,v as oe,p as Z,w as ne,x as le,h as ie,j as we,g as Ve,y as Ce,z as Ee,A as Te,i as Se,k as De,l as ee,B as ce,C as re,b as Me,d as je,e as Ie,D as ze,f as ae}from"./ui-CHSsU9Lc.js";import{r as R,W as de,x as C,y as f,B as e,O as t,F as l,K as E,L as u,c as G,w as J,M as A,ab as W,H as Q,I as q,E as K,n as ue,ae as P,u as O,j as Ue}from"./vendor-Dq0JXR-b.js";import{_ as Y,a as H}from"./index-BSF2zNx2.js";import{C as Be}from"./CommonTable-DDC4hMKa.js";import{I as _e}from"./IconButton-BMtfTzry.js";/* empty css                   *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                 */import{P as Fe}from"./PeriodIndicatorMatrix-CdnFxisq.js";import"./CommonPagination-IzNmwDoz.js";function Re(){const s=R([]),x=R(!1),p=de({current:1,pageSize:20,total:0}),b=async(r={})=>{x.value=!0;try{const c=await L.getTasks({skip:(p.current-1)*p.pageSize,limit:p.pageSize,...r});s.value=c.data||c,c.total!==void 0&&(p.total=c.total)}catch(c){U.error("加载任务列表失败"),console.error(c)}finally{x.value=!1}};return{tasks:s,loading:x,pagination:p,loadTasks:b,createTask:async r=>{try{await L.createTask(r),U.success("任务创建成功")}catch(c){throw U.error("任务创建失败"),c}},updateTask:async(r,c)=>{try{await L.updateTask(r,c),U.success("任务更新成功")}catch(v){throw U.error("任务更新失败"),v}},deleteTask:async r=>{try{await L.deleteTask(r),U.success("任务删除成功"),b()}catch(c){if(c!=="cancel")throw U.error("任务删除失败"),c}},executeTask:async r=>{try{return await L.executeTask(r)}catch(c){throw U.error("任务执行失败"),c}},toggleTaskStatus:async(r,c)=>{try{await L.updateTask(r,{is_active:c}),U.success(c?"任务已启用":"任务已禁用"),await b()}catch(v){throw U.error("状态更新失败"),v}}}}function pe(){const s=R([]),x=R(!1),p=de({current:1,pageSize:20,total:0}),b=async(c=null,v={})=>{var h,z;x.value=!0;try{const n={skip:(p.current-1)*p.pageSize,limit:p.pageSize,...v};let a;c?a=await L.getExecutions(c,n):a=await L.getAllExecutions(n),s.value=((h=a.data)==null?void 0:h.items)||a.items||a,p.total=((z=a.data)==null?void 0:z.total)||a.total||0}catch(n){U.error("加载执行记录失败"),console.error(n)}finally{x.value=!1}};return{executions:s,loading:x,pagination:p,loadExecutions:b,getExecutionDetail:async c=>{try{const v=await L.getExecutionDetail(c);return v.data||v}catch(v){throw U.error("获取执行详情失败"),v}},deleteExecution:async c=>{try{await L.deleteExecution(c),U.success("执行记录删除成功"),await b()}catch(v){throw U.error("删除执行记录失败"),v}},cancelExecution:async c=>{try{await L.cancelExecution(c),U.success("任务已取消"),await b()}catch(v){throw U.error("取消任务失败"),v}},getStatusText:c=>({pending:"待执行",running:"执行中",completed:"已完成",failed:"失败",cancelled:"已取消"})[c]||c,getStatusType:c=>({pending:"warning",running:"primary",completed:"success",failed:"danger",cancelled:"info"})[c]||"info",getTriggerTypeText:c=>({scheduled:"定时触发",manual:"手动触发"})[c]||c}}const Oe={class:"task-list"},Ae={class:"task-list-container"},Le={class:"task-list-header"},Ne={class:"header-content"},qe={class:"header-actions"},We={class:"task-list-content"},Pe={class:"task-name-cell"},He={class:"task-name"},Je={class:"task-description"},Ke={class:"cron-time-cell"},Ge={class:"cron-expression"},Ye={class:"cron-description"},Qe={class:"task-config-cell"},Xe={class:"config-summary"},Ze={class:"execution-count-cell"},et={class:"execution-count"},tt={class:"execution-limit"},st={class:"next-execution-cell"},at={class:"last-execution-cell"},ot={class:"table-actions"},nt={__name:"TaskList",props:{tasks:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["edit","delete","execute","toggle-status","view-executions","refresh"],setup(s,{emit:x}){const p=x,b=[{prop:"name",label:"任务名称",minWidth:120,slot:"name"},{prop:"task_type",label:"任务类型",minWidth:100,slot:"task_type"},{prop:"cron_expression",label:"执行时间",minWidth:120,slot:"cron_expression"},{prop:"task_config",label:"任务配置",minWidth:180,slot:"task_config"},{prop:"is_active",label:"状态",minWidth:120,slot:"is_active"},{prop:"current_executions",label:"执行次数",minWidth:120,slot:"current_executions"},{prop:"next_execution",label:"下次执行",minWidth:140,slot:"next_execution"},{prop:"last_execution",label:"最后执行",minWidth:140,slot:"last_execution"}],I=r=>({indicator_scan:"指标扫描",ai_analysis:"AI分析"})[r]||r,F=r=>({"* * * * *":"每分钟","0 * * * *":"每小时","0 9 * * *":"每天9点","0 18 * * *":"每天18点","0 9 * * MON":"每周一9点","0 9 1 * *":"每月1号9点","0 9 * * MON-FRI":"工作日9点","*/5 * * * *":"每5分钟","*/30 * * * *":"每30分钟"})[r]||"自定义时间",w=r=>{if(!r||typeof r!="object")return"-";try{if(r.indicators&&Array.isArray(r.indicators)){const c=r.indicators,v=r.periods||["d"],h=r.stock_codes,z={volume_pressure:"成交量压力",kdj:"KDJ指标",bollinger:"布林带",macd:"MACD",rsi:"RSI指标",arbr:"ARBR指标"},n={d:"日线",w:"周线",m:"月线"},a=c.map(S=>z[S]||S).join("、"),o=v.map(S=>n[S]||S).join("、"),i=h&&h.length>0?`指定${h.length}只股票`:"全市场";return`${a} | ${o} | ${i}`}return"自定义配置"}catch{return"配置解析失败"}},$=r=>r?new Date(r).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"",m=async r=>{var c,v;try{const h=r.is_active;await L.toggleTaskStatus(r.id,r.is_active),U.success(`任务已${r.is_active?"启用":"禁用"}`),p("refresh")}catch(h){r.is_active=!r.is_active,U.error(((v=(c=h.response)==null?void 0:c.data)==null?void 0:v.detail)||"操作失败")}};return(r,c)=>{const v=Z,h=oe,z=X,n=be,a=$e;return f(),C("div",Oe,[e("div",Ae,[e("div",Le,[e("div",Ne,[c[2]||(c[2]=e("h3",{class:"list-title"},"任务列表",-1)),e("div",qe,[t(_e,{icon:"refresh",variant:"ghost",size:"sm",onClick:c[0]||(c[0]=o=>r.$emit("refresh"))},{default:l(()=>c[1]||(c[1]=[E(" 刷新 ",-1)])),_:1,__:[1]})])])]),e("div",We,[t(Be,{data:s.tasks,loading:s.loading,columns:b,"show-pagination":!1,stripe:!0,actionsMinWidth:180,"empty-text":"暂无任务数据",class:"task-table"},{name:l(({row:o})=>[e("div",Pe,[e("div",He,u(o.name),1),e("div",Je,u(o.description||"无描述"),1)])]),task_type:l(({row:o})=>[t(v,{size:"small",type:"info",class:"task-type-tag"},{default:l(()=>[E(u(I(o.task_type)),1)]),_:2},1024)]),cron_expression:l(({row:o})=>[e("div",Ke,[e("div",Ge,u(o.cron_expression),1),e("div",Ye,u(F(o.cron_expression)),1)])]),task_config:l(({row:o})=>[e("div",Qe,[e("div",Xe,u(w(o.task_config)),1)])]),is_active:l(({row:o})=>[t(h,{modelValue:o.is_active,"onUpdate:modelValue":i=>o.is_active=i,onChange:i=>m(o),"active-text":"启用","inactive-text":"禁用",size:"small",class:"task-status-switch"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),current_executions:l(({row:o})=>[e("div",Ze,[e("div",et,u(o.current_executions),1),e("div",tt,u(o.max_executions?`/ ${o.max_executions}`:"/ 无限制"),1)])]),next_execution:l(({row:o})=>[e("div",st,u(o.next_execution?$(o.next_execution):"已停用"),1)]),last_execution:l(({row:o})=>[e("div",at,u(o.last_execution?$(o.last_execution):"未执行"),1)]),actions:l(({row:o})=>[e("div",ot,[t(n,{content:"立即执行",placement:"top"},{default:l(()=>[t(z,{text:"",size:"small",circle:"",onClick:i=>r.$emit("execute",o.id),class:"action-btn-execute"},{default:l(()=>[t(H,{name:"play"})]),_:2},1032,["onClick"])]),_:2},1024),t(n,{content:"查看记录",placement:"top"},{default:l(()=>[t(z,{text:"",size:"small",circle:"",onClick:i=>r.$emit("view-executions",o.id),class:"action-btn-view"},{default:l(()=>[t(H,{name:"list"})]),_:2},1032,["onClick"])]),_:2},1024),t(n,{content:"编辑任务",placement:"top"},{default:l(()=>[t(z,{text:"",size:"small",circle:"",onClick:i=>r.$emit("edit",o),class:"action-btn-edit"},{default:l(()=>[t(H,{name:"edit"})]),_:2},1032,["onClick"])]),_:2},1024),t(n,{content:"删除任务",placement:"top",disabled:!1},{default:l(()=>[t(a,{title:"确认删除此任务吗？","confirm-button-text":"确认","cancel-button-text":"取消",width:"220",placement:"bottom",onConfirm:i=>r.$emit("delete",o.id)},{reference:l(()=>[t(z,{text:"",size:"small",circle:"",class:"action-btn-delete"},{default:l(()=>[t(H,{name:"delete"})]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)])]),_:1},8,["data","loading"])])])])}}},lt=Y(nt,[["__scopeId","data-v-2b84186b"]]),it={presets:{"0 9 * * *":"每天早上9点执行","0 12 * * *":"每天中午12点执行","0 18 * * *":"每天下午6点执行","0 9 * * 1-5":"工作日早上9点执行","30 14 * * *":"每天下午2:30执行","0 8,12,16 * * 1-5":"工作日8点、12点、16点执行","0 9 1 * *":"每月1号早上9点执行","0 9 * * 1":"每周一早上9点执行","0 */4 * * *":"每4小时执行一次","*/15 * * * *":"每15分钟执行一次"},getCronDescription(s){if(!s||typeof s!="string")return"无效的Cron表达式";if(this.presets[s])return this.presets[s];const x=s.trim().split(/\s+/);if(x.length!==5)return"无效的Cron表达式格式";const[p,b,I,F,w]=x;try{let $="";const m=this.parseMinute(p),r=this.parseHour(b),c=this.parseDay(I),v=this.parseMonth(F),h=this.parseWeekday(w);return h&&h!=="每天"?$=h:c&&c!=="每日"?$=c:$="每天",v&&v!=="每月"&&($=v+$),$+=r+m,$||"自定义时间"}catch{return"无效的Cron表达式"}},parseMinute(s){return s==="*"||s==="0"?"":s.includes("/")?`:${s.split("/")[1]}分钟间隔`:s.includes(",")?`:${s.split(",").join("、")}分`:`:${s}分`},parseHour(s){if(s==="*")return"每小时";if(s.includes("/"))return`每${s.split("/")[1]}小时`;if(s.includes(","))return s.split(",").map(p=>`${p}点`).join("、");if(s.includes("-")){const[x,p]=s.split("-");return`${x}点到${p}点每小时`}return`${s}点`},parseDay(s){return s==="*"?"":s==="1"?"每月1号":s.includes("/")?`每${s.split("/")[1]}天`:s.includes(",")?s.split(",").map(p=>`${p}号`).join("、"):`${s}号`},parseMonth(s){if(s==="*")return"";const x=["","1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];return s.includes(",")?s.split(",").map(b=>x[parseInt(b)]).join("、"):x[parseInt(s)]||s+"月"},parseWeekday(s){if(s==="*")return"";const x=["周日","周一","周二","周三","周四","周五","周六","周日"];if(s==="1-5")return"工作日";if(s==="6,0"||s==="0,6")return"周末";if(s.includes(","))return s.split(",").map(b=>x[parseInt(b)]).join("、");if(s.includes("-")){const[p,b]=s.split("-");return`${x[parseInt(p)]}到${x[parseInt(b)]}`}return x[parseInt(s)]||"每天"}},ct={class:"cron-builder"},rt={class:"cron-display-section"},dt={class:"cron-expression-display"},ut={class:"cron-value"},_t={class:"cron-description"},pt={class:"description-text"},mt={class:"cron-builder-section"},vt={class:"builder-tabs"},ft=["onClick"],gt={class:"builder-content"},xt={key:0,class:"simple-mode"},ht={class:"time-picker-row"},yt={class:"time-input-group"},kt={class:"time-input-group"},bt={class:"frequency-section"},$t={class:"frequency-options"},wt=["onClick"],Vt={class:"frequency-icon"},Ct={class:"frequency-text"},Et={key:1,class:"advanced-mode"},Tt={class:"cron-field-grid"},St={class:"field-label"},Dt={class:"field-help"},Mt={key:2,class:"preset-mode"},jt={class:"preset-grid"},It=["onClick"],zt={class:"preset-icon"},Ut={class:"preset-title"},Bt={class:"preset-description"},Ft={__name:"CronBuilder",props:{modelValue:{type:String,default:"0 9 * * *"}},emits:["update:modelValue"],setup(s,{emit:x}){const p=s,b=x,I=R("simple"),F=[{key:"simple",label:"简单模式"},{key:"preset",label:"常用预设"},{key:"advanced",label:"高级模式"}],w=R({hour:9,minute:0,frequency:"daily"}),$=R({minute:"0",hour:"9",day:"*",month:"*",weekday:"*"}),m=[{value:"daily",label:"每天",icon:"📅"},{value:"weekdays",label:"工作日",icon:"💼"},{value:"weekly",label:"每周",icon:"📆"},{value:"monthly",label:"每月",icon:"🗓️"}],r=[{name:"minute",label:"分钟",placeholder:"0-59",help:"0-59 或 * 表示任意"},{name:"hour",label:"小时",placeholder:"0-23",help:"0-23 或 * 表示任意"},{name:"day",label:"日期",placeholder:"1-31",help:"1-31 或 * 表示任意"},{name:"month",label:"月份",placeholder:"1-12",help:"1-12 或 * 表示任意"},{name:"weekday",label:"星期",placeholder:"0-7",help:"0-7 (0和7都表示周日) 或 * 表示任意"}],c=[{value:"0 9 * * *",label:"每日早上9点",description:"适合开盘前扫描",icon:"🌅"},{value:"30 14 * * *",label:"每日下午2:30",description:"适合午盘分析",icon:"🕐"},{value:"0 9 * * 1-5",label:"工作日早上9点",description:"仅交易日执行",icon:"💼"},{value:"0 8,12,16 * * 1-5",label:"工作日多次扫描",description:"8点、12点、16点",icon:"⏰"},{value:"0 9 1 * *",label:"每月1号早上9点",description:"月度扫描",icon:"🗓️"},{value:"0 9 * * 1",label:"每周一早上9点",description:"周度扫描",icon:"📅"}],v=G(()=>it.getCronDescription(p.modelValue));J(w,n=>{if(I.value==="simple"){let a="";switch(n.frequency){case"daily":a=`${n.minute} ${n.hour} * * *`;break;case"weekdays":a=`${n.minute} ${n.hour} * * 1-5`;break;case"weekly":a=`${n.minute} ${n.hour} * * 1`;break;case"monthly":a=`${n.minute} ${n.hour} 1 * *`;break}b("update:modelValue",a)}},{deep:!0}),J($,n=>{if(I.value==="advanced"){const a=`${n.minute} ${n.hour} ${n.day} ${n.month} ${n.weekday}`;b("update:modelValue",a)}},{deep:!0});const h=n=>{b("update:modelValue",n)},z=n=>{const a=n.split(" ");a.length===5&&(w.value.minute=parseInt(a[0])||0,w.value.hour=parseInt(a[1])||9,a[4]==="1-5"?w.value.frequency="weekdays":a[4]==="1"?w.value.frequency="weekly":a[2]==="1"?w.value.frequency="monthly":w.value.frequency="daily",$.value.minute=a[0],$.value.hour=a[1],$.value.day=a[2],$.value.month=a[3],$.value.weekday=a[4])};return z(p.modelValue),J(()=>p.modelValue,n=>{z(n)}),(n,a)=>{const o=ne,i=le,S=ie;return f(),C("div",ct,[e("div",rt,[e("div",dt,[a[2]||(a[2]=e("div",{class:"cron-label"},"Cron 表达式:",-1)),e("div",ut,u(s.modelValue),1)]),e("div",_t,[a[3]||(a[3]=e("div",{class:"description-label"},"执行时间:",-1)),e("div",pt,u(v.value),1)])]),e("div",mt,[e("div",vt,[(f(),C(A,null,W(F,d=>e("div",{key:d.key,class:Q(["builder-tab",{"builder-tab--active":I.value===d.key}]),onClick:D=>I.value=d.key},u(d.label),11,ft)),64))]),e("div",gt,[I.value==="simple"?(f(),C("div",xt,[e("div",ht,[e("div",yt,[a[4]||(a[4]=e("label",{class:"input-label"},"小时",-1)),t(i,{modelValue:w.value.hour,"onUpdate:modelValue":a[0]||(a[0]=d=>w.value.hour=d),class:"time-select"},{default:l(()=>[(f(),C(A,null,W(24,d=>t(o,{key:d-1,label:String(d-1).padStart(2,"0"),value:d-1},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),a[6]||(a[6]=e("div",{class:"time-separator"},":",-1)),e("div",kt,[a[5]||(a[5]=e("label",{class:"input-label"},"分钟",-1)),t(i,{modelValue:w.value.minute,"onUpdate:modelValue":a[1]||(a[1]=d=>w.value.minute=d),class:"time-select"},{default:l(()=>[(f(),C(A,null,W([0,15,30,45],d=>t(o,{key:d,label:String(d).padStart(2,"0"),value:d},null,8,["label","value"])),64))]),_:1},8,["modelValue"])])]),e("div",bt,[a[7]||(a[7]=e("label",{class:"section-label"},"执行频率",-1)),e("div",$t,[(f(),C(A,null,W(m,d=>e("div",{key:d.value,class:Q(["frequency-option",{"frequency-option--active":w.value.frequency===d.value}]),onClick:D=>w.value.frequency=d.value},[e("div",Vt,u(d.icon),1),e("div",Ct,u(d.label),1)],10,wt)),64))])])])):I.value==="advanced"?(f(),C("div",Et,[e("div",Tt,[(f(),C(A,null,W(r,d=>e("div",{key:d.name,class:"cron-field"},[e("label",St,u(d.label),1),t(S,{modelValue:$.value[d.name],"onUpdate:modelValue":D=>$.value[d.name]=D,placeholder:d.placeholder,class:"field-input"},null,8,["modelValue","onUpdate:modelValue","placeholder"]),e("div",Dt,u(d.help),1)])),64))])])):I.value==="preset"?(f(),C("div",Mt,[e("div",jt,[(f(),C(A,null,W(c,d=>e("div",{key:d.value,class:Q(["preset-card",{"preset-card--active":s.modelValue===d.value}]),onClick:D=>h(d.value)},[e("div",zt,u(d.icon),1),e("div",Ut,u(d.label),1),e("div",Bt,u(d.description),1)],10,It)),64))])])):q("",!0)])])])}}},Rt=Y(Ft,[["__scopeId","data-v-02f88ed7"]]),Ot={class:"cron-builder-wrapper"},At={class:"w-full"},Lt={class:"form-help-text"},Nt={class:"max-executions-control"},qt={class:"dialog-footer"},Wt={__name:"TaskForm",props:{modelValue:{type:Boolean,default:!1},task:{type:Object,default:null}},emits:["update:modelValue","submit","cancel"],setup(s,{emit:x}){const p=s,b=x,I=R(null),F=R("all"),w=R(""),$=R(!1),m=R({name:"",description:"",cron_expression:"0 9 * * *",max_executions:null,task_config:{indicators:["kdj"],stock_codes:null,parameters:null,scan_mode:"traditional",periods:["d"],adjust:"n"}}),r=R({d:["kdj"],w:[],m:[]}),c={name:[{required:!0,message:"请输入任务名称",trigger:"blur"},{min:2,max:100,message:"任务名称长度在2-100个字符",trigger:"blur"}],cron_expression:[{required:!0,message:"请设置执行时间",trigger:"blur"}],"task_config.indicators":[{type:"array",required:!0,min:1,message:"请至少选择一个扫描指标",trigger:"change",validator:(o,i,S)=>{if(m.value.task_config.scan_mode==="traditional"){if(!i||i.length===0){S(new Error("请至少选择一个扫描指标"));return}}else if(!Object.values(r.value).some(D=>D&&D.length>0)){S(new Error("请为至少一个周期选择指标"));return}S()}}]},v=G(()=>!!p.task),h=()=>{m.value={name:"",description:"",cron_expression:"0 9 * * *",max_executions:null,task_config:{indicators:["kdj"],stock_codes:null,parameters:null,scan_mode:"traditional",periods:["d"],adjust:"n"}},r.value={d:["kdj"],w:[],m:[]},F.value="all",w.value="",$.value=!1,ue(()=>{var o;(o=I.value)==null||o.clearValidate()})};J(()=>p.task,o=>{var i,S,d,D,g,_;o?(m.value={name:o.name||"",description:o.description||"",cron_expression:o.cron_expression||"0 9 * * *",max_executions:o.max_executions,task_config:{indicators:((i=o.task_config)==null?void 0:i.indicators)||["kdj"],stock_codes:null,parameters:(S=o.task_config)==null?void 0:S.parameters,scan_mode:((d=o.task_config)==null?void 0:d.scan_mode)||"traditional",periods:((D=o.task_config)==null?void 0:D.periods)||["d"],adjust:"n"}},F.value="all",w.value="",(g=o.task_config)!=null&&g.period_indicators?r.value={d:o.task_config.period_indicators.d||[],w:o.task_config.period_indicators.w||[],m:o.task_config.period_indicators.m||[]}:r.value={d:((_=o.task_config)==null?void 0:_.indicators)||["kdj"],w:[],m:[]},$.value=!!o.max_executions):h()},{immediate:!0}),J(r,o=>{if(m.value.task_config.scan_mode==="traditional")m.value.task_config.indicators=o.d||[];else{const i=new Set;Object.values(o).forEach(S=>{S.forEach(d=>i.add(d))}),m.value.task_config.indicators=Array.from(i)}},{deep:!0}),J(()=>m.value.task_config.scan_mode,o=>{o==="traditional"?(r.value={d:r.value.d||[],w:[],m:[]},m.value.task_config.indicators=r.value.d||[]):(!r.value.d||r.value.d.length===0)&&(r.value.d=["kdj"])});const z=()=>{m.value.task_config.stock_codes=null},n=()=>{$.value?m.value.max_executions=10:m.value.max_executions=null},a=async()=>{if(I.value)try{await I.value.validate(),z();const o={...m.value};m.value.task_config.scan_mode==="multi_period"?(o.task_config.period_indicators=r.value,Object.keys(o.task_config.period_indicators).forEach(i=>{(!o.task_config.period_indicators[i]||o.task_config.period_indicators[i].length===0)&&delete o.task_config.period_indicators[i]})):(delete o.task_config.period_indicators,o.task_config.indicators=r.value.d||[]),console.log("提交数据:",{scanMode:o.task_config.scan_mode,indicators:o.task_config.indicators,periods:o.task_config.periods,periodIndicators:o.task_config.period_indicators}),b("submit",o)}catch{U.error("请检查表单输入")}};return(o,i)=>{const S=ie,d=Ve,D=Ee,g=Ce,_=Se,B=Te,T=oe,V=De,y=we,M=X,N=ee;return f(),K(N,{"model-value":s.modelValue,title:v.value?"编辑定时任务":"创建定时任务",width:"800px",onClose:i[9]||(i[9]=j=>o.$emit("cancel")),class:"scheduled-task-dialog"},{footer:l(()=>[e("div",qt,[t(M,{onClick:i[8]||(i[8]=j=>o.$emit("cancel")),class:"footer-btn-cancel"},{default:l(()=>i[15]||(i[15]=[E("取消",-1)])),_:1,__:[15]}),t(M,{type:"primary",onClick:a,class:"footer-btn-primary"},{default:l(()=>[E(u(v.value?"更新任务":"创建任务"),1)]),_:1})])]),default:l(()=>[t(y,{ref_key:"formRef",ref:I,model:m.value,rules:c,"label-width":"120px",class:"scheduled-task-form"},{default:l(()=>[t(d,{label:"任务名称",prop:"name"},{default:l(()=>[t(S,{modelValue:m.value.name,"onUpdate:modelValue":i[0]||(i[0]=j=>m.value.name=j),placeholder:"请输入任务名称",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(d,{label:"任务描述",prop:"description"},{default:l(()=>[t(S,{modelValue:m.value.description,"onUpdate:modelValue":i[1]||(i[1]=j=>m.value.description=j),type:"textarea",rows:3,placeholder:"请输入任务描述（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(d,{label:"执行时间",prop:"cron_expression",class:"cron-expression-form-item"},{default:l(()=>[e("div",Ot,[t(Rt,{modelValue:m.value.cron_expression,"onUpdate:modelValue":i[2]||(i[2]=j=>m.value.cron_expression=j)},null,8,["modelValue"])])]),_:1}),t(d,{label:"扫描模式",prop:"scan_mode"},{default:l(()=>[t(g,{modelValue:m.value.task_config.scan_mode,"onUpdate:modelValue":i[3]||(i[3]=j=>m.value.task_config.scan_mode=j),class:"scan-mode-radio-group"},{default:l(()=>[t(D,{value:"traditional",class:"scan-mode-radio"},{default:l(()=>i[10]||(i[10]=[E("传统模式（仅日线）",-1)])),_:1,__:[10]}),t(D,{value:"multi_period",class:"scan-mode-radio"},{default:l(()=>i[11]||(i[11]=[E("多周期模式",-1)])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),m.value.task_config.scan_mode==="multi_period"?(f(),K(d,{key:0,label:"扫描周期",prop:"periods"},{default:l(()=>[t(B,{modelValue:m.value.task_config.periods,"onUpdate:modelValue":i[4]||(i[4]=j=>m.value.task_config.periods=j),class:"periods-checkbox-group"},{default:l(()=>[t(_,{value:"d",class:"period-checkbox"},{default:l(()=>i[12]||(i[12]=[E("日线",-1)])),_:1,__:[12]}),t(_,{value:"w",class:"period-checkbox"},{default:l(()=>i[13]||(i[13]=[E("周线",-1)])),_:1,__:[13]}),t(_,{value:"m",class:"period-checkbox"},{default:l(()=>i[14]||(i[14]=[E("月线",-1)])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1})):q("",!0),t(d,{label:"扫描指标",prop:"task_config.indicators"},{default:l(()=>[e("div",At,[t(Fe,{modelValue:r.value,"onUpdate:modelValue":i[5]||(i[5]=j=>r.value=j),"scan-mode":m.value.task_config.scan_mode,"selected-periods":m.value.task_config.periods,type:"small"},null,8,["modelValue","scan-mode","selected-periods"]),e("div",Lt,[m.value.task_config.scan_mode==="traditional"?(f(),C(A,{key:0},[E(" 请选择要扫描的技术指标（至少选择一个） ")],64)):(f(),C(A,{key:1},[E(" 请为至少一个周期选择指标进行扫描 ")],64))])])]),_:1}),t(d,{label:"最大执行次数",prop:"max_executions"},{default:l(()=>[e("div",Nt,[t(T,{modelValue:$.value,"onUpdate:modelValue":i[6]||(i[6]=j=>$.value=j),"active-text":"限制执行次数","inactive-text":"无限制执行",onChange:n,class:"executions-switch"},null,8,["modelValue"]),$.value?(f(),K(V,{key:0,modelValue:m.value.max_executions,"onUpdate:modelValue":i[7]||(i[7]=j=>m.value.max_executions=j),min:1,max:1e3,"controls-position":"right",placeholder:"最大执行次数",class:"executions-input"},null,8,["modelValue"])):q("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value","title"])}}},Pt=Y(Wt,[["__scopeId","data-v-fea6cbe1"]]),Ht={key:0,class:"execution-results"},Jt={class:"execution-info bg-gray-50 dark:bg-gray-900 p-4 rounded-lg mb-4"},Kt={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Gt={class:"text-sm font-medium mt-1"},Yt={class:"text-lg font-bold text-blue-600 dark:text-blue-400 mt-1"},Qt={class:"text-sm font-medium mt-1"},Xt={key:0,class:"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded"},Zt={class:"text-sm text-red-600 dark:text-red-400"},es={key:0,class:"scan-results"},ts={class:"flex items-center justify-between mb-4"},ss={class:"flex items-center space-x-2"},as={class:"font-medium"},os={class:"flex flex-wrap gap-1"},ns={class:"text-xs space-y-1"},ls={key:0},is={key:1},cs={key:2},rs={key:1,class:"no-results text-center py-8"},ds={class:"text-gray-400 mb-2"},us={class:"task-config mt-6"},_s={class:"bg-gray-50 dark:bg-gray-900 p-4 rounded-lg"},ps={key:0,class:"formatted-config"},ms={class:"config-header mb-3"},vs={class:"text-base font-medium text-gray-800 dark:text-gray-200"},fs={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},gs={class:"config-details"},xs={class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},hs={class:"config-content pl-3"},ys={class:"text-xs text-gray-500 dark:text-gray-500"},ks={class:"text-sm text-gray-700 dark:text-gray-300 ml-2"},bs={key:1,class:"raw-config"},$s={class:"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap"},ws={__name:"ExecutionResults",props:{modelValue:{type:Boolean,default:!1},execution:{type:Object,default:null}},emits:["update:modelValue"],setup(s,{emit:x}){const p=s,b=G(()=>{var n;return((n=p.execution)==null?void 0:n.results_data)||[]}),I=n=>({pending:"待执行",running:"执行中",completed:"已完成",failed:"失败",cancelled:"已取消"})[n]||n,F=n=>({pending:"warning",running:"primary",completed:"success",failed:"danger",cancelled:"info"})[n]||"info",w=n=>({scheduled:"定时触发",manual:"手动触发"})[n]||n,$=n=>({BUY:"买入",SELL:"卖出",HOLD:"持有",STOP_LOSS:"止损"})[n]||n,m=n=>({BUY:"success",SELL:"danger",HOLD:"warning",STOP_LOSS:"danger"})[n]||"info",r=n=>({d:"日线",w:"周线",m:"月线"})[n]||n,c=n=>{if(!n)return"";if(n<60)return`${n}秒`;if(n<3600)return`${Math.floor(n/60)}分${n%60}秒`;const a=Math.floor(n/3600),o=Math.floor(n%3600/60),i=n%60;return`${a}时${o}分${i}秒`},v=n=>{try{const a=typeof n=="string"?JSON.parse(n):n;return JSON.stringify(a,null,2)}catch{return n||""}},h=()=>{var d;if(!b.value||b.value.length===0){U.warning("没有可导出的数据");return}const a=[["股票代码","股票名称","价格","涨跌幅","信号","周期"].join(","),...b.value.map(D=>{var g,_;return[D.stock_code,D.stock_name,((g=D.price)==null?void 0:g.toFixed(2))||"",D.change_percent?`${D.change_percent.toFixed(2)}%`:"",((_=D.signals)==null?void 0:_.join(";"))||"",r(D.period)].join(",")})].join(`
`),o=new Blob([a],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),S=URL.createObjectURL(o);i.setAttribute("href",S),i.setAttribute("download",`scan_results_${((d=p.execution)==null?void 0:d.id)||"unknown"}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),U.success("结果已导出")},z=()=>{U.info("结果已是最新数据")};return(n,a)=>{var V;const o=Z,i=P("i-carbon-warning"),S=P("i-carbon-download"),d=X,D=P("i-carbon-refresh"),g=re,_=ce,B=P("i-carbon-data-1"),T=ee;return f(),K(T,{"model-value":s.modelValue,title:`执行结果详情 - ID: ${((V=s.execution)==null?void 0:V.id)||""}`,width:"1000px",onClose:a[0]||(a[0]=y=>n.$emit("update:modelValue",!1))},{default:l(()=>[s.execution?(f(),C("div",Ht,[e("div",Jt,[e("div",Kt,[e("div",null,[a[1]||(a[1]=e("label",{class:"block text-sm text-gray-600 dark:text-gray-400"},"执行状态",-1)),t(o,{type:F(s.execution.status),size:"large",class:"mt-1"},{default:l(()=>[E(u(I(s.execution.status)),1)]),_:1},8,["type"])]),e("div",null,[a[2]||(a[2]=e("label",{class:"block text-sm text-gray-600 dark:text-gray-400"},"触发方式",-1)),e("div",Gt,u(w(s.execution.trigger_type)),1)]),e("div",null,[a[3]||(a[3]=e("label",{class:"block text-sm text-gray-600 dark:text-gray-400"},"结果数量",-1)),e("div",Yt,u(s.execution.results_count),1)]),e("div",null,[a[4]||(a[4]=e("label",{class:"block text-sm text-gray-600 dark:text-gray-400"},"执行时长",-1)),e("div",Qt,u(s.execution.duration_seconds?c(s.execution.duration_seconds):"-"),1)])]),s.execution.error_message?(f(),C("div",Xt,[e("div",Zt,[t(i,{class:"mr-1"}),E(" 错误信息："+u(s.execution.error_message),1)])])):q("",!0)]),s.execution.results_data&&s.execution.results_data.length>0?(f(),C("div",es,[e("div",ts,[a[7]||(a[7]=e("h3",{class:"text-lg font-medium"},"扫描结果",-1)),e("div",ss,[t(d,{size:"small",onClick:h},{default:l(()=>[t(S,{class:"mr-1"}),a[5]||(a[5]=E(" 导出结果 ",-1))]),_:1,__:[5]}),t(d,{size:"small",onClick:z},{default:l(()=>[t(D,{class:"mr-1"}),a[6]||(a[6]=E(" 刷新 ",-1))]),_:1,__:[6]})])]),t(_,{data:b.value,stripe:"","max-height":"400",class:"w-full"},{default:l(()=>[t(g,{prop:"stock_code",label:"股票代码",width:"100",fixed:"left"}),t(g,{prop:"stock_name",label:"股票名称",width:"120",fixed:"left"}),t(g,{prop:"price",label:"价格",width:"80"},{default:l(({row:y})=>{var M;return[e("span",as,u(((M=y.price)==null?void 0:M.toFixed(2))||"-"),1)]}),_:1}),t(g,{prop:"change_percent",label:"涨跌幅",width:"80"},{default:l(({row:y})=>[e("span",{class:Q({"text-red-600 dark:text-red-400":y.change_percent>0,"text-green-600 dark:text-green-400":y.change_percent<0,"text-gray-600 dark:text-gray-400":y.change_percent===0})},u(y.change_percent?`${y.change_percent>0?"+":""}${y.change_percent.toFixed(2)}%`:"-"),3)]),_:1}),t(g,{prop:"signals",label:"信号类型",width:"120"},{default:l(({row:y})=>[e("div",os,[(f(!0),C(A,null,W(y.signals,M=>(f(),K(o,{key:M,size:"small",type:m(M)},{default:l(()=>[E(u($(M)),1)]),_:2},1032,["type"]))),128))])]),_:1}),t(g,{prop:"period",label:"周期",width:"60"},{default:l(({row:y})=>[E(u(r(y.period)),1)]),_:1}),t(g,{prop:"indicator_data",label:"指标数据","min-width":"200"},{default:l(({row:y})=>{var M,N,j;return[e("div",ns,[(M=y.indicator_data)!=null&&M.kdj_k?(f(),C("div",ls," KDJ: K="+u(y.indicator_data.kdj_k.toFixed(2))+", D="+u(y.indicator_data.kdj_d.toFixed(2))+", J="+u(y.indicator_data.kdj_j.toFixed(2)),1)):q("",!0),(N=y.indicator_data)!=null&&N.macd?(f(),C("div",is," MACD: "+u(y.indicator_data.macd.toFixed(4)),1)):q("",!0),(j=y.indicator_data)!=null&&j.volume_pressure?(f(),C("div",cs," 量压: "+u(y.indicator_data.volume_pressure.toFixed(2)),1)):q("",!0)])]}),_:1})]),_:1},8,["data"])])):s.execution.results_count===0?(f(),C("div",rs,[e("div",ds,[t(B,{class:"text-4xl"})]),a[8]||(a[8]=e("div",{class:"text-gray-600 dark:text-gray-400"}," 本次扫描未找到符合条件的股票 ",-1))])):q("",!0),e("div",us,[a[9]||(a[9]=e("h3",{class:"text-lg font-medium mb-3"},"任务配置",-1)),e("div",_s,[s.execution.task_config&&s.execution.task_config.summary?(f(),C("div",ps,[e("div",ms,[e("h4",vs,u(s.execution.task_config.display_name||"任务配置"),1),e("div",fs,u(s.execution.task_config.summary),1)]),e("div",gs,[(f(!0),C(A,null,W(s.execution.task_config.details,(y,M)=>(f(),C("div",{key:M,class:"config-section mb-4"},[e("h5",xs,u(M),1),e("div",hs,[(f(!0),C(A,null,W(y,(N,j)=>(f(),C("div",{key:j,class:"config-item mb-1"},[e("span",ys,u(j)+":",1),e("span",ks,[Array.isArray(N)?(f(),C(A,{key:0},[E(u(N.join(", ")),1)],64)):(f(),C(A,{key:1},[E(u(N),1)],64))])]))),128))])]))),128))])])):(f(),C("div",bs,[e("pre",$s,u(v(s.execution.task_config_raw||s.execution.task_config)),1)]))])])])):q("",!0)]),_:1},8,["model-value","title"])}}},Vs=Y(ws,[["__scopeId","data-v-b12b5a2c"]]),Cs={class:"task-executions"},Es={class:"filters bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-4"},Ts={class:"flex items-center space-x-4"},Ss={class:"w-100px"},Ds={class:"w-100px"},Ms={class:"font-medium text-blue-600 dark:text-blue-400"},js={key:0,class:"text-red-600 dark:text-red-400 text-sm"},Is={key:1,class:"text-gray-400"},zs={class:"flex items-center space-x-1"},Us={class:"flex justify-center mt-4"},Bs={__name:"TaskExecutions",props:{modelValue:{type:Boolean,default:!1},taskId:{type:Number,default:null}},emits:["update:modelValue"],setup(s,{emit:x}){const p=s,{executions:b,loading:I,pagination:F,loadExecutions:w,deleteExecution:$,cancelExecution:m,getStatusText:r,getStatusType:c,getTriggerTypeText:v}=pe(),h=R({trigger_type:null,status:null}),z=R(!1),n=R(null);J(()=>p.modelValue,g=>{g&&ue(()=>{w(p.taskId)})});const a=()=>{F.current=1,w(p.taskId,h.value)},o=()=>{w(p.taskId,h.value)},i=g=>{n.value=g,z.value=!0},S=async(g,_)=>{switch(g){case"cancel":try{await ae.confirm("确认取消此次执行？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await m(_.id),await w(p.taskId,h.value)}catch(B){B!=="cancel"&&console.error("取消执行失败:",B)}break;case"delete":try{await ae.confirm("确认删除此执行记录？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await $(_.id),await w(p.taskId,h.value)}catch(B){B!=="cancel"&&console.error("删除记录失败:",B)}break}},d=g=>g?new Date(g).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"",D=g=>{if(!g)return"";if(g<60)return`${g}秒`;if(g<3600)return`${Math.floor(g/60)}分${g%60}秒`;const _=Math.floor(g/3600),B=Math.floor(g%3600/60),T=g%60;return`${_}时${B}分${T}秒`};return(g,_)=>{const B=ne,T=le,V=P("i-carbon-refresh"),y=X,M=re,N=Z,j=P("i-carbon-view"),me=P("i-carbon-overflow-menu-horizontal"),ve=P("i-carbon-stop"),te=Ie,fe=P("i-carbon-trash-can"),ge=je,xe=Me,he=ce,ye=ze,ke=ee;return f(),K(ke,{"model-value":s.modelValue,title:"任务执行记录",width:"1200px",onClose:_[5]||(_[5]=k=>g.$emit("update:modelValue",!1))},{default:l(()=>[e("div",Cs,[e("div",Es,[e("div",Ts,[e("div",Ss,[_[6]||(_[6]=e("label",{class:"block text-sm text-gray-700 dark:text-gray-300 mb-1"},"触发类型",-1)),t(T,{modelValue:h.value.trigger_type,"onUpdate:modelValue":_[0]||(_[0]=k=>h.value.trigger_type=k),placeholder:"全部",clearable:"",onChange:a},{default:l(()=>[t(B,{label:"定时触发",value:"scheduled"}),t(B,{label:"手动触发",value:"manual"})]),_:1},8,["modelValue"])]),e("div",Ds,[_[7]||(_[7]=e("label",{class:"block text-sm text-gray-700 dark:text-gray-300 mb-1"},"执行状态",-1)),t(T,{modelValue:h.value.status,"onUpdate:modelValue":_[1]||(_[1]=k=>h.value.status=k),placeholder:"全部",clearable:"",onChange:a},{default:l(()=>[t(B,{label:"待执行",value:"pending"}),t(B,{label:"执行中",value:"running"}),t(B,{label:"已完成",value:"completed"}),t(B,{label:"失败",value:"failed"}),t(B,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_[9]||(_[9]=e("div",{class:"flex-1"},null,-1)),t(y,{onClick:o},{default:l(()=>[t(V,{class:"mr-1"}),_[8]||(_[8]=E(" 刷新 ",-1))]),_:1,__:[8]})])]),t(he,{data:O(b),loading:O(I),stripe:"",class:"w-full","empty-text":"暂无执行记录"},{default:l(()=>[t(M,{prop:"id",label:"执行ID",width:"80"}),t(M,{prop:"trigger_type",label:"触发方式",width:"100"},{default:l(({row:k})=>[t(N,{type:k.trigger_type==="scheduled"?"primary":"success",size:"small"},{default:l(()=>[E(u(O(v)(k.trigger_type)),1)]),_:2},1032,["type"])]),_:1}),t(M,{prop:"status",label:"执行状态",width:"100"},{default:l(({row:k})=>[t(N,{type:O(c)(k.status),size:"small"},{default:l(()=>[E(u(O(r)(k.status)),1)]),_:2},1032,["type"])]),_:1}),t(M,{prop:"start_time",label:"开始时间",width:"140"},{default:l(({row:k})=>[E(u(k.start_time?d(k.start_time):"-"),1)]),_:1}),t(M,{prop:"end_time",label:"结束时间",width:"140"},{default:l(({row:k})=>[E(u(k.end_time?d(k.end_time):"-"),1)]),_:1}),t(M,{prop:"duration_seconds",label:"执行时长",width:"100"},{default:l(({row:k})=>[E(u(k.duration_seconds?D(k.duration_seconds):"-"),1)]),_:1}),t(M,{prop:"results_count",label:"结果数量",width:"100"},{default:l(({row:k})=>[e("span",Ms,u(k.results_count),1)]),_:1}),t(M,{prop:"error_message",label:"错误信息","min-width":"200"},{default:l(({row:k})=>[k.error_message?(f(),C("div",js,u(k.error_message),1)):(f(),C("span",Is,"-"))]),_:1}),t(M,{label:"操作",width:"150",fixed:"right"},{default:l(({row:k})=>[e("div",zs,[t(y,{size:"small",type:"primary",onClick:se=>i(k),disabled:!k.results_data},{default:l(()=>[t(j,{class:"mr-1"}),_[10]||(_[10]=E(" 查看结果 ",-1))]),_:2,__:[10]},1032,["onClick","disabled"]),t(xe,{onCommand:se=>S(se,k)},{dropdown:l(()=>[t(ge,null,{default:l(()=>[k.status==="pending"||k.status==="running"?(f(),K(te,{key:0,command:"cancel"},{default:l(()=>[t(ve,{class:"mr-2"}),_[11]||(_[11]=E(" 取消执行 ",-1))]),_:1,__:[11]})):q("",!0),t(te,{command:"delete",divided:""},{default:l(()=>[t(fe,{class:"mr-2"}),_[12]||(_[12]=E(" 删除记录 ",-1))]),_:1,__:[12]})]),_:2},1024)]),default:l(()=>[t(y,{size:"small",type:"text"},{default:l(()=>[t(me)]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data","loading"]),e("div",Us,[t(ye,{"current-page":O(F).current,"onUpdate:currentPage":_[2]||(_[2]=k=>O(F).current=k),"page-size":O(F).pageSize,"onUpdate:pageSize":_[3]||(_[3]=k=>O(F).pageSize=k),total:O(F).total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:O(w),onSizeChange:O(w)},null,8,["current-page","page-size","total","onCurrentChange","onSizeChange"])])]),t(Vs,{modelValue:z.value,"onUpdate:modelValue":_[4]||(_[4]=k=>z.value=k),execution:n.value},null,8,["modelValue","execution"])]),_:1},8,["model-value"])}}},Fs=Y(Bs,[["__scopeId","data-v-e51d3965"]]),Rs={class:"scheduled-tasks-page"},Os={class:"page-header"},As={class:"stats-grid"},Ls={class:"stat-card"},Ns={class:"stat-content"},qs={class:"stat-icon-wrapper stat-icon--primary"},Ws={class:"stat-info"},Ps={class:"stat-value"},Hs={class:"stat-card"},Js={class:"stat-content"},Ks={class:"stat-icon-wrapper stat-icon--success"},Gs={class:"stat-info"},Ys={class:"stat-value"},Qs={class:"stat-card"},Xs={class:"stat-content"},Zs={class:"stat-icon-wrapper stat-icon--warning"},ea={class:"stat-info"},ta={class:"stat-value"},sa={class:"stat-card"},aa={class:"stat-content"},oa={class:"stat-icon-wrapper stat-icon--danger"},na={class:"stat-info"},la={class:"stat-value"},ia={__name:"index",setup(s){const{tasks:x,loading:p,loadTasks:b,createTask:I,updateTask:F,deleteTask:w,executeTask:$,toggleTaskStatus:m}=Re(),{executions:r}=pe(),c=R(!1),v=R(!1),h=R(null),z=R(null),n=G(()=>x.value.filter(T=>T.is_active).length),a=G(()=>{const T=new Date().toDateString();return r.value.filter(V=>new Date(V.created_at).toDateString()===T).length}),o=G(()=>r.value.filter(T=>T.status==="failed").length);Ue(()=>{b()});const i=T=>{h.value={...T},c.value=!0},S=async T=>{try{await w(T),await b()}catch{}},d=async T=>{try{await $(T),U.success("任务已提交执行")}catch{}},D=async(T,V)=>{try{await m(T,V)}catch{}},g=T=>{z.value=T,v.value=!0},_=async T=>{try{h.value?await F(h.value.id,T):await I(T),await b(),B()}catch{}},B=()=>{c.value=!1,h.value=null};return(T,V)=>(f(),C("div",Rs,[e("div",Os,[V[4]||(V[4]=e("div",{class:"header-content"},[e("h1",{class:"page-title"},"定时任务管理")],-1)),t(_e,{icon:"add",variant:"primary",size:"md",onClick:V[0]||(V[0]=y=>c.value=!0),class:"create-task-btn"},{default:l(()=>V[3]||(V[3]=[E(" 创建任务 ",-1)])),_:1,__:[3]})]),e("div",As,[e("div",Ls,[e("div",Ns,[e("div",qs,[t(H,{name:"task",class:"stat-icon"})]),e("div",Ws,[V[5]||(V[5]=e("p",{class:"stat-label"},"总任务数",-1)),e("p",Ps,u(O(x).length),1)])])]),e("div",Hs,[e("div",Js,[e("div",Ks,[t(H,{name:"checkmark",class:"stat-icon"})]),e("div",Gs,[V[6]||(V[6]=e("p",{class:"stat-label"},"活跃任务",-1)),e("p",Ys,u(n.value),1)])])]),e("div",Qs,[e("div",Xs,[e("div",Zs,[t(H,{name:"time",class:"stat-icon"})]),e("div",ea,[V[7]||(V[7]=e("p",{class:"stat-label"},"今日执行",-1)),e("p",ta,u(a.value),1)])])]),e("div",sa,[e("div",aa,[e("div",oa,[t(H,{name:"warning",class:"stat-icon"})]),e("div",na,[V[8]||(V[8]=e("p",{class:"stat-label"},"失败任务",-1)),e("p",la,u(o.value),1)])])])]),t(lt,{tasks:O(x),loading:O(p),onEdit:i,onDelete:S,onExecute:d,onToggleStatus:D,onViewExecutions:g,onRefresh:O(b)},null,8,["tasks","loading","onRefresh"]),t(Pt,{modelValue:c.value,"onUpdate:modelValue":V[1]||(V[1]=y=>c.value=y),task:h.value,onSubmit:_,onCancel:B},null,8,["modelValue","task"]),t(Fs,{modelValue:v.value,"onUpdate:modelValue":V[2]||(V[2]=y=>v.value=y),"task-id":z.value},null,8,["modelValue","task-id"])]))}},ba=Y(ia,[["__scopeId","data-v-1e9b2f7e"]]);export{ba as default};
