import{r as i,w as ct,j as ut,x as I,B as s,O as R,K as q,L as d,G,al as dt,u as st,aC as nt,I as ft,M as mt,ab as xt,y as L,H as vt}from"./vendor-Dq0JXR-b.js";import{_ as pt,a as Z,b as K}from"./index-BSF2zNx2.js";import{C as gt}from"./CommonPagination-IzNmwDoz.js";import{m as B,n as bt}from"./ui-CHSsU9Lc.js";var Q,at;function yt(){if(at)return Q;at=1;var J="Expected a function",g=NaN,f="[object Symbol]",b=/^\s+|\s+$/g,y=/^[-+]0x[0-9a-f]+$/i,T=/^0b[01]+$/i,h=/^0o[0-7]+$/i,_=parseInt,M=typeof B=="object"&&B&&B.Object===Object&&B,m=typeof self=="object"&&self&&self.Object===Object&&self,k=M||m||Function("return this")(),P=Object.prototype,x=P.toString,O=Math.max,D=Math.min,C=function(){return k.Date.now()};function V(e,t,n){var r,c,v,u,o,l,p=0,j=!1,N=!1,H=!0;if(typeof e!="function")throw new TypeError(J);t=w(t)||0,$(n)&&(j=!!n.leading,N="maxWait"in n,v=N?O(w(n.maxWait)||0,t):v,H="trailing"in n?!!n.trailing:H);function U(a){var S=r,E=c;return r=c=void 0,p=a,u=e.apply(E,S),u}function ot(a){return p=a,o=setTimeout(F,t),j?U(a):u}function rt(a){var S=a-l,E=a-p,et=t-S;return N?D(et,v-E):et}function Y(a){var S=a-l,E=a-p;return l===void 0||S>=t||S<0||N&&E>=v}function F(){var a=C();if(Y(a))return tt(a);o=setTimeout(F,rt(a))}function tt(a){return o=void 0,H&&r?U(a):(r=c=void 0,u)}function lt(){o!==void 0&&clearTimeout(o),p=0,r=l=c=o=void 0}function it(){return o===void 0?u:tt(C())}function W(){var a=C(),S=Y(a);if(r=arguments,c=this,l=a,S){if(o===void 0)return ot(l);if(N)return o=setTimeout(F,t),U(l)}return o===void 0&&(o=setTimeout(F,t)),u}return W.cancel=lt,W.flush=it,W}function $(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function z(e){return!!e&&typeof e=="object"}function A(e){return typeof e=="symbol"||z(e)&&x.call(e)==f}function w(e){if(typeof e=="number")return e;if(A(e))return g;if($(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=$(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(b,"");var n=T.test(e);return n||h.test(e)?_(e.slice(2),n?2:8):y.test(e)?g:+e}return Q=V,Q}var ht=yt();const _t=bt(ht),St={class:"page-container p-6 bg-bg-primary text-text-primary"},kt={class:"card bg-bg-secondary p-6 rounded-lg shadow-lg"},Ct={class:"flex items-center mb-6 gap-4"},jt={class:"flex space-x-3"},It=["disabled"],Lt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"},Tt={class:"overflow-x-auto"},$t={class:"data-table w-full"},wt={key:0},Nt={key:1},Et={colspan:"9",class:"p-8 text-center"},Mt={class:"text-text-muted"},Ot={class:"p-3 text-sm text-text-secondary font-mono"},Dt={class:"p-3 text-sm text-text-primary font-medium"},Ft={class:"p-3 text-sm text-text-secondary"},Rt={class:"p-3 text-sm text-text-secondary"},Bt={class:"p-3 text-sm text-text-secondary"},Pt={class:"p-3 text-sm text-right text-text-secondary"},Vt={class:"p-3 text-sm text-right text-text-secondary"},zt={class:"p-3 text-center"},X=20,At={__name:"index",setup(J){const g=i([]),f=i(!1);i(!1);const b=i(null),y=i(""),T=i(""),h=i(""),_=i(""),M=i([]),m=i(1),k=i(0),P=({page:e})=>{m.value=e},x=async()=>{try{f.value=!0,b.value=null;const e={skip:(m.value-1)*X,limit:X};y.value.trim()&&(e.search=y.value.trim()),T.value&&(e.industry=T.value),h.value&&(e.exchange=h.value),_.value&&(e.is_active=_.value==="true");const{data:t}=await K.stockList.getStockList(e);if(console.log(t),t&&t.stocks){if(g.value=t.stocks,k.value=t.total||0,M.value.length===0){const n=[...new Set(t.stocks.map(r=>r.industry).filter(r=>r&&r.trim()))];M.value=n.sort()}}else g.value=[],k.value=0}catch(e){console.error("获取股票列表失败:",e),b.value=e.message||"获取股票列表失败",g.value=[],k.value=0}finally{f.value=!1}},O=_t(()=>{m.value=1,x()},300),D=async()=>{var e,t;try{f.value=!0,b.value=null,console.log("正在从运营商拉取最新股票和指数数据，请稍候...");const r=[K.stockList.refreshStockList(),K.indexList.refreshIndexList()],[c,v]=await Promise.allSettled(r);if(c.status==="fulfilled"&&c.value){const{deleted_count:u,inserted_count:o,total_fetched:l,data_source:p,duration_seconds:j}=c.value;console.log(`股票数据刷新成功：删除${u}条，插入${o}条，共获取${l}条数据，使用${p}数据源，耗时${j.toFixed(2)}秒`)}else console.warn("股票数据刷新失败:",((e=c.reason)==null?void 0:e.message)||"未知错误");if(v.status==="fulfilled"&&v.value){const{deleted_count:u,inserted_count:o,total_fetched:l,data_source:p,duration_seconds:j}=v.value;console.log(`指数数据刷新成功：删除${u}条，插入${o}条，共获取${l}条数据，使用${p}数据源，耗时${j.toFixed(2)}秒`)}else console.warn("指数数据刷新失败:",((t=v.reason)==null?void 0:t.message)||"未知错误");await x(),console.log("数据刷新操作完成")}catch(n){console.error("刷新数据失败:",n),b.value=n.message||"刷新数据失败",await x()}finally{f.value=!1}},C=()=>{m.value=1,x()},V=()=>{y.value="",T.value="",h.value="",_.value="",m.value=1,x()},$=()=>{console.log("导出数据功能待实现")},z=e=>({SH:"上证",SZ:"深证"})[e]||e,A=e=>{if(!e||e===null||e==="null")return"暂无数据";try{const t=new Date(e);return isNaN(t.getTime())?"暂无数据":t.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})}catch{return"暂无数据"}},w=e=>{if(!e||e===null||e===0||e==="null")return"暂无数据";const t=parseInt(e);return isNaN(t)||t<=0?"暂无数据":t>=1e8?(t/1e8).toFixed(1)+"亿":t>=1e4?(t/1e4).toFixed(1)+"万":t.toLocaleString()};return ct(m,()=>{x()}),ut(()=>{x()}),(e,t)=>(L(),I("div",St,[s("div",kt,[s("div",Ct,[t[5]||(t[5]=s("h2",{class:"text-2xl font-semibold text-text-primary"},"股票列表管理",-1)),t[6]||(t[6]=s("div",{id:"gap",class:"flex-1"},null,-1)),s("div",jt,[s("button",{class:"btn-primary",onClick:D,disabled:f.value},[R(Z,{name:"activity",class:"mr-2"}),q(" "+d(f.value?"拉取中...":"拉取数据"),1)],8,It),s("button",{class:"btn-secondary",onClick:$},[R(Z,{name:"download",class:"mr-2"}),t[4]||(t[4]=q("导出数据 ",-1))])])]),s("div",Lt,[G(s("input",{type:"text",class:"input-field",placeholder:"搜索股票代码或名称","onUpdate:modelValue":t[0]||(t[0]=n=>y.value=n),onInput:t[1]||(t[1]=(...n)=>st(O)&&st(O)(...n))},null,544),[[dt,y.value]]),G(s("select",{class:"input-field","onUpdate:modelValue":t[2]||(t[2]=n=>h.value=n),onChange:C},t[7]||(t[7]=[s("option",{value:""},"所有市场",-1),s("option",{value:"SH"},"上证",-1),s("option",{value:"SZ"},"深证",-1)]),544),[[nt,h.value]]),G(s("select",{class:"input-field","onUpdate:modelValue":t[3]||(t[3]=n=>_.value=n),onChange:C},t[8]||(t[8]=[s("option",{value:""},"所有状态",-1),s("option",{value:"true"},"活跃",-1),s("option",{value:"false"},"非活跃",-1)]),544),[[nt,_.value]]),s("button",{class:"btn-primary",onClick:V},[R(Z,{name:"filter",class:"mr-2"}),t[9]||(t[9]=q("重置筛选 ",-1))])]),s("div",Tt,[s("table",$t,[t[11]||(t[11]=s("thead",null,[s("tr",null,[s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 代码 "),s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 名称 "),s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 行业 "),s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 市场 "),s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 上市日期 "),s("th",{class:"p-3 text-right text-sm font-semibold text-text-muted"}," 总股本 "),s("th",{class:"p-3 text-right text-sm font-semibold text-text-muted"}," 流通股本 "),s("th",{class:"p-3 text-center text-sm font-semibold text-text-muted"}," 状态 ")])],-1)),s("tbody",null,[f.value?(L(),I("tr",wt,t[10]||(t[10]=[s("td",{colspan:"9",class:"p-8 text-center"},[s("div",{class:"flex items-center justify-center"},[s("div",{class:"spinner mr-2"}),s("span",{class:"text-text-muted"},"加载中...")])],-1)]))):g.value.length===0?(L(),I("tr",Nt,[s("td",Et,[s("p",Mt,d(b.value?"数据加载失败":"暂无数据"),1),b.value?(L(),I("button",{key:0,class:"btn-primary mt-2",onClick:D}," 重试 ")):ft("",!0)])])):(L(!0),I(mt,{key:2},xt(g.value,n=>(L(),I("tr",{key:n.code,class:"border-b border-border-color hover:bg-bg-tertiary transition-colors"},[s("td",Ot,d(n.code),1),s("td",Dt,d(n.name),1),s("td",Ft,d(n.industry||"未分类"),1),s("td",Rt,d(z(n.exchange)),1),s("td",Bt,d(A(n.listing_date)),1),s("td",Pt,d(w(n.total_shares)),1),s("td",Vt,d(w(n.circulating_shares)),1),s("td",zt,[s("span",{class:vt(n.is_active?"text-green-500":"text-red-500")},d(n.is_active?"活跃":"非活跃"),3)])]))),128))])])]),R(gt,{total:k.value,"current-page":m.value,"page-size":X,"show-filtered":!!(y.value||h.value||_.value),onPageChange:P,class:"mt-6"},null,8,["total","current-page","show-filtered"])])]))}},Gt=pt(At,[["__scopeId","data-v-60afc630"]]);export{Gt as default};
