import{aD as O,r as y,c as F,j as Q,Q as J,x as n,B as t,I as f,L as l,u as i,O as r,F as V,K as $,M as k,ab as T,G as X,al as Y,az as Z,y as o,H as I}from"./vendor-Dq0JXR-b.js";import{_ as tt,c as et,a as x,b as W}from"./index-BSF2zNx2.js";import{s as h}from"./searchStats-Dfdmz4M0.js";import{I as v}from"./IconButton-BMtfTzry.js";import"./ui-CHSsU9Lc.js";const st={class:"watchlist-page"},at={class:"grid grid-cols-1 gap-6"},nt={class:"card p-6"},ot={class:"flex items-center justify-between mb-4"},lt={class:"flex items-center gap-3"},rt={class:"text-sm text-gray-400"},it={class:"flex items-center gap-2"},ct={class:"data-table"},dt={key:0,class:"text-center py-12"},ut={key:1,class:"text-center py-12"},mt={class:"text-red-400 mb-4"},xt={key:2,class:"w-full"},gt={class:"font-mono text-sm"},pt={class:"font-medium"},yt={class:"font-mono text-sm"},ht={class:"flex items-center gap-1 justify-center"},vt={key:3,class:"text-center py-12"},_t={key:0,class:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"},ft={class:"bg-gray-800 rounded-lg p-6 w-full max-w-md"},bt={class:"flex items-center justify-between mb-4"},St={class:"space-y-4"},wt={key:0,class:"max-h-60 overflow-y-auto overflow-x-hidden"},Ct={class:"text-sm text-gray-400 mb-2"},kt={class:"space-y-2 px-2"},Tt=["onClick"],zt={class:"flex-1 min-w-0"},Bt={class:"font-medium truncate"},jt={class:"text-sm text-gray-400 flex items-center gap-2"},Dt={class:"truncate"},Mt={key:0,class:"text-xs bg-gray-600 px-1 rounded"},Pt={key:1,class:"text-xs bg-orange-500 bg-opacity-20 text-orange-400 px-1 rounded"},Ft={key:1},Vt={class:"text-center py-8"},$t={key:2},It={key:3},Wt={class:"space-y-2"},At=["onClick"],Nt={class:"font-medium"},Lt={class:"text-sm text-gray-400 flex items-center gap-2"},Rt={key:0,class:"text-xs bg-blue-500 bg-opacity-20 text-blue-400 px-1 rounded"},qt={key:1,class:"text-xs bg-green-500 bg-opacity-20 text-green-400 px-1 rounded"},Ht={key:4},Kt={class:"text-center py-8"},Ut={__name:"index",setup(Et){const A=Z(),u=et(),{watchlistStocks:b,loading:z,error:S}=O(u),w=y(!1),c=y(""),d=y([]),_=y(!1),N=F(()=>h.getPopularStocks(5)),C=y([]),L=async()=>{try{const e=await W.stockSearch.getPopularStocks({limit:5});C.value=e.data||[]}catch(e){console.error("获取热门股票失败:",e),C.value=[]}},B=F(()=>{const e=N.value,s=C.value,a=[...e],p=new Set(e.map(m=>m.code));for(const m of s)if(!p.has(m.code)&&(a.push({code:m.code,name:m.name,industry:m.industry||"未分类",searchCount:0}),a.length>=8))break;return a}),j=async()=>{try{await Promise.all([u.loadWatchlist(),L()])}catch(e){console.error("加载数据失败:",e)}};Q(j),J(()=>{g&&clearTimeout(g)});const R=()=>{u.error=null,j()},D=()=>{w.value=!0,c.value="",d.value=[]},M=()=>{w.value=!1};let g=null;const q=()=>{g&&clearTimeout(g),g=setTimeout(async()=>{if(!c.value.trim()){d.value=[];return}_.value=!0;try{const e=await W.stockSearch.quickSearchStocks(c.value,20);d.value=e.data||[]}catch(e){console.error("搜索股票失败:",e),d.value=[]}finally{_.value=!1}},300)},P=async e=>{if(u.watchlistStocks.some(s=>s.code===e.code)){alert("该股票已在自选股中");return}try{h.incrementSearchCount(e.code,e.name,e.industry),await u.addToWatchlist(e.code),M()}catch(s){alert(s.message||"添加失败")}},H=e=>{h.incrementSearchCount(e.code,e.name,e.industry),A.push({name:"Analysis",query:{stock:e.code,name:e.name}})},K=async e=>{if(confirm(`确定要删除 ${e.name} 吗？`))try{await u.removeFromWatchlist(e.code)}catch(s){alert(s.message||"删除失败")}},U=e=>e<0?"text-green-400":e>0?"text-red-400":"text-gray-400",E=e=>e<0?"bg-green-500 bg-opacity-20 text-green-400":e>0?"bg-red-500 bg-opacity-20 text-red-400":"bg-gray-500 bg-opacity-20 text-gray-400",G=e=>e>=1e8?(e/1e8).toFixed(1)+"亿":e>=1e4?(e/1e4).toFixed(1)+"万":e.toString();return(e,s)=>(o(),n("div",st,[t("div",at,[t("div",null,[t("div",nt,[t("div",ot,[t("div",lt,[s[1]||(s[1]=t("h3",{class:"text-lg font-semibold"},"我的自选股",-1)),t("span",rt,"("+l(i(b).length)+"只)",1)]),t("div",it,[r(v,{icon:"add",variant:"primary",size:"sm",onClick:D},{default:V(()=>s[2]||(s[2]=[$(" 添加股票 ",-1)])),_:1,__:[2]})])]),t("div",ct,[i(z)?(o(),n("div",dt,s[3]||(s[3]=[t("div",{class:"loading-spinner mx-auto mb-4"},null,-1),t("p",{class:"text-gray-400"},"正在加载自选股...",-1)]))):i(S)?(o(),n("div",ut,[r(x,{name:"exclamation-triangle",class:"text-4xl text-red-500 mb-4"}),t("p",mt,l(i(S)),1),t("button",{class:"btn btn--primary btn--sm",onClick:R}," 重试 ")])):(o(),n("table",xt,[s[4]||(s[4]=t("thead",null,[t("tr",null,[t("th",null,"代码"),t("th",null,"名称"),t("th",null,"现价"),t("th",null,"涨跌幅"),t("th",null,"成交量"),t("th",{class:"text-center"},"操作")])],-1)),t("tbody",null,[(o(!0),n(k,null,T(i(b),a=>(o(),n("tr",{key:a.code,class:"watchlist-row"},[t("td",null,[t("span",gt,l(a.code),1)]),t("td",null,[t("span",pt,l(a.name),1)]),t("td",null,[t("span",{class:I(["font-mono font-bold",U(a.changePercent)])}," ¥"+l(a.price),3)]),t("td",null,[t("span",{class:I(["px-2 py-1 rounded text-xs font-medium",E(a.changePercent)])},l(a.changePercent>=0?"+":"")+l(a.changePercent)+"% ",3)]),t("td",null,[t("span",yt,l(G(a.volume)),1)]),t("td",null,[t("div",ht,[r(v,{icon:"chart-line",variant:"ghost",size:"xs",title:"分析股票",onClick:p=>H(a)},null,8,["onClick"]),r(v,{icon:"trash-can",variant:"ghost",size:"xs",title:"移除自选",onClick:p=>K(a)},null,8,["onClick"])])])]))),128))])])),!i(z)&&!i(S)&&i(b).length===0?(o(),n("div",vt,[r(x,{name:"star",class:"text-4xl text-gray-500 mb-4"}),s[6]||(s[6]=t("p",{class:"text-gray-400 mb-4"},"您还没有添加任何自选股",-1)),r(v,{icon:"add",variant:"primary",size:"md",onClick:D},{default:V(()=>s[5]||(s[5]=[$(" 添加第一只股票 ",-1)])),_:1,__:[5]})])):f("",!0)])])])]),w.value?(o(),n("div",_t,[t("div",ft,[t("div",bt,[s[7]||(s[7]=t("h3",{class:"text-lg font-semibold"},"添加自选股",-1)),r(v,{icon:"close",variant:"ghost",size:"xs",title:"关闭",onClick:M})]),t("div",St,[t("div",null,[s[8]||(s[8]=t("label",{class:"block text-sm text-gray-400 mb-2"},"搜索股票",-1)),X(t("input",{"onUpdate:modelValue":s[0]||(s[0]=a=>c.value=a),type:"text",class:"input-field w-full",placeholder:"输入股票代码或名称",onInput:q},null,544),[[Y,c.value]])]),c.value.trim()&&d.value.length>0?(o(),n("div",wt,[t("div",Ct,"搜索结果 ("+l(d.value.length)+"只):",1),t("div",kt,[(o(!0),n(k,null,T(d.value,a=>(o(),n("div",{key:a.code,class:"flex items-center justify-between p-3 bg-gray-700 rounded cursor-pointer hover:bg-gray-600",onClick:p=>P(a)},[t("div",zt,[t("div",Bt,l(a.code)+" "+l(a.name),1),t("div",jt,[t("span",Dt,l(a.industry||"未分类"),1),a.exchange?(o(),n("span",Mt,l(a.exchange),1)):f("",!0),i(h).getSearchCount(a.code)>0?(o(),n("span",Pt,l(i(h).getSearchCount(a.code))+"次 ",1)):f("",!0)])]),r(x,{name:"add",class:"text-blue-400 flex-shrink-0"})],8,Tt))),128))])])):c.value.trim()&&d.value.length===0&&!_.value?(o(),n("div",Ft,[t("div",Vt,[r(x,{name:"search",class:"text-3xl text-gray-500 mb-2"}),s[9]||(s[9]=t("p",{class:"text-gray-400 text-sm"},"未找到匹配的股票",-1)),s[10]||(s[10]=t("p",{class:"text-gray-500 text-xs mt-1"},"请尝试输入完整的股票代码或名称",-1))])])):c.value.trim()&&_.value?(o(),n("div",$t,s[11]||(s[11]=[t("div",{class:"text-center py-8"},[t("div",{class:"loading-spinner mx-auto mb-4"}),t("p",{class:"text-gray-400 text-sm"},"正在搜索...")],-1)]))):B.value.length>0?(o(),n("div",It,[s[12]||(s[12]=t("div",{class:"text-sm text-gray-400 mb-2"},"热门推荐 (基于搜索次数):",-1)),t("div",Wt,[(o(!0),n(k,null,T(B.value,a=>(o(),n("div",{key:a.code,class:"flex items-center justify-between p-3 bg-gray-700 rounded cursor-pointer hover:bg-gray-600",onClick:p=>P(a)},[t("div",null,[t("div",Nt,l(a.code)+" "+l(a.name),1),t("div",Lt,[t("span",null,l(a.industry||"未分类"),1),a.searchCount>0?(o(),n("span",Rt,l(a.searchCount)+"次搜索 ",1)):(o(),n("span",qt," 热门 "))])]),r(x,{name:"add",class:"text-blue-400"})],8,At))),128))])])):(o(),n("div",Ht,[t("div",Kt,[r(x,{name:"search",class:"text-3xl text-gray-500 mb-2"}),s[13]||(s[13]=t("p",{class:"text-gray-400 text-sm"},"输入股票代码或名称开始搜索",-1))])]))])])])):f("",!0)]))}},Yt=tt(Ut,[["__scopeId","data-v-1223a1d9"]]);export{Yt as default};
