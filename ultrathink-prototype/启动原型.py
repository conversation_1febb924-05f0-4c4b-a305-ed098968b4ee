#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultrathink 股票量化分析原型 - 一键启动服务器
支持 Python 3.x，无需安装额外依赖
"""

import os
import sys
import webbrowser
import socketserver
from http.server import SimpleHTTPRequestHandler
import threading
import time

class CustomHandler(SimpleHTTPRequestHandler):
    """自定义HTTP处理器，支持CORS和正确的MIME类型"""
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()
    
    def guess_type(self, path):
        """确保JS文件使用正确的MIME类型"""
        # 直接处理特定文件类型
        if path.endswith('.js') or path.endswith('.mjs'):
            return 'application/javascript'
        if path.endswith('.css'):
            return 'text/css'
        if path.endswith('.html'):
            return 'text/html'
        if path.endswith('.svg'):
            return 'image/svg+xml'
        if path.endswith('.png'):
            return 'image/png'
        if path.endswith('.jpg') or path.endswith('.jpeg'):
            return 'image/jpeg'
        if path.endswith('.gif'):
            return 'image/gif'
        if path.endswith('.ico'):
            return 'image/x-icon'
        if path.endswith('.json'):
            return 'application/json'
        
        # 默认情况
        return super().guess_type(path)

def find_free_port(start_port=8080):
    """寻找可用端口"""
    import socket
    for port in range(start_port, start_port + 100):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return port
            except OSError:
                continue
    return None

def start_server():
    """启动HTTP服务器"""
    # 切换到dist目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    dist_dir = os.path.join(script_dir, 'dist')
    
    if not os.path.exists(dist_dir):
        print("❌ 错误: 未找到 dist 目录")
        print("   请先运行 'npm run build' 构建项目")
        input("按回车键退出...")
        return
    
    os.chdir(dist_dir)
    
    # 寻找可用端口
    port = find_free_port()
    if port is None:
        print("❌ 错误: 无法找到可用端口")
        input("按回车键退出...")
        return
    
    print(f"""
🚀 Ultrathink 股票量化分析原型启动中...

📂 服务目录: {dist_dir}
🌐 服务地址: http://localhost:{port}
🎯 演示账户:
   管理员: admin / admin123
   普通用户: user / user123  
   演示用户: demo / demo123

💡 使用 Ctrl+C 停止服务器
""")
    
    try:
        # 启动服务器
        with socketserver.TCPServer(("", port), CustomHandler) as httpd:
            # 在新线程中启动服务器
            server_thread = threading.Thread(target=httpd.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            
            # 等待1秒确保服务器启动
            time.sleep(1)
            
            # 自动打开浏览器
            url = f"http://localhost:{port}"
            print(f"🌟 正在打开浏览器: {url}")
            
            try:
                webbrowser.open(url)
            except Exception as e:
                print(f"   无法自动打开浏览器: {e}")
                print(f"   请手动访问: {url}")
            
            print("\n✅ 服务器运行中... (按 Ctrl+C 停止)")
            
            # 保持服务器运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n\n🛑 正在停止服务器...")
                httpd.shutdown()
                print("✅ 服务器已停止")
                
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════╗
║   🚀 Ultrathink 股票量化分析系统原型         ║
║                                              ║
║   高保真交互式前端原型                        ║
║   让投资更智能，让决策更精准                   ║
╚══════════════════════════════════════════════╝
""")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要 Python 3.6 或更高版本")
        input("按回车键退出...")
        sys.exit(1)
    
    start_server()