<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动 Ultrathink 股票量化分析原型</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 600px;
            margin: 20px;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .launch-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ff8e53);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px 0 rgba(31, 38, 135, 0.4);
        }
        
        .launch-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba(31, 38, 135, 0.6);
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .account-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .account-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: left;
        }
        
        .loading {
            display: none;
            margin-top: 20px;
        }
        
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Ultrathink 股票量化分析原型</h1>
        <div class="subtitle">高保真交互式前端原型系统</div>
        
        <div class="info-box">
            <h3>🎯 演示账户</h3>
            <div class="account-info">
                <div class="account-card">
                    <strong>管理员</strong><br>
                    用户名: admin<br>
                    密码: admin123
                </div>
                <div class="account-card">
                    <strong>普通用户</strong><br>
                    用户名: user<br>
                    密码: user123
                </div>
                <div class="account-card">
                    <strong>演示用户</strong><br>
                    用户名: demo<br>
                    密码: demo123
                </div>
            </div>
        </div>
        
        <div style="margin: 20px 0;">
            <div class="info-box" style="background: rgba(255, 193, 7, 0.2); border-left: 4px solid #ffc107;">
                <h3>⚠️ 重要提示</h3>
                <p>由于现代浏览器的安全限制，ES模块无法通过 file:// 协议直接运行。</p>
                <p>请使用以下方式之一启动原型：</p>
            </div>
        </div>
        
        <div style="display: flex; flex-direction: column; gap: 10px; align-items: center;">
            <a href="javascript:void(0)" class="launch-button" onclick="startWithPython()">
                🐍 使用 Python 启动
            </a>
            <a href="javascript:void(0)" class="launch-button" onclick="startWithNode()">
                ⚡️ 使用 Node.js 启动
            </a>
            <div style="opacity: 0.8; font-size: 0.9em; margin-top: 10px;">
                点击上方按钮查看具体启动方法
            </div>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>正在加载原型系统...
        </div>
        
        <div class="info-box">
            <h3>📋 功能特性</h3>
            <ul style="text-align: left; line-height: 1.6;">
                <li>✅ 股票分析和K线图表展示</li>
                <li>✅ 技术指标计算和可视化</li>
                <li>✅ 多策略股票扫描器</li>
                <li>✅ 自选股管理功能</li>
                <li>✅ 定时任务管理</li>
                <li>✅ 用户权限管理</li>
                <li>✅ 响应式设计支持</li>
            </ul>
        </div>
        
        <div class="info-box">
            <h3>🚀 启动方式</h3>
            <p><strong>方式一：双击启动脚本</strong></p>
            <ul style="text-align: left;">
                <li>Windows: 双击 <code>start.bat</code></li>
                <li>Mac/Linux: 双击 <code>start.sh</code> 或运行 <code>./start.sh</code></li>
            </ul>
            
            <p><strong>方式二：命令行启动</strong></p>
            <ul style="text-align: left;">
                <li>Python: <code>python3 启动原型.py</code></li>
                <li>Node.js: <code>node 启动原型.js</code></li>
                <li>开发模式: <code>npm run dev</code></li>
            </ul>
            
            <p><strong>注意：</strong>本原型使用模拟数据，所有功能均为演示用途</p>
        </div>
        
        <div style="margin-top: 30px; opacity: 0.8; font-size: 0.9em;">
            <p>让投资更智能，让决策更精准</p>
            <p>版本: v1.0.0 | 纯前端原型版</p>
        </div>
    </div>

    <script>
        function startWithPython() {
            alert(`🐍 使用 Python 启动原型：

在命令行/终端中运行：
python3 启动原型.py

或者双击 启动原型.py 文件（如果系统支持）

需要 Python 3.6+ 版本
- Windows: https://python.org
- Mac: brew install python3
- Ubuntu: sudo apt install python3`);
        }
        
        function startWithNode() {
            alert(`⚡️ 使用 Node.js 启动原型：

在命令行/终端中运行：
node 启动原型.js

或者双击启动脚本：
- Windows: start.bat
- Mac/Linux: start.sh

需要 Node.js 12+ 版本
- https://nodejs.org`);
        }
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }
        
        // 页面加载完成后的欢迎信息
        window.addEventListener('load', function() {
            console.log('🚀 Ultrathink 股票量化分析原型已准备就绪！');
            console.log('📊 演示账户: admin/admin123, user/user123, demo/demo123');
            console.log('💡 提示: 请使用启动脚本而非直接打开HTML文件');
        });
    </script>
</body>
</html>