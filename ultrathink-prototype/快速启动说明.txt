🚀 Ultrathink 股票量化分析原型 - 快速启动指南
================================================================

⚠️ 重要提示：
由于现代浏览器的安全限制，ES模块无法通过 file:// 协议直接运行
需要使用 HTTP 服务器来访问原型

🌟 启动方式（按推荐顺序）：

方式一：双击启动脚本（最简单）
----------------------------
Windows: 双击 start.bat
Mac/Linux: 双击 start.sh 或运行 ./start.sh

脚本会自动检测系统环境，优先使用 Python，备选 Node.js

方式二：命令行启动（推荐）
--------------------------
Python版本（推荐）：
python3 启动原型.py

Node.js版本：
node 启动原型.js

方式三：开发模式（需要Node.js环境）
---------------------------------
1. 打开命令行/终端
2. 运行：npm install（首次运行）
3. 运行：npm run dev
4. 访问：http://localhost:5173

🎯 演示账户信息：
================
管理员: admin / admin123
普通用户: user / user123  
演示用户: demo / demo123

✨ 核心功能：
============
- 股票分析和K线图表
- 技术指标计算展示
- 多策略扫描器
- 自选股管理
- 定时任务管理
- 用户权限控制

📝 注意事项：
============
1. 所有数据均为模拟数据，仅供演示
2. 推荐使用现代浏览器（Chrome、Firefox、Safari、Edge）
3. 支持响应式设计，可在移动端查看
4. 本地存储用于保存用户状态

🎨 技术亮点：
============
- Vue 3 + Composition API
- Element Plus UI框架
- ECharts图表库
- Mock API系统
- 响应式设计
- 深色主题

📞 技术支持：
============
如有问题请查看：
- README.md（详细说明）
- DEPLOYMENT.md（部署文档）

版本: v1.0.0
更新日期: 2025-08-15

让投资更智能，让决策更精准！