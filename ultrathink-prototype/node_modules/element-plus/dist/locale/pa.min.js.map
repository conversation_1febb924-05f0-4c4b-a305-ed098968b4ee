{"version": 3, "file": "pa.min.js", "sources": ["../../../../packages/locale/lang/pa.ts"], "sourcesContent": ["export default {\n  name: 'pa',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'تایید',\n      clear: 'پاکول',\n    },\n    datepicker: {\n      now: 'اوس',\n      today: 'نن',\n      cancel: 'ردول',\n      clear: 'پاکول',\n      confirm: 'تایید',\n      selectDate: 'نیټه وټاکئ',\n      selectTime: 'وخت وټاکئ',\n      startDate: 'پیل نیټه',\n      startTime: 'د پيل وخت',\n      endDate: 'د پای نیټه',\n      endTime: 'د پای وخت',\n      prevYear: 'تیر کال',\n      nextYear: 'راتلونکی کال',\n      prevMonth: 'تیره میاشت',\n      nextMonth: 'راتلونکې میاشت',\n      year: 'کال',\n      month1: 'جنوري',\n      month2: 'فبروري',\n      month3: 'مارچ',\n      month4: 'اپریل',\n      month5: 'می',\n      month6: 'جون',\n      month7: 'جولای',\n      month8: 'اګست',\n      month9: 'سپتمبر',\n      month10: 'اکتوبر',\n      month11: 'نومبر',\n      month12: 'دسمبر',\n      // week: 'week',\n      weeks: {\n        sun: 'یکشنبه',\n        mon: 'دوشنبه',\n        tue: 'سه​ شنبه',\n        wed: 'چهارشنبه',\n        thu: 'پنج​شنبه',\n        fri: 'جمعه',\n        sat: 'شنبه',\n      },\n      months: {\n        jan: 'جنوري',\n        feb: 'فبروري',\n        mar: 'مارچ',\n        apr: 'اپریل',\n        may: 'می',\n        jun: 'جون',\n        jul: 'جولای',\n        aug: 'اګست',\n        sep: 'سپتمبر',\n        oct: 'اکتوبر',\n        nov: 'نومبر',\n        dec: 'دسمبر',\n      },\n    },\n    select: {\n      loading: 'بار کول',\n      noMatch: 'هیڅه ونه موندل شول',\n      noData: 'هیڅ معلومات نشته',\n      placeholder: 'ځای لرونکی',\n    },\n    mention: {\n      loading: 'بار کول',\n    },\n    cascader: {\n      noMatch: 'هیڅه ونه موندل شول',\n      loading: 'بار کول',\n      placeholder: 'ځای لرونکی',\n      noData: 'هیڅ معلومات نشته',\n    },\n    pagination: {\n      goto: 'ورتګ',\n      pagesize: '/د پاڼې اندازه',\n      total: 'مجموعه {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'عنوان',\n      confirm: 'تایید',\n      cancel: 'لغوه کول',\n      error: 'تيروتنه',\n    },\n    upload: {\n      deleteTip: 'د حذف کولو لپاره پاکه تڼۍ فشار کړئ',\n      delete: 'ړنګول',\n      preview: 'مخکتنه',\n      continue: 'ادامه',\n    },\n    table: {\n      emptyText: 'هیڅ معلومات ونه موندل شول',\n      confirmFilter: 'تایید',\n      resetFilter: 'پاکول',\n      clearFilter: 'ټول',\n      sumText: 'مجموعه',\n    },\n    tree: {\n      emptyText: 'هیڅ معلومات ونه موندل شول',\n    },\n    transfer: {\n      noMatch: 'هیڅه ونه موندل شول',\n      noData: 'هیڅ معلومات نشته',\n      titles: ['لیسټ 1', 'لیسټ 2'],\n      filterPlaceholder: 'د متن کلیمې دننه کړئ',\n      noCheckedFormat: '{total} توکي',\n      hasCheckedFormat: '{checked} توکي از {total} توکي ټاکل شوی دي',\n    },\n    image: {\n      error: 'د انځور پورته کولو کې ستونزه',\n    },\n    pageHeader: {\n      title: 'بیرته راتګ',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,gCAAgC,CAAC,UAAU,CAAC,yDAAyD,CAAC,UAAU,CAAC,mDAAmD,CAAC,SAAS,CAAC,6CAA6C,CAAC,SAAS,CAAC,8CAA8C,CAAC,OAAO,CAAC,oDAAoD,CAAC,OAAO,CAAC,8CAA8C,CAAC,QAAQ,CAAC,uCAAuC,CAAC,QAAQ,CAAC,qEAAqE,CAAC,SAAS,CAAC,yDAAyD,CAAC,SAAS,CAAC,iFAAiF,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,gCAAgC,CAAC,OAAO,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,6CAA6C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,OAAO,CAAC,+FAA+F,CAAC,MAAM,CAAC,wFAAwF,CAAC,WAAW,CAAC,yDAAyD,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,+FAA+F,CAAC,OAAO,CAAC,uCAAuC,CAAC,WAAW,CAAC,yDAAyD,CAAC,MAAM,CAAC,wFAAwF,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,uEAAuE,CAAC,KAAK,CAAC,8CAA8C,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,6CAA6C,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,2KAA2K,CAAC,MAAM,CAAC,gCAAgC,CAAC,OAAO,CAAC,sCAAsC,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,oIAAoI,CAAC,aAAa,CAAC,gCAAgC,CAAC,WAAW,CAAC,gCAAgC,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oIAAoI,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,+FAA+F,CAAC,MAAM,CAAC,wFAAwF,CAAC,MAAM,CAAC,CAAC,4BAA4B,CAAC,4BAA4B,CAAC,CAAC,iBAAiB,CAAC,sGAAsG,CAAC,eAAe,CAAC,kCAAkC,CAAC,gBAAgB,CAAC,2IAA2I,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,iJAAiJ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}