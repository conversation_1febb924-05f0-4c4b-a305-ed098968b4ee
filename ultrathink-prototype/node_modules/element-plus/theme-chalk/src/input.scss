@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@mixin inset-prepend-border($color) {
  box-shadow: 1px 0 0 0 $color inset, 0 1px 0 0 $color inset,
    0 -1px 0 0 $color inset;
}

@mixin inset-append-border($color) {
  box-shadow: 0 1px 0 0 $color inset, 0 -1px 0 0 $color inset,
    -1px 0 0 0 $color inset;
}

@mixin inset-prepend-input-border($color) {
  box-shadow: 1px 0 0 0 $color inset, 1px 0 0 0 $color, 0 1px 0 0 $color inset,
    0 -1px 0 0 $color inset !important;
}

@mixin inset-append-input-border($color) {
  box-shadow: -1px 0 0 0 $color, -1px 0 0 0 $color inset, 0 1px 0 0 $color inset,
    0 -1px 0 0 $color inset !important;
}

@mixin mixed-input-border($color) {
  box-shadow: 0 0 0 1px $color inset;
}

@include b(textarea) {
  @include set-component-css-var('input', $input);
}

@include b(textarea) {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: bottom;
  font-size: getCssVar('font-size', 'base');

  @include e(inner) {
    position: relative;
    display: block;
    resize: vertical;
    padding: 5px map.get($input-padding-horizontal, 'default')-$border-width;
    line-height: 1.5;
    box-sizing: border-box;
    width: 100%;
    font-size: inherit;
    font-family: inherit;
    color: var(
      #{getCssVarName('input-text-color')},
      map.get($input, 'text-color')
    );
    background-color: var(
      #{getCssVarName('input-bg-color')},
      map.get($input, 'bg-color')
    );
    background-image: none;
    -webkit-appearance: none;
    @include inset-input-border(
      var(
        #{getCssVarName('input-border-color')},
        map.get($input, 'border-color')
      )
    );
    border-radius: getCssVarWithDefault(
      'input-border-radius',
      map.get($input, 'border-radius')
    );
    transition: getCssVar('transition-box-shadow');
    border: none;

    &::placeholder {
      color: getCssVarWithDefault(
        'input-placeholder-color',
        map.get($input, 'placeholder-color')
      );
    }

    &:hover {
      @include inset-input-border(#{getCssVar('input', 'hover-border-color')});
    }

    &:focus {
      outline: none;
      @include inset-input-border(#{getCssVar('input', 'focus-border-color')});
    }
  }

  & .#{$namespace}-input__count {
    color: getCssVar('color-info');
    background: getCssVar('fill-color', 'blank');
    position: absolute;
    font-size: 12px;
    line-height: 14px;
    bottom: 5px;
    right: 10px;
  }

  @include when(disabled) {
    .#{$namespace}-textarea__inner {
      @include inset-input-border(#{getCssVar('disabled-border-color')});
      background-color: map.get($input-disabled, 'fill');
      color: map.get($input-disabled, 'text-color');
      cursor: not-allowed;

      &::placeholder {
        color: map.get($input-disabled, 'placeholder-color');
      }
    }
  }

  @include when(exceed) {
    .#{$namespace}-textarea__inner {
      @include mixed-input-border(#{getCssVar('color-danger')});
    }

    .#{$namespace}-input__count {
      color: getCssVar('color-danger');
    }
  }
}

@include b(input) {
  @include set-component-css-var('input', $input);
}

@include b(input) {
  @include css-var-from-global('input-height', 'component-size');

  position: relative;
  font-size: getCssVar('font-size', 'base');
  display: inline-flex;
  width: getCssVar('input-width');
  line-height: getCssVar('input-height');
  box-sizing: border-box;
  vertical-align: middle;
  @include scroll-bar;

  & .#{$namespace}-input__clear,
  & .#{$namespace}-input__password {
    color: getCssVar('input-icon-color');
    font-size: map.get($input-font-size, 'default');
    cursor: pointer;

    &:hover {
      color: getCssVar('input-clear-hover-color');
    }
  }

  & .#{$namespace}-input__count {
    height: 100%;
    display: inline-flex;
    align-items: center;
    color: getCssVar('color-info');
    font-size: 12px;

    .#{$namespace}-input__count-inner {
      background: getCssVar('fill-color', 'blank');
      line-height: initial;
      display: inline-block;
      padding-left: 8px;
    }
  }

  @include e(wrapper) {
    display: inline-flex;
    flex-grow: 1;
    align-items: center;
    justify-content: center;
    padding: $border-width map.get($input-padding-horizontal, 'default')-$border-width;
    background-color: var(
      #{getCssVarName('input-bg-color')},
      map.get($input, 'bg-color')
    );
    background-image: none;
    border-radius: getCssVarWithDefault(
      'input-border-radius',
      map.get($input, 'border-radius')
    );
    cursor: text;
    transition: getCssVar('transition-box-shadow');
    transform: translate3d(0, 0, 0);
    @include inset-input-border(
      var(
        #{getCssVarName('input-border-color')},
        map.get($input, 'border-color')
      )
    );

    &:hover {
      @include inset-input-border(#{getCssVar('input', 'hover-border-color')});
    }

    @include when(focus) {
      @include inset-input-border(#{getCssVar('input', 'focus-border-color')});
    }
  }

  & {
    // use map.get as default value for date picker range
    @include set-css-var-value(
      'input-inner-height',
      calc(
        var(
            #{getCssVarName('input-height')},
            #{map.get($input-height, 'default')}
          ) - $border-width * 2
      )
    );
  }

  @include e(inner) {
    width: 100%;
    flex-grow: 1;
    -webkit-appearance: none;
    color: var(
      #{getCssVarName('input-text-color')},
      map.get($input, 'text-color')
    );
    font-size: inherit;
    height: getCssVar('input-inner-height');
    line-height: getCssVar('input-inner-height');
    padding: 0;
    outline: none;
    border: none;
    background: none;
    box-sizing: border-box;

    &:focus {
      outline: none;
    }

    &::placeholder {
      color: getCssVarWithDefault(
        'input-placeholder-color',
        map.get($input, 'placeholder-color')
      );
    }

    // override edge default style
    &[type='password']::-ms-reveal {
      display: none;
    }

    &[type='number'] {
      line-height: 1;
    }
  }

  @each $slot in (prefix, suffix) {
    @include e($slot) {
      display: inline-flex;
      white-space: nowrap;
      flex-shrink: 0;
      flex-wrap: nowrap;
      height: 100%;
      line-height: getCssVar('input-inner-height');
      text-align: center;
      color: var(
        #{getCssVarName('input-icon-color')},
        map.get($input, 'icon-color')
      );
      transition: all getCssVar('transition-duration');
      pointer-events: none;
    }

    @include e(#{$slot}-inner) {
      pointer-events: all;
      display: inline-flex;
      align-items: center;
      justify-content: center;

      @if $slot == prefix {
        > :last-child {
          margin-right: 8px;
        }

        > :first-child {
          &,
          &.#{$namespace}-input__icon {
            margin-left: 0;
          }
        }
      } @else {
        > :first-child {
          margin-left: 8px;
        }
      }
    }
  }

  & .#{$namespace}-input__icon {
    height: inherit;
    line-height: inherit;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all getCssVar('transition-duration');
    margin-left: 8px;
  }

  @include e(validateIcon) {
    pointer-events: none;
  }

  @include when(active) {
    .#{$namespace}-input__wrapper {
      @include mixed-input-border(
        var(
          #{getCssVarName('input-focus-color')},
          map.get($input, 'focus-color')
        )
      );
    }
  }

  @include when(disabled) {
    cursor: not-allowed;

    .#{$namespace}-input__wrapper {
      background-color: map.get($input-disabled, 'fill');
      cursor: not-allowed;
      @include mixed-input-border(map.get($input-disabled, 'border'));
    }

    .#{$namespace}-input__inner {
      color: map.get($input-disabled, 'text-color');
      -webkit-text-fill-color: map.get($input-disabled, 'text-color');
      cursor: not-allowed;

      &::placeholder {
        color: map.get($input-disabled, 'placeholder-color');
      }
    }

    .#{$namespace}-input__icon {
      cursor: not-allowed;
    }

    .#{$namespace}-input__prefix-inner,
    .#{$namespace}-input__suffix-inner {
      pointer-events: none;
    }
  }

  @include when(exceed) {
    .#{$namespace}-input__wrapper {
      @include mixed-input-border(#{getCssVar('color-danger')});
    }

    .#{$namespace}-input__suffix {
      .#{$namespace}-input__count {
        color: getCssVar('color-danger');
      }
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      @include css-var-from-global('input-height', ('component-size', $size));

      font-size: map.get($input-font-size, $size);

      @include e(wrapper) {
        padding: $border-width map.get($input-padding-horizontal, $size)-$border-width;
      }
      & {
        @include set-css-var-value(
          'input-inner-height',
          calc(
            var(
                #{getCssVarName('input-height')},
                #{map.get($input-height, $size)}
              ) - $border-width * 2
          )
        );
      }
    }
  }
}

@include b(input-group) {
  display: inline-flex;
  width: 100%;
  align-items: stretch;

  @include e((append, prepend)) {
    background-color: getCssVar('fill-color', 'light');
    color: getCssVar('color-info');
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
    border-radius: getCssVar('input-border-radius');
    padding: 0 20px;
    white-space: nowrap;

    &:focus {
      outline: none;
    }

    .#{$namespace}-select,
    .#{$namespace}-button {
      display: inline-block;
      flex: 1;
      margin: 0 -20px;
    }

    button.#{$namespace}-button,
    button.#{$namespace}-button:hover,
    div.#{$namespace}-select .#{$namespace}-select__wrapper,
    div.#{$namespace}-select:hover .#{$namespace}-select__wrapper {
      border-color: transparent;
      background-color: transparent;
      color: inherit;
    }

    .#{$namespace}-button,
    .#{$namespace}-input {
      font-size: inherit;
    }
  }

  @include e(prepend) {
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    @include inset-prepend-border(#{getCssVar('input-border-color')});
  }

  @include e(append) {
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    @include inset-append-border(#{getCssVar('input-border-color')});
  }

  @include m(prepend) {
    > .#{$namespace}-input__wrapper {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    @include e(prepend) {
      .#{$namespace}-select {
        .#{$namespace}-select__wrapper {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          @include inset-prepend-border(#{getCssVar('input-border-color')});
        }
      }
    }
  }

  @include m(append) {
    > .#{$namespace}-input__wrapper {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    @include e(append) {
      .#{$namespace}-select {
        .#{$namespace}-select__wrapper {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          @include inset-append-border(#{getCssVar('input-border-color')});
        }
      }
    }
  }
}

@include b(input-hidden) {
  display: none !important;
}
