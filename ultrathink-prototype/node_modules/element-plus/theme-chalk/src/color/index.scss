@use 'sass:color';
@use 'sass:string';

@function rgb2hex($color) {
  @return string.unquote('#' + #{string.slice(color.ie-hex-str($color), 4)});
}

// rgba color above solid color
@function mix-overlay-color($upper, $lower) {
  $opacity: color.alpha($upper);

  $red: color.channel($upper, 'red') * $opacity + color.channel($lower, 'red') *
    (1 - $opacity);
  $green: color.channel($upper, 'green') * $opacity +
    color.channel($lower, 'green') * (1 - $opacity);
  $blue: color.channel($upper, 'blue') * $opacity +
    color.channel($lower, 'blue') * (1 - $opacity);

  @return rgb2hex(rgb($red, $green, $blue));
}
