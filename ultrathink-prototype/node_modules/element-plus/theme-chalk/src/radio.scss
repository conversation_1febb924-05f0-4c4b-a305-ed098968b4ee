@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'mixins/var' as *;
@use 'mixins/button' as *;
@use 'common/var' as *;

$radio-font-size: () !default;
$radio-font-size: map.merge(
  (
    'large': 14px,
    'small': 12px,
  ),
  $radio-font-size
);

@include b(radio) {
  @include set-component-css-var('radio', $radio);
}

@include b(radio) {
  color: getCssVar('radio-text-color');
  font-weight: getCssVar('radio-font-weight');
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  outline: none;
  font-size: getCssVar('font-size', 'base');
  user-select: none;
  margin-right: 30px;
  height: map.get($radio-height, 'default');

  @each $size in (large, small) {
    &.#{$namespace}-radio--#{$size} {
      height: map.get($radio-height, $size);
    }
  }

  @include when(bordered) {
    padding: 0 map.get($checkbox-bordered-padding-right, 'default')-$border-width
      0 map.get($checkbox-bordered-padding-left, 'default')-$border-width;

    border-radius: getCssVar('border-radius-base');
    border: getCssVar('border');
    box-sizing: border-box;

    &.is-checked {
      border-color: getCssVar('color-primary');
    }

    &.is-disabled {
      cursor: not-allowed;
      border-color: getCssVar('border-color-lighter');
    }

    @each $size in (large, small) {
      &.#{$namespace}-radio--#{$size} {
        padding: 0
          map.get($checkbox-bordered-padding-right, $size)-$border-width
          0
          map.get($checkbox-bordered-padding-left, $size)-$border-width;
        border-radius: getCssVar('border-radius-base');

        .#{$namespace}-radio__label {
          font-size: map.get($button-font-size, $size);
        }

        .#{$namespace}-radio__inner {
          height: map.get($radio-bordered-input-height, $size);
          width: map.get($radio-bordered-input-width, $size);
        }
      }
    }
  }

  &:last-child {
    margin-right: 0;
  }

  @include e(input) {
    white-space: nowrap;
    cursor: pointer;
    outline: none;
    display: inline-flex;
    position: relative;
    vertical-align: middle;

    @include when(disabled) {
      .#{$namespace}-radio__inner {
        background-color: map.get($radio-disabled, 'input-fill');
        border-color: map.get($radio-disabled, 'input-border-color');
        cursor: not-allowed;

        &::after {
          cursor: not-allowed;
          background-color: map.get($radio-disabled, 'icon-color');
        }

        & + .#{$namespace}-radio__label {
          cursor: not-allowed;
        }
      }
      &.is-checked {
        .#{$namespace}-radio__inner {
          background-color: map.get($radio-disabled, 'checked-input-fill');
          border-color: map.get($radio-disabled, 'checked-input-border-color');

          &::after {
            background-color: map.get($radio-disabled, 'checked-icon-color');
          }
        }
      }
      & + span.#{$namespace}-radio__label {
        color: getCssVar('text-color', 'placeholder');
        cursor: not-allowed;
      }
    }

    @include when(checked) {
      .#{$namespace}-radio__inner {
        border-color: map.get($radio-checked, 'input-border-color');
        background: map.get($radio-checked, 'icon-color');

        &::after {
          background-color: getCssVar('color-white');
          transform: translate(-50%, -50%) scale(1);
        }
      }

      & + .#{$namespace}-radio__label {
        color: map.get($radio-checked, 'text-color');
      }
    }

    @include when(focus) {
      .#{$namespace}-radio__inner {
        border-color: getCssVar('radio-input-border-color-hover');
      }
    }
  }
  @include e(inner) {
    border: getCssVar('radio-input-border');
    border-radius: getCssVar('radio-input-border-radius');
    width: getCssVar('radio-input-width');
    height: getCssVar('radio-input-height');
    background-color: getCssVar('radio-input-bg-color');
    position: relative;
    cursor: pointer;
    display: inline-block;
    box-sizing: border-box;

    &:hover {
      border-color: getCssVar('radio-input-border-color-hover');
    }

    &::after {
      width: 4px;
      height: 4px;
      border-radius: getCssVar('radio-input-border-radius');
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      transition: transform 0.15s ease-in;
    }
  }

  @include e(original) {
    opacity: 0;
    outline: none;
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;

    &:focus-visible {
      & + .#{$namespace}-radio__inner {
        outline: 2px solid getCssVar('radio-input-border-color-hover');
        outline-offset: 1px;
        border-radius: getCssVar('radio-input-border-radius');
      }
    }
  }

  &:focus:not(:focus-visible):not(.is-focus):not(:active):not(.is-disabled) {
    /*获得焦点时 样式提醒*/
    .#{$namespace}-radio__inner {
      box-shadow: 0 0 2px 2px getCssVar('radio-input-border-color-hover');
    }
  }

  @include e(label) {
    font-size: getCssVar('radio-font-size');
    padding-left: 8px;
  }

  @each $size in (large, small) {
    &.#{$namespace}-radio--#{$size} {
      @include e(label) {
        font-size: map.get($radio-font-size, $size);
      }
      @include e(inner) {
        width: map.get($radio-font-size, $size);
        height: map.get($radio-font-size, $size);
      }
    }
  }
}
