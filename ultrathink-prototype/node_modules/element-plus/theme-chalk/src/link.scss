@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'mixins/utils' as *;
@use 'common/var' as *;

@include b(link) {
  @include set-component-css-var('link', $link);
}

@include b(link) {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  position: relative;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  padding: 0;
  font-size: getCssVar('link', 'font-size');
  font-weight: getCssVar('link', 'font-weight');
  color: getCssVar('link', 'text-color');

  @include when(hover-underline) {
    &:hover:after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      height: 0;
      bottom: 0;
      border-bottom: 1px solid getCssVar('link', 'hover-text-color');
    }
  }

  @include when(underline) {
    &:after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      height: 0;
      bottom: 0;
      border-bottom: 1px solid getCssVar('link', 'text-color');
    }
  }

  &:hover {
    color: getCssVar('link', 'hover-text-color');

    &:after {
      border-color: getCssVar('link', 'hover-text-color');
    }
  }

  & [class*='#{$namespace}-icon-'] {
    & + span {
      margin-left: 5px;
    }
  }

  @include e(inner) {
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }

  @each $type in $types {
    &.#{$namespace}-link--#{$type} {
      @include css-var-from-global(('link', 'text-color'), ('color', $type));
      @include css-var-from-global(
        ('link', 'hover-text-color'),
        ('color', $type, 'light-3')
      );
      @include css-var-from-global(
        ('link', 'disabled-text-color'),
        ('color', $type, 'light-5')
      );
    }
  }

  @include when(disabled) {
    color: getCssVar('link', 'disabled-text-color');
    cursor: not-allowed;

    &:after {
      border-color: getCssVar('link', 'disabled-text-color');
    }
  }
}
