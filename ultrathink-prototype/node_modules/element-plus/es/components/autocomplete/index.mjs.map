{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/autocomplete/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Autocomplete from './src/autocomplete.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAutocomplete: SFCWithInstall<typeof Autocomplete> =\n  withInstall(Autocomplete)\n\nexport default ElAutocomplete\n\nexport * from './src/autocomplete'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,cAAc,GAAG,WAAW,CAAC,YAAY;;;;"}