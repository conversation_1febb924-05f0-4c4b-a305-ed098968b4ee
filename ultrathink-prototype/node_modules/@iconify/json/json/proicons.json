{"prefix": "proicons", "info": {"name": "ProIcons", "total": 521, "version": "4.13.0", "author": {"name": "ProCode", "url": "https://github.com/ProCode-Software/proicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/ProCode-Software/proicons/blob/main/LICENSE"}, "samples": ["code", "checkmark", "photo-filter", "soundwave", "more", "folder"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "lastModified": **********, "icons": {"accessibility": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M6.867 5.543a1.58 1.58 0 0 0-2.155.576a1.57 1.57 0 0 0 .577 2.15l2.63 1.515a1 1 0 0 1 .5.866v3.536a1 1 0 0 1-.134.5l-2.408 4.162a1.57 1.57 0 0 0 .577 2.15a1.58 1.58 0 0 0 2.156-.576l3.258-5.629h.258l3.258 5.629a1.58 1.58 0 0 0 2.156.576a1.57 1.57 0 0 0 .577-2.15l-2.402-4.15a1 1 0 0 1-.135-.502V10.65a1 1 0 0 1 .501-.866l2.63-1.514a1.57 1.57 0 0 0 .577-2.15a1.58 1.58 0 0 0-2.155-.577l-3.636 2.094a3 3 0 0 1-2.994 0z\"/><path d=\"M14.623 5.414a2.623 2.623 0 1 1-5.246 0a2.623 2.623 0 0 1 5.246 0\"/></g>"}, "add": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4 12h8m0 0h8m-8 0V4m0 8v8\"/>"}, "add-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path d=\"M12 8.5v7M8.5 12h7\"/></g>"}, "add-rhombus": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.775 14.475a3.5 3.5 0 0 1 0-4.95l5.75-5.75a3.5 3.5 0 0 1 4.95 0l5.75 5.75a3.5 3.5 0 0 1 0 4.95l-5.75 5.75a3.5 3.5 0 0 1-4.95 0zM8.25 12H12m0 0h3.75M12 12V8.25M12 12v3.75\"/>"}, "add-square": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12 8.5v7M8.5 12h7\"/><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"4\"/></g>"}, "add-square-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M10.63 7.66v5.94m-2.97-2.97h5.94\"/><rect width=\"14\" height=\"14\" x=\"3.63\" y=\"3.63\" rx=\"3\"/><path d=\"M20.63 7.63v7a6 6 0 0 1-6 6h-7\"/></g>"}, "airplane": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.238 4.512a1.762 1.762 0 1 1 3.524 0V8.9l6.733 3.535a1 1 0 0 1 .535.885v.431a.6.6 0 0 1-.712.59l-6.556-1.24v4.107a.6.6 0 0 0 .317.53l1.862.996a1.5 1.5 0 0 1 .792 1.322v.447a.6.6 0 0 1-.73.586L12 20.204l-4.003.885a.6.6 0 0 1-.73-.586v-.447a1.5 1.5 0 0 1 .792-1.322l1.862-.997a.6.6 0 0 0 .317-.529v-4.106L3.682 14.34a.6.6 0 0 1-.712-.59v-.43a1 1 0 0 1 .535-.886L10.238 8.9z\"/>"}, "airplane-landing": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 20.75h18.5m-2.05-7.453a1.783 1.783 0 1 1-.923 3.445L8.185 14.04a4 4 0 0 1-1.32-.628l-2.14-1.543A3.04 3.04 0 0 1 3.47 9.271l.11-2.508a.607.607 0 0 1 .765-.56l.436.117a1.52 1.52 0 0 1 1.086 1.121l.486 2.082c.051.218.218.39.434.448l4.015 1.076l.506-6.735a.607.607 0 0 1 .763-.541l.422.113c.363.097.643.388.725.755l1.692 7.509z\"/>"}, "airplane-takeoff": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 20.75h18.5M18.575 6.299a1.783 1.783 0 0 1 1.783 3.089L11.31 14.61a4 4 0 0 1-1.377.49l-2.604.422a3.04 3.04 0 0 1-2.725-.948L2.91 12.723a.607.607 0 0 1 .145-.936l.391-.226a1.52 1.52 0 0 1 1.56.025l1.816 1.128c.19.118.43.122.624.01l3.6-2.078l-4.404-5.12a.607.607 0 0 1 .156-.922l.378-.218c.326-.188.73-.18 1.047.02l6.506 4.113z\"/>"}, "alarm-clock": {"body": "<g fill=\"none\" stroke=\"currentColor\"><path stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"m21.25 7.072l-3.574-3.574M2.75 7.072l3.574-3.574\"/><circle cx=\"12\" cy=\"12.753\" r=\"7.75\" stroke-width=\"1.503\"/><path stroke-linecap=\"round\" stroke-width=\"1.503\" d=\"m17.514 18.267l2.236 2.235M6.486 18.267L4.25 20.502\"/><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M11.9 8.353v4.25l3.685 2.117\"/></g>"}, "album": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" stroke-linecap=\"round\" rx=\"3\"/><path d=\"M16.25 3.75h-6v7.517a.2.2 0 0 0 .341.142l1.952-1.952a1 1 0 0 1 1.414 0l1.952 1.952a.2.2 0 0 0 .341-.142z\"/></g>"}, "alert-circle": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-width=\"1.5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 12.438v-5\"/><circle cx=\"1.25\" cy=\"1.25\" r=\"1.25\" fill=\"currentColor\" transform=\"matrix(1 0 0 -1 10.75 17.063)\"/></g>"}, "alert-rhombus": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.775 14.475a3.5 3.5 0 0 1 0-4.95l5.75-5.75a3.5 3.5 0 0 1 4.95 0l5.75 5.75a3.5 3.5 0 0 1 0 4.95l-5.75 5.75a3.5 3.5 0 0 1-4.95 0z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 12.435v-5\"/><circle cx=\"1.25\" cy=\"1.25\" r=\"1.25\" fill=\"currentColor\" transform=\"matrix(1 0 0 -1 10.75 17.06)\"/></g>"}, "alert-triangle": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-width=\"1.5\" d=\"M5.732 20.5c-2.29 0-3.723-2.498-2.581-4.5L9.419 5.006c1.144-2.008 4.018-2.008 5.163 0L20.849 16c1.142 2.002-.291 4.5-2.581 4.5z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 13.375V9\"/><circle cx=\"1.25\" cy=\"1.25\" r=\"1.25\" fill=\"currentColor\" transform=\"matrix(1 0 0 -1 10.75 17.938)\"/></g>"}, "align-bottom": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 21.25h18.5\"/><rect width=\"6\" height=\"10\" rx=\"2\" transform=\"matrix(-1 0 0 1 19.75 7.75)\"/><rect width=\"6\" height=\"15\" rx=\"2\" transform=\"matrix(-1 0 0 1 10.25 2.75)\"/></g>"}, "align-horizontal-centers": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M19.75 12h1.5m-11 0h3.5m-11 0h1.5\"/><rect width=\"6\" height=\"10\" rx=\"2\" transform=\"matrix(-1 0 0 1 19.75 7)\"/><rect width=\"6\" height=\"15\" rx=\"2\" transform=\"matrix(-1 0 0 1 10.25 4.5)\"/></g>"}, "align-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 2.75v18.5\"/><rect width=\"6\" height=\"10\" rx=\"2\" transform=\"matrix(0 -1 -1 0 16.25 19.75)\"/><rect width=\"6\" height=\"15\" rx=\"2\" transform=\"matrix(0 -1 -1 0 21.25 10.25)\"/></g>"}, "align-right": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M21.25 2.75v18.5\"/><rect width=\"6\" height=\"10\" x=\"7.75\" y=\"19.75\" rx=\"2\" transform=\"rotate(-90 7.75 19.75)\"/><rect width=\"6\" height=\"15\" x=\"2.75\" y=\"10.25\" rx=\"2\" transform=\"rotate(-90 2.75 10.25)\"/></g>"}, "align-top": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 2.75h18.5\"/><rect width=\"6\" height=\"10\" x=\"19.75\" y=\"16.25\" rx=\"2\" transform=\"rotate(180 19.75 16.25)\"/><rect width=\"6\" height=\"15\" x=\"10.25\" y=\"21.25\" rx=\"2\" transform=\"rotate(180 10.25 21.25)\"/></g>"}, "align-vertical-centers": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12 19.75v1.5m0-11v3.5m0-11v1.5\"/><rect width=\"6\" height=\"10\" x=\"7\" y=\"19.75\" rx=\"2\" transform=\"rotate(-90 7 19.75)\"/><rect width=\"6\" height=\"15\" x=\"4.5\" y=\"10.25\" rx=\"2\" transform=\"rotate(-90 4.5 10.25)\"/></g>"}, "amazon": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.3\" d=\"M17.284 17.724h1.967c.543 0 .983.44.983.983v1.967\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m6.901 7.111l1.976.2a.57.57 0 0 0 .558-.34c.268-.573.924-1.59 2.209-1.59c.258 0 .478.041.665.108c1.04.373 1.02 1.77 1.02 2.874c0 0-2.415-.058-3.992.476c-.465.157-.934.333-1.36.578c-1.223.705-1.77 2.276-1.969 3.022c-.071.27-.098.549-.08.828c.08 1.178.558 3.184 2.968 3.599c2.247.386 3.749-.64 4.43-1.267c.23-.213.612-.22.817.018l.88 1.017a.59.59 0 0 0 .825.066l1.676-1.402a.59.59 0 0 0 .101-.796l-.267-.374c-.215-.3-.422-.613-.517-.97c-.099-.372-.09-.763-.09-1.148V6.704c0-2.372-2.295-3.681-3.94-3.85l-.592-.057a6 6 0 0 0-.972-.034a6 6 0 0 0-1.162.196c-1.537.399-3.348 1.765-3.597 3.664c-.033.25.162.463.413.488m6.428 4.576v-1.432s-1.905-.047-2.664.363c-.312.168-.574.491-.775.813a2.54 2.54 0 0 0-.291 1.94c.066.275.158.54.282.695c.259.323.876.439 1.166.476q.156.022.311.003c.157-.022.413-.08.67-.23c.39-.229.695-.595.903-.995c.118-.226.208-.471.273-.676a3.2 3.2 0 0 0 .125-.957\" clip-rule=\"evenodd\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M4.5 18.707c1.933 1.619 4.584 2.548 7.375 2.548c1.853 0 3.643-.41 5.203-1.158a11 11 0 0 0 .573-.295\"/></g>"}, "anchor": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.75 12a9.25 9.25 0 1 0 18.5 0M12 21.25V7.75\"/><circle cx=\"12\" cy=\"5.25\" r=\"2.5\"/><path stroke-linecap=\"round\" d=\"M2.75 12h4m10.5 0h4\"/></g>"}, "android": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M16.764 9.907a9.3 9.3 0 0 1 1.777 1.46a9.8 9.8 0 0 1 2.005 3.158c.343.873.564 1.791.655 2.727c.054.55-.399.998-.951.998H3.75c-.552 0-1.005-.449-.951-.998c.091-.936.312-1.854.655-2.727a9.8 9.8 0 0 1 2.005-3.157a9.3 9.3 0 0 1 1.777-1.461m9.528 0a9 9 0 0 0-1.224-.65a8.85 8.85 0 0 0-3.54-.74a8.85 8.85 0 0 0-3.54.74a9 9 0 0 0-1.224.65m9.528 0L18.49 6.75M7.236 9.907L5.51 6.75\"/><circle cx=\"8.75\" cy=\"14.063\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"15.25\" cy=\"14.063\" r=\"1.25\" fill=\"currentColor\"/></g>"}, "angle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M4.25 4.25v12.5a3 3 0 0 0 3 3h12.5\"/><path d=\"M4.25 10.356h.394a9 9 0 0 1 9 9v.394\"/></g>"}, "app-remove": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"6.5\" height=\"6.5\" rx=\"2\" transform=\"matrix(1 0 0 -1 3.75 20.25)\"/><path d=\"M20.25 20.25L17 17m0 0l-3.25-3.25M17 17l-3.25 3.25M17 17l3.25-3.25\"/><rect width=\"6.5\" height=\"6.5\" rx=\"2\" transform=\"matrix(1 0 0 -1 3.75 10.25)\"/><rect width=\"6.5\" height=\"6.5\" rx=\"2\" transform=\"matrix(1 0 0 -1 13.75 10.25)\"/></g>"}, "app-store": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M10.904 13.794h2.024l-1.012-1.755z\"/><path stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m8.843 17.367l-1.58 2.74A1.784 1.784 0 1 1 4.17 18.32l.55-.953m4.122 0H4.72m4.122 0h6.146m-10.268 0h-.466a1.786 1.786 0 0 1 0-3.573h2.527l3.073-5.327l-1.03-1.787a1.784 1.784 0 1 1 3.092-1.786a1.784 1.784 0 1 1 3.09 1.786l-1.03 1.786m-1.049 5.328h-2.024l1.012-1.755m1.012 1.755l2.061 3.573m-2.06-3.573l-1.013-1.755m3.073 5.328l1.58 2.74a1.784 1.784 0 1 0 3.092-1.787l-.55-.953h.634a1.786 1.786 0 0 0 0-3.573H17.05l-3.073-5.328m0 0l-2.06 3.573\"/></g>"}, "apple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\"><path stroke-width=\"1.5\" d=\"M10.102 7.78a4 4 0 0 0-1.185-.161c-1.918 0-4.167 1.695-4.167 5.359c0 3.36 2.358 7.21 3.801 7.822c.328.14.731.227 1.084.193c.707-.07 1.348-.421 2.023-.622c.228-.038.5-.074.719-.075c.24-.002.51.06.774.12c.715.164 1.505.65 2.255.577c.267-.026.633-.187.746-.239l.07-.03c.85-.343 1.988-1.826 2.759-3.703c.162-.396-.052-.833-.404-1.077c-.999-.694-1.663-1.934-1.663-3.195c0-.969.313-1.854.907-2.523l.02-.023a8 8 0 0 1 .45-.5c.255-.248.326-.645.082-.904c-.808-.854-1.832-1.277-2.764-1.277a3.6 3.6 0 0 0-1.106.156c-.138.033-.778.192-1.222.355c-.257.071-.635.167-.81.21a.7.7 0 0 1-.192.023c-.735-.024-1.47-.303-2.177-.487Z\" clip-rule=\"evenodd\"/><path fill=\"currentColor\" d=\"M13.86 5.178c-.733.878-1.975 1.047-1.975 1.047s-.05-1.256.684-2.135s1.976-1.046 1.976-1.046s.05 1.256-.684 2.134Z\"/></g>"}, "apps": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"6.5\" height=\"6.5\" x=\"3.75\" y=\"3.75\" rx=\"2\"/><path d=\"M15.586 3.818a2 2 0 0 1 2.828 0l1.768 1.768a2 2 0 0 1 0 2.828l-1.768 1.768a2 2 0 0 1-2.828 0l-1.768-1.768a2 2 0 0 1 0-2.828z\"/><rect width=\"6.5\" height=\"6.5\" x=\"3.75\" y=\"13.75\" rx=\"1.5\"/><rect width=\"6.5\" height=\"6.5\" x=\"13.75\" y=\"13.75\" rx=\"2\"/></g>"}, "apps-add": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"6.5\" height=\"6.5\" x=\"3.75\" y=\"3.75\" rx=\"2\"/><path d=\"M17 3.75V7m0 0v3.25M17 7h-3.25M17 7h3.25\"/><rect width=\"6.5\" height=\"6.5\" x=\"3.75\" y=\"13.75\" rx=\"2\"/><rect width=\"6.5\" height=\"6.5\" x=\"13.75\" y=\"13.75\" rx=\"2\"/></g>"}, "arc": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.25 4.75h-.5c-7.732 0-14 6.268-14 14v.5\"/>"}, "archive": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.25 12.5h3.5m-10-5.25a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5h-9.5a3.5 3.5 0 0 1-3.5-3.5zm0 1.5h16.5\"/>"}, "archive-add-2": {"body": "<g fill=\"none\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 1 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.25 12.5h2.063m7.937-1.687V7.25a3.5 3.5 0 0 0-3.5-3.5h-9.5a3.5 3.5 0 0 0-3.5 3.5v9.5a3.5 3.5 0 0 0 3.5 3.5h3.594M3.75 8.75h16.5\"/></g>"}, "arrow-clockwise": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.719 14.385a8.25 8.25 0 1 1-.824-6.26l.42.908m.58-4.658v3.75a1 1 0 0 1-.58.908m-4.17.092h3.75c.15 0 .293-.033.42-.092\"/>"}, "arrow-counterclockwise": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.281 14.385a8.25 8.25 0 1 0 .824-6.26l-.477.88m-.523-4.63v3.75a1 1 0 0 0 .523.88m4.227.12h-3.75a1 1 0 0 1-.477-.12\"/>"}, "arrow-down": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 4v15.879M5.25 13.75l5.69 5.69c.292.292.676.439 1.06.439m6.75-6.129l-5.69 5.69a1.5 1.5 0 0 1-1.06.439\"/>"}, "arrow-download": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12.074 3.25v12.478M6.19 10.465l4.822 4.822c.293.293.677.44 1.06.44m5.883-5.262l-4.822 4.822c-.293.293-.677.44-1.06.44m8.677.788v.935a3.3 3.3 0 0 1-3.3 3.3H6.55a3.3 3.3 0 0 1-3.3-3.3v-.935\"/>"}, "arrow-enter": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.75 5.623V9.52a4 4 0 0 1-4 4H3.871m4.236 4.857L4.31 14.58a1.5 1.5 0 0 1-.44-1.061m4.236-4.857L4.31 12.46c-.293.293-.44.677-.44 1.061\"/>"}, "arrow-export": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.798 12H2.871m5.157-5.778l-4.717 4.717c-.293.293-.44.677-.44 1.061m5.157 5.778l-4.717-4.717A1.5 1.5 0 0 1 2.87 12m17.88-7.905v15.81\"/>"}, "arrow-forward": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.25 18.219c0-2.352 0-3.527.383-4.455a5.06 5.06 0 0 1 2.743-2.743c.928-.383 2.103-.383 4.455-.383h8.298m-4.236-4.857l3.796 3.796c.293.293.44.677.44 1.061m-4.236 4.857l3.796-3.796c.293-.293.44-.677.44-1.061\"/>"}, "arrow-import": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.25 12h12.927M10.02 6.222l4.717 4.717c.293.293.44.677.44 1.061m-5.157 5.778l4.717-4.717c.293-.293.44-.677.44-1.061m5.573-7.905v15.81\"/>"}, "arrow-left": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20 12H4.121m6.129 6.75l-5.69-5.69A1.5 1.5 0 0 1 4.122 12m6.129-6.75l-5.69 5.69A1.5 1.5 0 0 0 4.122 12\"/>"}, "arrow-left-right": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m16.25 17l3.94-3.94c.292-.292.439-.676.439-1.06M16.25 7l3.94 3.94c.292.292.439.676.439 1.06M7.75 17l-3.94-3.94A1.5 1.5 0 0 1 3.372 12M7.75 7l-3.94 3.94A1.5 1.5 0 0 0 3.371 12m0 0H20.63\"/>"}, "arrow-maximize": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 12.75v6c0 .414.168.79.44 1.06m7.06.44h-6a1.5 1.5 0 0 1-1.06-.44m16.06-8.56v-6c0-.414-.168-.79-.44-1.06m-7.06-.44h6c.414 0 .79.168 1.06.44M4.19 19.81l.56-.56l14.5-14.5l.56-.56\"/>"}, "arrow-minimize": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m2.75 21.25l6.5-6.5l.56-.56m.44 7.06v-6c0-.414-.168-.79-.44-1.06m-7.06-.44h6c.414 0 .79.168 1.06.44m3.94-11.44v6c0 .414.168.79.44 1.06m7.06.44h-6a1.5 1.5 0 0 1-1.06-.44m7.06-7.06l-6.5 6.5l-.56.56\"/>"}, "arrow-move": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 9.005V2.75M9 5.324l2.273-2.273c.201-.2.464-.301.727-.301m3 2.574l-2.273-2.273c-.2-.2-.464-.301-.727-.301M14.995 12h6.255m-2.574-3l2.273 2.273c.2.201.301.464.301.727m-2.574 3l2.273-2.273c.2-.2.301-.464.301-.727M12 14.995v6.255m-3-2.574l2.273 2.273c.201.2.464.301.727.301m3-2.574l-2.273 2.273c-.2.2-.464.301-.727.301M9.005 12H2.75m2.574-3l-2.273 2.273c-.2.201-.301.464-.301.727m2.574 3l-2.273-2.273c-.2-.2-.301-.464-.301-.727\"/>"}, "arrow-redo": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m14.03 21.25l-8.26-8.26a5.999 5.999 0 0 1 8.483-8.483l4.734 4.734l.622.622m-7.199.505h6.077c.447 0 .848-.195 1.122-.505m.378-7.072v6.077c0 .382-.142.73-.378.995\"/>"}, "arrow-redo-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m14.75 12l3.293-3.293A1 1 0 0 0 18.336 8M14.75 4l3.293 3.293a1 1 0 0 1 .293.707M16.75 19.5H10.5a5.75 5.75 0 0 1 0-11.5h7.836\"/>"}, "arrow-reply": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.75 18.219c0-2.352 0-3.527-.383-4.455a5.06 5.06 0 0 0-2.743-2.743c-.928-.383-2.103-.383-4.455-.383H3.871m4.236-4.857L4.31 9.577c-.293.293-.44.677-.44 1.061m4.236 4.857L4.31 11.699a1.5 1.5 0 0 1-.44-1.061\"/>"}, "arrow-right": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4 12h15.879m-6.129 6.75l5.69-5.69c.292-.292.439-.676.439-1.06M13.75 5.25l5.69 5.69c.292.292.439.676.439 1.06\"/>"}, "arrow-rotate-clockwise": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.75 12a8.25 8.25 0 1 1 12.375 7.145l-1.199.421m-.051-3.316v3q0 .166.051.316m3.949.684h-3a1 1 0 0 1-.949-.684\"/><path d=\"M14.5 11.75a2.25 2.25 0 1 1-4.5 0a2.25 2.25 0 0 1 4.5 0Z\"/></g>"}, "arrow-rotate-counterclockwise": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M20.25 12a8.25 8.25 0 1 0-12.375 7.145l1.168.503m.082-3.398v3q-.002.213-.082.398m-3.918.602h3a1 1 0 0 0 .918-.602\"/><path d=\"M14.5 11.75a2.25 2.25 0 1 1-4.5 0a2.25 2.25 0 0 1 4.5 0Z\"/></g>"}, "arrow-sort": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 9.867L8.186 6.053a1.5 1.5 0 0 0-1.061-.44M2.25 9.868l3.814-3.814c.293-.293.677-.44 1.061-.44m0 13.395V5.614m9.75-.124v13.394m4.875-4.253l-3.814 3.814c-.293.293-.677.44-1.061.44M12 14.63l3.814 3.814c.293.293.677.44 1.061.44\"/>"}, "arrow-swap": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m9.618 12.249l-3.814 3.814c-.293.293-.44.677-.44 1.06M9.619 22l-3.814-3.814a1.5 1.5 0 0 1-.44-1.061m13.395 0H5.365m-.124-9.751h13.394m-4.253-4.875l3.814 3.814c.293.293.44.677.44 1.06m-4.254 4.876l3.814-3.814c.293-.293.44-.677.44-1.061\"/>"}, "arrow-sync": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.548 9.735a8.75 8.75 0 0 1 16.03-2.11l.335.759m.837-5.134v4.147a1 1 0 0 1-.837.987m-4.31.013h4.147q.083 0 .163-.013M3.25 20.75v-4.147a1 1 0 0 1 1-1m0 0h4.147m-4.147 0l.172.772a8.75 8.75 0 0 0 16.03-2.11\"/>"}, "arrow-sync-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.915 18.532A7.83 7.83 0 0 1 11.831 3.92h1.043m-1.755-2.17l1.462 1.462a1 1 0 0 1 .293.708m-1.755 2.169l1.462-1.462a1 1 0 0 0 .293-.707m3.211 1.299a7.83 7.83 0 0 1-3.916 14.612h-1.043M12.881 22l-1.462-1.462a1 1 0 0 1-.293-.707m1.755-2.17l-1.462 1.462a1 1 0 0 0-.293.708\"/>"}, "arrow-trending": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m3.25 16.75l4.793-4.793a1 1 0 0 1 1.414 0l2.586 2.586a1 1 0 0 0 1.414 0L19.75 8.25l.56-.56m-5.56-.44h4.5c.414 0 .79.168 1.06.44m.44 5.56v-4.5c0-.414-.168-.79-.44-1.06\"/>"}, "arrow-undo": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m9.97 21.25l8.26-8.26a5.999 5.999 0 0 0-8.483-8.483L5.013 9.24l-.622.622m7.199.505H5.513c-.447 0-.848-.195-1.122-.505M4.013 2.79v6.077c0 .382.143.73.378.995\"/>"}, "arrow-undo-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.25 12L5.957 8.707A1 1 0 0 1 5.664 8M9.25 4L5.957 7.293A1 1 0 0 0 5.664 8M7.25 19.5h6.25a5.75 5.75 0 0 0 0-11.5H5.664\"/>"}, "arrow-up": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 20V4.121M5.25 10.25l5.69-5.69A1.5 1.5 0 0 1 12 4.121m6.75 6.129l-5.69-5.69A1.5 1.5 0 0 0 12 4.122\"/>"}, "arrow-up-down": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m7 16.25l3.94 3.94c.292.292.676.439 1.06.439m5-4.379l-3.94 3.94a1.5 1.5 0 0 1-1.06.439M7 7.75l3.94-3.94A1.5 1.5 0 0 1 12 3.371m5 4.379l-3.94-3.94A1.5 1.5 0 0 0 12 3.372m0 0V20.63\"/>"}, "arrow-upload": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 16.349V3.87M6.118 9.132l4.821-4.821c.293-.293.677-.44 1.061-.44m5.882 5.261l-4.821-4.821A1.5 1.5 0 0 0 12 3.87m8.75 12.645v.935a3.3 3.3 0 0 1-3.3 3.3H6.55a3.3 3.3 0 0 1-3.3-3.3v-.935\"/>"}, "asterisk": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m12 3.75l.004 8.243l7.009-4.118L12.008 12l7.005 4.125l-7.009-4.118L12 20.25l-.004-8.243l-7.009 4.118L11.992 12L4.987 7.875l7.009 4.118z\"/>"}, "attach": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m2.861 13.625l9.342-9.342a5.235 5.235 0 1 1 7.403 7.403L10.88 20.41a2.867 2.867 0 0 1-4.054-4.054l8.372-8.373\"/>"}, "background-color": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M13.04 4.75h3.66c1.68 0 2.52 0 3.162.327a3 3 0 0 1 1.311 1.311c.327.642.327 1.482.327 3.162v5.4c0 1.68 0 2.52-.327 3.162a3 3 0 0 1-1.311 1.311c-.642.327-1.482.327-3.162.327H7.8c-1.68 0-2.52 0-3.162-.327a3 3 0 0 1-1.311-1.311C3 17.47 3 16.63 3 14.95v-.93\"/><path d=\"m6.407 2.818l5.384 5.385M6.407 2.818L3.01 6.214c-.696.696-1.044 1.044-1.174 1.446c-.057.176-.086.36-.086.543m4.657-5.385L5.589 2m6.202 6.203l-3.396 3.396c-.696.696-1.044 1.044-1.445 1.174a1.76 1.76 0 0 1-1.086 0c-.401-.13-.75-.478-1.445-1.174L3.01 10.191c-.696-.696-1.044-1.044-1.174-1.445a1.8 1.8 0 0 1-.086-.543m10.042 0H1.75m10.672 4.094l1.485-2.448l1.744 2.313a1.824 1.824 0 0 1-.668 2.491c-1.546.893-3.322-.74-2.562-2.356\"/></g>"}, "background-color-accent": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 14.95v-1.68h.633c.168.163.33.33.486.462c.307.261.696.54 1.204.706a3.5 3.5 0 0 0 2.168 0c.507-.165.896-.445 1.203-.706c.28-.236.583-.54.895-.852l1.185-1.186c-1.322 3.12 2.099 6.198 5.084 4.475a3.574 3.574 0 0 0 1.308-4.882a2 2 0 0 0-.118-.179l-1.744-2.313a1.75 1.75 0 0 0-1.764-.657a1.75 1.75 0 0 0-.511-1.173l-.739-.716V4.75h4.41c1.68 0 2.52 0 3.162.327a3 3 0 0 1 1.311 1.311c.327.642.327 1.482.327 3.162v5.4c0 1.68 0 2.52-.327 3.162a3 3 0 0 1-1.311 1.311c-.642.327-1.482.327-3.162.327H7.8c-1.68 0-2.52 0-3.162-.327a3 3 0 0 1-1.311-1.311C3 17.47 3 16.63 3 14.95\" clip-rule=\"evenodd\"/>"}, "backspace": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M6.71 6.084a3.5 3.5 0 0 1 2.572-1.126h7.968a3.5 3.5 0 0 1 3.5 3.5v7.084a3.5 3.5 0 0 1-3.5 3.5H9.282a3.5 3.5 0 0 1-2.571-1.126l-3.27-3.542a3.5 3.5 0 0 1 0-4.748zm4.11 3.281l5.271 5.27m0-5.27l-5.27 5.27\"/>"}, "badge": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12 3.75H8.75a5 5 0 0 0-5 5v6.5a5 5 0 0 0 5 5h6.5a5 5 0 0 0 5-5V12\"/><circle cx=\"18.25\" cy=\"5.75\" r=\"3\"/></g>"}, "bank": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"3\" x=\"2.75\" y=\"18.376\" rx=\"1\"/><path d=\"M11.04 3.15L3.27 7.4a1 1 0 0 0-.52.877v.997a.6.6 0 0 0 .6.6h17.3a.6.6 0 0 0 .6-.6v-.997a1 1 0 0 0-.52-.877l-7.77-4.25a2 2 0 0 0-1.92 0M5.25 9.874v8.51m13.5-8.51v8.51m-4.25-8.51v8.51m-5-8.51v8.51\"/></g>"}, "bar-chart": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 10.25a2 2 0 0 1 2-2h4.167v13H4.75a2 2 0 0 1-2-2zm12.333 2h4.167a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-4.167zm-6.166-7.5a2 2 0 0 1 2-2h2.166a2 2 0 0 1 2 2v16.5H8.917z\"/>"}, "bar-graph": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 21h18.5\"/><rect width=\"6\" height=\"10\" x=\"4\" y=\"7.5\" rx=\"2\"/><rect width=\"6\" height=\"15\" x=\"14\" y=\"2.5\" rx=\"2\"/></g>", "hidden": true}, "basketball": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path d=\"M18.54 18.54s-1.852-4.79-5.415-8.227s-8.823-3.445-8.823-3.445\"/><path d=\"M13.912 13.912c2.241-2.242 4.86-3.613 7.113-3.927a9.24 9.24 0 0 1-2.484 8.555a9.24 9.24 0 0 1-8.557 2.486c.315-2.254 1.686-4.873 3.928-7.114M5.46 5.459a9.24 9.24 0 0 1 8.554-2.485c-.314 2.253-1.685 4.872-3.926 7.114S5.227 13.7 2.973 14.015A9.24 9.24 0 0 1 5.46 5.459\"/></g>"}, "battery": {"body": "<g fill=\"none\"><rect width=\"16\" height=\"12\" x=\"2.75\" y=\"6\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"3.25\"/><rect width=\"7\" height=\"7\" x=\"5.25\" y=\"8.5\" fill=\"currentColor\" rx=\"1.5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.25 10v4\"/></g>"}, "battery-full": {"body": "<g fill=\"none\"><rect width=\"16\" height=\"12\" x=\"2.75\" y=\"6\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"3.25\"/><rect width=\"11\" height=\"7\" x=\"5.25\" y=\"8.5\" fill=\"currentColor\" rx=\"1.5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.25 10v4\"/></g>"}, "beach": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M17.135 4.027c1.969 1.137 3.369 3.074 3.89 5.386a9.4 9.4 0 0 1 .005 4.066c-.361 1.67-2.311 2.182-3.791 1.328l-9.492-5.48c-1.48-.855-2.012-2.8-.746-3.948a9.4 9.4 0 0 1 3.523-2.03c2.263-.703 4.641-.46 6.61.678m0 0c-1.368-.79-4.554 2.17-7.118 6.61m7.118-6.61c1.367.789.397 5.028-2.167 9.469m-2.475-1.43l-3.61 6.254m7.027 2.725a9.306 9.306 0 0 0-13.159 0\"/>"}, "beaker": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.456 3.75v5.09a3 3 0 0 1-.557 1.742l-1.736 2.436M9.456 3.75h-1.65m1.65 0h5.088m0 0v5.09a3 3 0 0 0 .557 1.742l1.736 2.436M14.544 3.75h1.65m-9.031 9.268l-2.378 3.337a2.465 2.465 0 0 0 2.007 3.895h10.416a2.465 2.465 0 0 0 2.007-3.895l-2.378-3.337m-9.674 0h9.674\"/>"}, "bell": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path stroke-linejoin=\"round\" d=\"M18.934 14.98a3 3 0 0 1-.457-1.59V9.226a6.477 6.477 0 0 0-12.954 0v4.162a3 3 0 0 1-.457 1.592l-1.088 1.74a1 1 0 0 0 .848 1.53h14.348a1 1 0 0 0 .848-1.53z\"/><path d=\"M10 21.25h4\"/></g>"}, "bell-dot": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18.477 9.75v3.64a3 3 0 0 0 .456 1.59l1.09 1.74a1 1 0 0 1-.849 1.53H4.826a1 1 0 0 1-.848-1.53l1.088-1.74a3 3 0 0 0 .457-1.59V9.226A6.477 6.477 0 0 1 14.52 3.26\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M10 21.25h4\"/><circle cx=\"18.5\" cy=\"5.5\" r=\"2.5\" fill=\"currentColor\"/></g>"}, "bell-off": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path d=\"M10 21.25h4\"/><path stroke-linejoin=\"round\" d=\"M17.188 18.25H4.826a1 1 0 0 1-.848-1.53l1.089-1.74a3 3 0 0 0 .457-1.59V9.226c0-.788.14-1.543.398-2.242M7.62 4.458l.06-.055a6.477 6.477 0 0 1 10.798 4.825v4.161a3 3 0 0 0 .457 1.592l1.088 1.74q.068.106.104.219\"/><path stroke-linejoin=\"round\" d=\"m20.719 21.782l-3.531-3.531L5.922 6.985L2.218 3.282\"/></g>"}, "bluesky": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.247 5.646c.068-1.031-.695-1.98-1.702-1.724c-3.68.933-6.648 5.997-7.545 7.755c-.896-1.758-3.865-6.822-7.545-7.755c-1.007-.255-1.77.693-1.702 1.724l.3 4.466c.151 2.28 2.116 4.014 4.41 3.894l.49-.036c-.039.007-2.904.518-3.39 1.981c-.658 1.983 1.28 3.408 1.28 3.408c.022.024 1.894 2.096 3.765.899c1.463-.71 2.393-4.01 2.393-4.01s.929 3.3 2.392 4.01c1.88 1.203 3.762-.896 3.764-.9c.016-.01 1.935-1.431 1.28-3.407c-.49-1.473-3.391-1.98-3.391-1.98l.493.035c2.293.12 4.258-1.615 4.41-3.894z\"/>"}, "bluetooth": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m6.25 7.5l5.454 4.5m0 0l5.246 3.982a.65.65 0 0 1 0 1.036l-4.202 3.19a.65.65 0 0 1-1.043-.517V4.31a.65.65 0 0 1 1.043-.518l4.202 3.19a.65.65 0 0 1 0 1.036zm0 0L6.25 16.5\"/>"}, "board": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M17.75 4H6.25a3.5 3.5 0 0 0-3.5 3.5v9a3.5 3.5 0 0 0 3.5 3.5h11.5a3.5 3.5 0 0 0 3.5-3.5v-9a3.5 3.5 0 0 0-3.5-3.5\"/><path fill=\"currentColor\" d=\"M17.1 6.95H6.9a1.2 1.2 0 0 0-1.2 1.2v.483a1.2 1.2 0 0 0 1.2 1.2h10.2a1.2 1.2 0 0 0 1.2-1.2V8.15a1.2 1.2 0 0 0-1.2-1.2m0 5.64h-2.9a1.2 1.2 0 0 0-1.2 1.2v2.06a1.2 1.2 0 0 0 1.2 1.2h2.9a1.2 1.2 0 0 0 1.2-1.2v-2.06a1.2 1.2 0 0 0-1.2-1.2m-8.1 0H6.9a1.2 1.2 0 0 0-1.2 1.2v2.06a1.2 1.2 0 0 0 1.2 1.2H9a1.2 1.2 0 0 0 1.2-1.2v-2.06a1.2 1.2 0 0 0-1.2-1.2\"/></g>"}, "bolt": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.896 11.457c.64 0 .924.804.426 1.206l-10.45 8.434c-.505.407-1.238-.057-1.086-.687l1.615-6.696H4.104a.678.678 0 0 1-.455-1.182l10.63-9.604c.489-.442 1.257.002 1.118.646l-1.698 7.883z\"/>"}, "book": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M4.5 16.25V5.75a3 3 0 0 1 3-3h11a1 1 0 0 1 1 1v12.5H7.375M4.5 16.245v2.38\"/><path d=\"M18.5 21.25H7a2.5 2.5 0 0 1 0-5h12.5v4a1 1 0 0 1-1 1\"/></g>"}, "book-2": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M4.5 4.749a2 2 0 0 1 2-2h11a2 2 0 0 1 2 2v13h-15zm0 13v1.503a2 2 0 0 0 2 2h13\"/><rect width=\"8\" height=\"3\" x=\"8\" y=\"6.25\" rx=\".6\"/></g>"}, "book-add": {"body": "<g fill=\"none\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 1 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.5 16.245v2.38m2.875-2.375h2.969H7a2.5 2.5 0 0 0-2.5 2.5m6.875 2.5H7a2.5 2.5 0 0 1-2.5-2.5m0 0v-13a3 3 0 0 1 3-3h11a1 1 0 0 1 1 1v6.844\"/></g>"}, "book-add-2": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.5 18.749v-12.8c0-1.12 0-1.68.218-2.108a2 2 0 0 1 .874-.874c.428-.218.988-.218 2.108-.218h8.6c1.12 0 1.68 0 2.108.218a2 2 0 0 1 .874.874c.218.427.218.987.218 2.108v4.301m-15 8.499c0 .467 0 .7.039.895a2 2 0 0 0 1.568 1.569c.195.039.429.039.896.039h3.247M4.5 18.749h4.75M8.96 9.25h6.08c.336 0 .504 0 .632-.065a.6.6 0 0 0 .263-.263C16 8.794 16 8.626 16 8.29V7.21c0-.336 0-.504-.065-.632a.6.6 0 0 0-.263-.263c-.128-.065-.296-.065-.632-.065H8.96c-.336 0-.504 0-.632.065a.6.6 0 0 0-.263.263C8 6.706 8 6.874 8 7.21v1.08c0 .336 0 .504.065.632a.6.6 0 0 0 .263.263c.128.065.296.065.632.065\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 1 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "book-info": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.5 16.25V5.75a3 3 0 0 1 3-3h11a1 1 0 0 1 1 1v12.5H7.375M4.5 16.245v2.38\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18.5 21.25H7a2.5 2.5 0 0 1 0-5h12.5v4a1 1 0 0 1-1 1\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 9.45v3.79\"/><circle cx=\"12\" cy=\"6.217\" r=\"1.197\" fill=\"currentColor\"/></g>"}, "book-info-2": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.5 4.749a2 2 0 0 1 2-2h11a2 2 0 0 1 2 2v13h-15z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 10.07v4.79\"/><circle cx=\"12\" cy=\"6.837\" r=\"1.197\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.5 17.749v1.503a2 2 0 0 0 2 2h13\"/></g>"}, "book-letter": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M4.5 16.25V5.75a3 3 0 0 1 3-3h11a1 1 0 0 1 1 1v12.5H7.375M4.5 16.245v2.38\"/><path d=\"M18.5 21.25H7a2.5 2.5 0 0 1 0-5h12.5v4a1 1 0 0 1-1 1m-9.842-8l1.193-2.872m0 0h4.298m-4.298 0l1.83-4.4c.126-.304.513-.304.639 0l1.829 4.4m0 0l1.193 2.872\"/></g>"}, "book-marked": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" d=\"M4.5 16.25V5.75a3 3 0 0 1 3-3h11a1 1 0 0 1 1 1v12.5H7.375M4.5 16.245v2.38\"/><path stroke-linecap=\"round\" d=\"M18.5 21.25H7a2.5 2.5 0 0 1 0-5h12.5v4a1 1 0 0 1-1 1\"/><path d=\"M15.5 2.75h-6v7.517a.2.2 0 0 0 .341.142l1.952-1.952a1 1 0 0 1 1.414 0l1.952 1.952a.2.2 0 0 0 .341-.142z\"/></g>"}, "book-open": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 7.21a2 2 0 0 1 2-2H8.5a3.5 3.5 0 0 1 3.5 3.5v10.885l-1.015-.721a4 4 0 0 0-2.318-.74H4.75a2 2 0 0 1-2-2zm18.5 0a2 2 0 0 0-2-2H15.5a3.5 3.5 0 0 0-3.5 3.5v10.885l1.015-.721a4 4 0 0 1 2.317-.74h3.918a2 2 0 0 0 2-2z\"/>"}, "bookmark": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.75 20.113c0 .498.554.803.983.54l5.702-3.48a1.09 1.09 0 0 1 1.13 0l5.702 3.48a.644.644 0 0 0 .983-.54V6.25a3 3 0 0 0-3-3h-8.5a3 3 0 0 0-3 3z\"/>"}, "bookmark-add": {"body": "<g fill=\"none\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 12a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V6h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 0 1-1 0V7h-2.493a.5.5 0 1 1 0-1H17V3.507a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.25 13.5v6.613a.644.644 0 0 1-.983.54l-5.702-3.48a1.09 1.09 0 0 0-1.13 0l-5.702 3.48a.644.644 0 0 1-.983-.54V6.25a3 3 0 0 1 3-3h3.31\"/></g>"}, "bookmark-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.75 20.654a.6.6 0 0 0 .916.51l5.308-3.282a1 1 0 0 1 1.052 0l5.308 3.282a.6.6 0 0 0 .916-.51V8.75a3 3 0 0 0-3-3h-7.5a3 3 0 0 0-3 3z\"/><path stroke-linecap=\"round\" d=\"M20.25 17.65v-8.9a6 6 0 0 0-6-6h-6.5\"/></g>"}, "bookmark-multiple-var": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M4.421 8.45v11.884c0 .763.88 1.19 1.48.718l4.883-3.843a.914.914 0 0 1 1.13 0l4.884 3.843a.914.914 0 0 0 1.48-.718V8.45a3.2 3.2 0 0 0-3.2-3.2H7.621a3.2 3.2 0 0 0-3.2 3.2Z\"/><path stroke-linecap=\"round\" d=\"M21.278 16.334V8.25a6 6 0 0 0-6-6H8.421\"/></g>", "hidden": true}, "border-all": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 20.25h4.25a4 4 0 0 0 4-4V12M12 20.25H7.75a4 4 0 0 1-4-4V12M12 20.25V3.75m0 0H7.75a4 4 0 0 0-4 4V12M12 3.75h4.25a4 4 0 0 1 4 4V12m-16.5 0h16.5\"/>"}, "box": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m12 12l8.073-4.625M12 12v9.25M12 12L7.963 9.688m12.11-2.313a3.17 3.17 0 0 0-1.165-1.156L16.25 4.696m3.823 2.679c.275.472.427 1.015.427 1.58v6.09a3.15 3.15 0 0 1-1.592 2.736l-5.316 3.046A3.2 3.2 0 0 1 12 21.25M3.926 7.375a3.14 3.14 0 0 0-.426 1.58v6.09c0 1.13.607 2.172 1.592 2.736l5.316 3.046A3.2 3.2 0 0 0 12 21.25M3.926 7.375a3.17 3.17 0 0 1 1.166-1.156l5.316-3.046a3.2 3.2 0 0 1 3.184 0l2.658 1.523M3.926 7.375l4.037 2.313m0 0l8.287-4.992\"/>"}, "box-add": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m11 12l8.073-4.625M11 12L6.963 9.688M11 12v2.281m8.073-6.906a3.17 3.17 0 0 0-1.165-1.156L15.25 4.696m3.823 2.679c.275.472.427 1.015.427 1.58v1.608M2.926 7.374a3.14 3.14 0 0 0-.426 1.58v6.09c0 1.13.607 2.172 1.592 2.736l5.316 3.046A3.2 3.2 0 0 0 11 21.25M2.926 7.375a3.17 3.17 0 0 1 1.166-1.156l5.316-3.046a3.2 3.2 0 0 1 3.184 0l2.658 1.523M2.926 7.375l4.037 2.313m0 0l8.287-4.992\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 1 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "braces": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9 20.25a3 3 0 0 1-3-3v0v-1.343a4 4 0 0 0-1.172-2.829L3.75 12l1.078-1.078A4 4 0 0 0 6 8.093V6.75v0a3 3 0 0 1 3-3v0m6 16.5a3 3 0 0 0 3-3v0v-1.343a4 4 0 0 1 1.172-2.829L20.25 12l-1.078-1.078A4 4 0 0 1 18 8.093V6.75v0a3 3 0 0 0-3-3v0\"/>"}, "braces-variable": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7 20.25a2 2 0 0 1-2-2v-2.343a4 4 0 0 0-1.172-2.829L2.75 12l1.078-1.078A4 4 0 0 0 5 8.093V5.75a2 2 0 0 1 2-2m10 16.5a2 2 0 0 0 2-2v-2.343a4 4 0 0 1 1.172-2.829L21.25 12l-1.078-1.078A4 4 0 0 1 19 8.093V5.75a2 2 0 0 0-2-2M9 8.143l6 7.714m0-7.714l-6 7.714\"/>"}, "brackets": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.75 3.75h-2a2 2 0 0 0-2 2v12.5a2 2 0 0 0 2 2h2m6.5-16.5h2a2 2 0 0 1 2 2v12.5a2 2 0 0 1-2 2h-2\"/>"}, "branch": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7 8.25a2.75 2.75 0 1 0 0-5.5a2.75 2.75 0 0 0 0 5.5m0 0v7.5m0-7.5c0 2.9 2.35 5.25 5.25 5.25h2M7 15.75a2.75 2.75 0 1 0 0 5.5a2.75 2.75 0 0 0 0-5.5m7.25-2.25a2.75 2.75 0 1 0 5.5 0a2.75 2.75 0 0 0-5.5 0\"/>"}, "branch-compare": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M8.25 5.5a2.75 2.75 0 1 1-5.5 0a2.75 2.75 0 0 1 5.5 0m13 13a2.75 2.75 0 1 1-5.5 0a2.75 2.75 0 0 1 5.5 0\"/><path d=\"M5.5 8.25v7.25a3 3 0 0 0 3 3h4.336M10.75 16l1.793 1.793a1 1 0 0 1 .293.707M10.75 21l1.793-1.793a1 1 0 0 0 .293-.707m5.664-2.75V8.5a3 3 0 0 0-3-3h-4.336M13.25 8l-1.793-1.793a1 1 0 0 1-.293-.707M13.25 3l-1.793 1.793a1 1 0 0 0-.293.707\"/></g>"}, "branch-fork": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7 8.25a2.75 2.75 0 1 0 0-5.5a2.75 2.75 0 0 0 0 5.5m0 0V12m0 3.75a2.75 2.75 0 1 0 0 5.5a2.75 2.75 0 0 0 0-5.5m0 0V12m10-3.75a2.75 2.75 0 1 0 0-5.5a2.75 2.75 0 0 0 0 5.5m0 0V9a3 3 0 0 1-3 3H7\"/>"}, "branch-fork-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M6.5 8.25a2.75 2.75 0 1 0 0-5.5a2.75 2.75 0 0 0 0 5.5m0 0V10a2 2 0 0 0 2 2h3m6-3.75a2.75 2.75 0 1 0 0-5.5a2.75 2.75 0 0 0 0 5.5m0 0V10a2 2 0 0 1-2 2h-4m0 0v3.75m0 0a2.75 2.75 0 1 0 0 5.5a2.75 2.75 0 0 0 0-5.5\"/>"}, "branch-pull-request": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M8.25 5.5a2.75 2.75 0 1 1-5.5 0a2.75 2.75 0 0 1 5.5 0m13 13a2.75 2.75 0 1 1-5.5 0a2.75 2.75 0 0 1 5.5 0m-13 0a2.75 2.75 0 1 1-5.5 0a2.75 2.75 0 0 1 5.5 0M5.5 8.25v7.5\"/><path d=\"M18.5 15.75V8.5a3 3 0 0 0-3-3h-4.336M13.25 8l-1.793-1.793a1 1 0 0 1-.293-.707M13.25 3l-1.793 1.793a1 1 0 0 0-.293.707\"/></g>"}, "briefcase": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.75 9.894a2.5 2.5 0 0 1 2.5-2.5h11.5a2.5 2.5 0 0 1 2.5 2.5V17.5a2.5 2.5 0 0 1-2.5 2.5H6.25a2.5 2.5 0 0 1-2.5-2.5z\"/><path d=\"M17.75 7.394H6.25a2.5 2.5 0 0 0-2.5 2.5v.303a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3v-.303a2.5 2.5 0 0 0-2.5-2.5M8.603 5.5a1.5 1.5 0 0 1 1.5-1.5h3.794a1.5 1.5 0 0 1 1.5 1.5v1.894H8.603z\"/></g>"}, "briefcase-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 10.5A2.5 2.5 0 0 1 6.25 8h11.5a2.5 2.5 0 0 1 2.5 2.5v7a2.5 2.5 0 0 1-2.5 2.5H6.25a2.5 2.5 0 0 1-2.5-2.5zm4.853-5a1.5 1.5 0 0 1 1.5-1.5h3.794a1.5 1.5 0 0 1 1.5 1.5V8H8.603z\"/>"}, "brightness": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"4.25\"/><path stroke-linecap=\"round\" d=\"M12 2.75v1.5M5.46 5.46l1.06 1.06M12 19.75v1.5m5.48-3.77l1.06 1.06M2.75 12h1.5m1.21 6.54l1.06-1.06M19.75 12h1.5m-3.77-5.48l1.06-1.06\"/></g>"}, "broom": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m13.444 17.626l.707-.707a5 5 0 0 0 0-7.071m-.707 7.778l-7.071-7.071m7.07 7.07l-2.828 4.243l-8.485-8.485l4.243-2.828m0 0l.707-.707a5 5 0 0 1 7.07 0m0 0l6.718-6.718\"/>"}, "bug": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.5 9.08a3.23 3.23 0 0 1 3.23-3.23h2.54a3.23 3.23 0 0 1 3.23 3.23v6.27a4.5 4.5 0 0 1-4.5 4.5v0a4.5 4.5 0 0 1-4.5-4.5zm9 3.77h4.75m-18.5 0H7.5m2.25-9.7v.45A2.25 2.25 0 0 0 12 5.85v0a2.25 2.25 0 0 0 2.25-2.25v-.45M16.5 16.6h1.253a2.5 2.5 0 0 1 2.5 2.5v1.75M7.5 16.6H6.247a2.5 2.5 0 0 0-2.5 2.5v1.75M16.5 9.1h1.253a2.5 2.5 0 0 0 2.5-2.5V4.85M7.5 9.1H6.247a2.5 2.5 0 0 1-2.5-2.5V4.85\"/>"}, "bug-add": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M16.5 10.438V9.08a3.23 3.23 0 0 0-3.23-3.231h-2.54A3.23 3.23 0 0 0 7.5 9.08v6.27a4.5 4.5 0 0 0 3.23 4.319M2.75 12.85H7.5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.75 3.15v.45A2.25 2.25 0 0 0 12 5.85v0a2.25 2.25 0 0 0 2.25-2.25v-.45M7.5 16.6H6.247a2.5 2.5 0 0 0-2.5 2.5v1.75M16.5 9.1h1.253a2.5 2.5 0 0 0 2.5-2.5V4.85M7.5 9.1H6.247a2.5 2.5 0 0 1-2.5-2.5V4.85\"/><path fill=\"currentColor\" d=\"M17.5 12a5.5 5.5 0 1 1 0 11a5.5 5.5 0 0 1 0-11m0 2a.5.5 0 0 0-.501.501v2.498h-2.498a.501.501 0 1 0 0 1.002h2.498v2.498a.501.501 0 0 0 1.002 0v-2.498h2.498l.1-.01a.502.502 0 0 0 0-.982l-.1-.01h-2.498v-2.498a.5.5 0 0 0-.501-.5\"/></g>"}, "bug-play": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" d=\"M12 19.85a4.5 4.5 0 0 1-4.5-4.5V9.08a3.23 3.23 0 0 1 3.23-3.23h2.54a3.23 3.23 0 0 1 3.23 3.23v1.326M2.75 12.85H7.5m2.25-9.7v.45A2.25 2.25 0 0 0 12 5.85v0a2.25 2.25 0 0 0 2.25-2.25v-.45M7.5 16.6H6.247a2.5 2.5 0 0 0-2.5 2.5v1.75M16.5 9.1h1.253a2.5 2.5 0 0 0 2.5-2.5V4.85M7.5 9.1H6.247a2.5 2.5 0 0 1-2.5-2.5V4.85\"/><path d=\"M14.496 13.978c0-.89.982-1.428 1.731-.95l4.505 2.872a1.126 1.126 0 0 1 0 1.9l-4.505 2.872a1.126 1.126 0 0 1-1.731-.95z\"/></g>"}, "building-multiple": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.75 5a1.5 1.5 0 0 0-1.5-1.5H9.288a1 1 0 0 0-1 1v1H4.75a2 2 0 0 0-2 2v11a2 2 0 0 0 2 2h10zm0 3.5h4.5a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-4.5zm0 4h2.5m-2.5 4h2.5\"/><circle cx=\"6.75\" cy=\"9.5\" r=\"1\" fill=\"currentColor\"/><circle cx=\"6.75\" cy=\"13\" r=\"1\" fill=\"currentColor\"/><circle cx=\"6.75\" cy=\"16.5\" r=\"1\" fill=\"currentColor\"/><circle cx=\"10.75\" cy=\"9.5\" r=\"1\" fill=\"currentColor\"/><circle cx=\"10.75\" cy=\"13\" r=\"1\" fill=\"currentColor\"/><circle cx=\"10.75\" cy=\"16.5\" r=\"1\" fill=\"currentColor\"/></g>"}, "bullet-list": {"body": "<g fill=\"none\"><circle cx=\"4.443\" cy=\"5.081\" r=\"1.331\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.123 5.08h11.765\"/><circle cx=\"4.443\" cy=\"12\" r=\"1.331\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.123 12h11.765\"/><circle cx=\"4.443\" cy=\"18.919\" r=\"1.331\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.123 18.92h11.765\"/></g>"}, "bullet-list-square": {"body": "<g fill=\"none\"><circle cx=\"7.877\" cy=\"8.25\" r=\"1\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M11.062 8.25h5.31\"/><circle cx=\"7.877\" cy=\"12\" r=\"1\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M11.062 12h5.31\"/><circle cx=\"7.877\" cy=\"15.75\" r=\"1\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M11.062 15.75h5.31\"/><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" stroke=\"currentColor\" stroke-width=\"1.5\" rx=\"4\"/></g>"}, "bullet-list-square-add": {"body": "<g fill=\"none\"><circle cx=\"7.877\" cy=\"8.25\" r=\"1\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M11.062 8.25h5.31\"/><circle cx=\"7.877\" cy=\"12\" r=\"1\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M11.062 12h1.31\"/><circle cx=\"7.877\" cy=\"15.75\" r=\"1\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M20.25 10.25v-2.5a4 4 0 0 0-4-4h-8.5a4 4 0 0 0-4 4v8.5a4 4 0 0 0 4 4h2.5\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 1 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "bullet-list-tree": {"body": "<g fill=\"none\"><circle cx=\"4.443\" cy=\"5.081\" r=\"1.331\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.123 5.08h11.765\"/><circle cx=\"4.443\" cy=\"12\" r=\"1.331\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.123 12h11.765\"/><circle cx=\"9.701\" cy=\"18.919\" r=\"1.331\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.38 18.92h6.508\"/></g>"}, "button": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"11\" x=\"2.75\" y=\"6.5\" rx=\"4\"/><path d=\"M7 12h10\"/></g>"}, "cake": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M4.25 13.25a2.5 2.5 0 0 1 2.5-2.5h10.5a2.5 2.5 0 0 1 2.5 2.5v8H4.25z\"/><path d=\"m4.25 14.87l2.249 1.45a3 3 0 0 0 3.252 0l.623-.4a3 3 0 0 1 3.252 0l.623.4a3 3 0 0 0 3.252 0l2.249-1.45m1.5 6.375H2.75M12 2.75l1.414 1.414a2 2 0 1 1-2.828 0zm0 4.83v3.17\"/></g>"}, "calculator": {"body": "<g fill=\"none\"><rect width=\"14.5\" height=\"18.5\" x=\"4.75\" y=\"2.75\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"3\"/><rect width=\"7.5\" height=\"3.75\" x=\"8.25\" y=\"6.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"1\"/><circle cx=\"8.5\" cy=\"13.5\" r=\"1\" fill=\"currentColor\"/><circle cx=\"12\" cy=\"13.5\" r=\"1\" fill=\"currentColor\"/><circle cx=\"15.5\" cy=\"13.5\" r=\"1\" fill=\"currentColor\"/><circle cx=\"8.5\" cy=\"17.5\" r=\"1\" fill=\"currentColor\"/><circle cx=\"12\" cy=\"17.5\" r=\"1\" fill=\"currentColor\"/><circle cx=\"15.5\" cy=\"17.5\" r=\"1\" fill=\"currentColor\"/></g>"}, "calendar": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M17.25 4.75H6.75a3.5 3.5 0 0 0-3.5 3.5v9.5a3.5 3.5 0 0 0 3.5 3.5h10.5a3.5 3.5 0 0 0 3.5-3.5v-9.5a3.5 3.5 0 0 0-3.5-3.5m-14 4.5h17.5M7.361 4.75v-2m9.25 2v-2\"/>"}, "call": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.829 16.171a20.9 20.9 0 0 1-4.846-7.614c-.573-1.564-.048-3.282 1.13-4.46l.729-.728a2.11 2.11 0 0 1 2.987 0l1.707 1.707a2.11 2.11 0 0 1 0 2.987l-.42.42a1.81 1.81 0 0 0 0 2.56l3.84 3.841a1.81 1.81 0 0 0 2.56 0l.421-.42a2.11 2.11 0 0 1 2.987 0l1.707 1.707a2.11 2.11 0 0 1 0 2.987l-.728.728c-1.178 1.179-2.896 1.704-4.46 1.131a20.9 20.9 0 0 1-7.614-4.846Z\"/>"}, "call-end": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 8.25c2.517 0 5 .555 7.3 1.621c1.252.581 1.95 1.895 1.95 3.276V14a1.75 1.75 0 0 1-1.75 1.75h-2A1.75 1.75 0 0 1 15.75 14v-.492a1.5 1.5 0 0 0-1.5-1.5h-4.5a1.5 1.5 0 0 0-1.5 1.5V14a1.75 1.75 0 0 1-1.75 1.75h-2A1.75 1.75 0 0 1 2.75 14v-.853c0-1.38.698-2.695 1.95-3.276A17.3 17.3 0 0 1 12 8.25Z\"/>"}, "calligraphy-pen": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.16 2.75v1.049c0 1.187 0 1.78-.231 2.233a2.12 2.12 0 0 1-.926.927c-.454.23-1.047.23-2.234.23h-.423M4.84 2.75v1.049c0 1.187 0 1.78.231 2.233c.203.4.528.724.926.927c.454.23 1.047.23 2.234.23h.423m0 0L6.319 11.47c-.367.673-.55 1.01-.61 1.362a2.1 2.1 0 0 0 .054.935c.1.344.32.657.762 1.283l2.704 3.83c.934 1.323 1.4 1.983 1.98 2.217a2.12 2.12 0 0 0 1.582 0c.58-.234 1.047-.894 1.98-2.216l2.704-3.83c.442-.627.662-.94.761-1.284a2.1 2.1 0 0 0 .055-.935c-.06-.352-.243-.689-.61-1.362l-2.335-4.28m-6.692 0h6.692M12 13.325v7.522\"/><circle cx=\"12\" cy=\"13.325\" r=\"1.673\" fill=\"currentColor\"/></g>"}, "camera": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12.857 3.189h-1.714c-.681 0-1.022 0-1.331.094c-.274.083-.529.22-.75.401c-.25.205-.438.489-.816 1.056L7.103 6.454c-1.524 0-2.286 0-2.868.296a2.72 2.72 0 0 0-1.188 1.19c-.297.581-.297 1.343-.297 2.867v5.651c0 1.524 0 2.286.297 2.868c.26.512.677.928 1.188 1.189c.582.296 1.344.296 2.868.296h9.794c1.524 0 2.286 0 2.868-.296a2.72 2.72 0 0 0 1.188-1.19c.297-.581.297-1.343.297-2.867v-5.651c0-1.524 0-2.286-.297-2.868a2.72 2.72 0 0 0-1.188-1.189c-.582-.296-1.344-.296-2.868-.296L15.754 4.74c-.378-.567-.567-.85-.816-1.056a2.2 2.2 0 0 0-.75-.401c-.309-.094-.65-.094-1.331-.094\"/><path d=\"M15.775 13.212a3.775 3.775 0 1 1-7.55 0a3.775 3.775 0 0 1 7.55 0\"/></g>"}, "cancel": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m5 19l7-7m0 0l7-7m-7 7L5 5m7 7l7 7\"/>"}, "cancel-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path stroke-linecap=\"round\" d=\"m8.875 8.875l6.25 6.25m0-6.25l-6.25 6.25\"/></g>"}, "cancel-octagon": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path stroke-linejoin=\"round\" d=\"M7.583 3.336a2 2 0 0 1 1.414-.586h6.006a2 2 0 0 1 1.414.586l4.247 4.247a2 2 0 0 1 .586 1.414v6.006a2 2 0 0 1-.586 1.414l-4.247 4.247a2 2 0 0 1-1.414.586H8.997a2 2 0 0 1-1.414-.586l-4.247-4.247a2 2 0 0 1-.586-1.414V8.997a2 2 0 0 1 .586-1.414z\"/><path d=\"m8.75 8.75l6.5 6.5m0-6.5l-6.5 6.5\"/></g>"}, "cancel-square": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><path d=\"M8.25 3.75a4.5 4.5 0 0 0-4.5 4.5v7.5a4.5 4.5 0 0 0 4.5 4.5h7.5a4.5 4.5 0 0 0 4.5-4.5v-7.5a4.5 4.5 0 0 0-4.5-4.5z\"/><path stroke-linecap=\"round\" d=\"m8.655 8.655l6.69 6.69m0-6.69l-6.69 6.69\"/></g>"}, "candy": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M9.02 7.53L7.53 9.02A5.269 5.269 0 0 0 14 17.243M9.02 7.53q.294-.295.62-.533m-.62.532l.62-.532m0 0q.177-.128.36-.24m0 11.106V6.757m0 0a5.28 5.28 0 0 1 4.288-.54a5.268 5.268 0 0 1 2.183 8.764l-1.49 1.49a5.3 5.3 0 0 1-.981.772m0-11.106v11.106\"/><path d=\"M16.47 7.53a3.16 3.16 0 0 1 0-4.471a1.053 1.053 0 0 1 1.486-.005l2.99 2.99a1.053 1.053 0 0 1-.005 1.486a3.16 3.16 0 0 1-4.47 0M7.53 16.47a3.16 3.16 0 0 0-4.471 0a1.053 1.053 0 0 0-.005 1.486l2.99 2.99a1.053 1.053 0 0 0 1.486-.005a3.16 3.16 0 0 0 0-4.47\"/></g>"}, "cart": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"9.549\" cy=\"19.049\" r=\"1.701\"/><circle cx=\"16.96\" cy=\"19.049\" r=\"1.701\"/><path d=\"m5.606 5.555l2.01 6.364c.309.978.463 1.467.76 1.829c.26.32.599.567.982.72c.435.173.947.173 1.973.173h3.855c1.026 0 1.538 0 1.972-.173c.384-.153.722-.4.983-.72c.296-.362.45-.851.76-1.829l.409-1.296l.24-.766l.331-1.05a2.5 2.5 0 0 0-2.384-3.252zm0 0l-.011-.037a7 7 0 0 0-.14-.42a2.92 2.92 0 0 0-2.512-1.84C2.84 3.25 2.727 3.25 2.5 3.25\"/></g>"}, "cent": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12.085 3.875v2.417m0 14.083v-2.417m4.382-1.705a5.84 5.84 0 0 1-4.382 1.705m4.382-9.961a5.84 5.84 0 0 0-4.382-1.705m0 11.666A5.836 5.836 0 0 1 6.5 12.125a5.84 5.84 0 0 1 5.585-5.833\"/>"}, "center-horizontal": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M20.25 20.25V3.75m-16.5 16.5V3.75\"/><rect width=\"6\" height=\"13\" rx=\"2\" transform=\"matrix(-1 0 0 1 15 5.5)\"/></g>"}, "center-vertical": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.75 20.25h16.5M3.75 3.75h16.5\"/><rect width=\"6\" height=\"13\" rx=\"2\" transform=\"matrix(0 -1 -1 0 18.5 15)\"/></g>"}, "chat": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 21.25a9.25 9.25 0 1 0-8.307-5.177c.108.22.144.468.089.706l-.816 3.536a.6.6 0 0 0 .72.72l3.535-.817a1.06 1.06 0 0 1 .706.09A9.2 9.2 0 0 0 12 21.25M7.97 9.886h8.06m-8.06 4.228h5.748\"/>"}, "chat-question": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 21.25a9.25 9.25 0 1 0-8.307-5.177c.108.22.144.468.089.706l-.816 3.536a.6.6 0 0 0 .72.72l3.535-.817a1.06 1.06 0 0 1 .706.09A9.2 9.2 0 0 0 12 21.25\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.301 9.709a2.699 2.699 0 1 1 4.85 1.63a4 4 0 0 1-.32.317c-.092.078-.137.11-.227.171l-.979.675a1.81 1.81 0 0 0-.784 1.493\"/><circle cx=\"11.828\" cy=\"16.74\" r=\"1\" fill=\"currentColor\"/></g>"}, "checkbox-checked": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"4\"/><path d=\"m16.512 9.107l-5.787 5.786l-3.237-3.232\"/></g>"}, "checkbox-indeterminate": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"4\"/><path d=\"M16.19 12H7.81\"/></g>"}, "checkbox-indeterminate-2": {"body": "<g fill=\"none\"><rect width=\"18.5\" height=\"18.5\" x=\"2.75\" y=\"2.75\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"4\"/><path fill=\"currentColor\" d=\"M7.113 6.25a.86.86 0 0 0-.863.862v9.775c0 .477.386.863.862.863h9.775a.863.863 0 0 0 .863-.863V7.114a.863.863 0 0 0-.863-.863z\"/></g>"}, "checkbox-list": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M10.25 4.5h11\"/><rect width=\"3.5\" height=\"3.5\" x=\"2.75\" y=\"2.75\" rx=\"1.2\"/><path d=\"M10.25 12h11\"/><rect width=\"3.5\" height=\"3.5\" x=\"2.75\" y=\"10.25\" rx=\"1.2\"/><path d=\"M10.25 19.5h11\"/><rect width=\"3.5\" height=\"3.5\" x=\"2.75\" y=\"17.75\" rx=\"1.2\"/></g>"}, "checkbox-unchecked": {"body": "<rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"4\"/>"}, "checkmark": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m19.75 7.018l-9.257 9.257a1 1 0 0 1-1.414 0L4.25 11.446\"/>"}, "checkmark-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path stroke-linejoin=\"round\" d=\"m16.375 9.194l-5.611 5.612l-3.139-3.134\"/></g>"}, "checkmark-starburst": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M7.072 4.069a2.17 2.17 0 0 1 2.804-1.162l1.315.529c.52.208 1.099.208 1.618 0l1.315-.529a2.17 2.17 0 0 1 2.804 1.162l.556 1.303c.22.515.63.925 1.144 1.144l1.303.556a2.17 2.17 0 0 1 1.162 2.804l-.529 1.315a2.17 2.17 0 0 0 0 1.618l.529 1.315a2.17 2.17 0 0 1-1.162 2.804l-1.303.556a2.17 2.17 0 0 0-1.144 1.144l-.556 1.303a2.17 2.17 0 0 1-2.804 1.162l-1.315-.529a2.17 2.17 0 0 0-1.618 0l-1.315.529a2.17 2.17 0 0 1-2.804-1.162l-.556-1.303a2.17 2.17 0 0 0-1.144-1.144l-1.303-.556a2.17 2.17 0 0 1-1.162-2.804l.529-1.315a2.17 2.17 0 0 0 0-1.618l-.529-1.315A2.17 2.17 0 0 1 4.07 7.072l1.303-.556a2.17 2.17 0 0 0 1.144-1.144z\"/><path d=\"m15.899 9.5l-5 5l-2.797-2.793\"/></g>"}, "chevron-down": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m4.75 9.75l6.19 6.19a1.5 1.5 0 0 0 2.12 0l6.19-6.19\"/>"}, "chevron-left": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m14.25 4.75l-6.19 6.19a1.5 1.5 0 0 0 0 2.12l6.19 6.19\"/>"}, "chevron-right": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m9.75 4.75l6.19 6.19a1.5 1.5 0 0 1 0 2.12l-6.19 6.19\"/>"}, "chevron-up": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m4.75 14.25l6.19-6.19a1.5 1.5 0 0 1 2.12 0l6.19 6.19\"/>"}, "chrome-restore": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 9.75a3.5 3.5 0 0 1 3.5-3.5h8a3.5 3.5 0 0 1 3.5 3.5v8a3.5 3.5 0 0 1-3.5 3.5h-8a3.5 3.5 0 0 1-3.5-3.5z\"/><path d=\"M7.25 2.75h7.241v0a6.76 6.76 0 0 1 6.759 6.759v7.241\"/></g>"}, "chrome-restore-var": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 9.75a4 4 0 0 1 4-4h7.5a4 4 0 0 1 4 4v7.5a4 4 0 0 1-4 4h-7.5a4 4 0 0 1-4-4z\"/><path d=\"M6.75 2.75h7.5a7 7 0 0 1 7 7v7.5\"/></g>", "hidden": true}, "circle": {"body": "<circle cx=\"12\" cy=\"12\" r=\"9.25\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/>"}, "circle-small": {"body": "<circle cx=\"12\" cy=\"12\" r=\"3.25\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/>"}, "clipboard": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"18.5\" x=\"3.75\" y=\"2.75\" rx=\"3.5\"/><path d=\"M8.25 2.75h7.5v2.5a2 2 0 0 1-2 2h-3.5a2 2 0 0 1-2-2z\"/></g>"}, "clipboard-paste": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" d=\"M9.75 21.25h-3a3.5 3.5 0 0 1-3.5-3.5V6.25a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5v2\"/><path d=\"M7.75 2.75h7.5v2.5a2 2 0 0 1-2 2h-3.5a2 2 0 0 1-2-2z\"/><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.25 13.25a2.5 2.5 0 0 1 2.5-2.5h3.5a2.5 2.5 0 0 1 2.5 2.5v5.5a2.5 2.5 0 0 1-2.5 2.5h-3.5a2.5 2.5 0 0 1-2.5-2.5z\"/></g>"}, "clipboard-search": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" d=\"M14.25 21.25h3a3.5 3.5 0 0 0 3.5-3.5V6.25a3.5 3.5 0 0 0-3.5-3.5h-9.5a3.5 3.5 0 0 0-3.5 3.5v4\"/><path d=\"M8.75 2.75h7.5v2.5a2 2 0 0 1-2 2h-3.5a2 2 0 0 1-2-2z\"/><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.17 19.67a4.054 4.054 0 1 0-5.733-5.733A4.054 4.054 0 0 0 9.17 19.67m0 0l2.58 2.58\"/></g>"}, "clock": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M21.25 12a9.25 9.25 0 1 1-18.5 0a9.25 9.25 0 0 1 18.5 0\"/><path d=\"M11.25 6.75v6h4\"/></g>"}, "closed-captions": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"14.5\" x=\"2.75\" y=\"4.75\" rx=\"4\"/><path d=\"M10.5 14.382a2.75 2.75 0 1 1 0-4.764m7.125 4.764a2.75 2.75 0 1 1 0-4.764\"/></g>"}, "cloud": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18.268 11c0 .3.21.563.497.65a3.502 3.502 0 0 1-1.015 6.85H7.375q-.09 0-.178-.005q-.098.005-.197.005A4.25 4.25 0 0 1 7 10a.445.445 0 0 0 .431-.334A5.5 5.5 0 0 1 18.268 11\"/>"}, "cloud-add": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.33 18.5H7.375q-.09 0-.178-.005q-.098.005-.197.005A4.25 4.25 0 0 1 7 10a.445.445 0 0 0 .431-.334a5.502 5.502 0 0 1 10.793.634\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.494a.5.5 0 0 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "cloud-arrow-down": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.33 18.5H7.375q-.09 0-.178-.005q-.098.005-.197.005A4.25 4.25 0 0 1 7 10a.445.445 0 0 0 .431-.334a5.502 5.502 0 0 1 10.793.634\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m.5-8.455a.5.5 0 0 0-1 0v4.703l-1.564-1.564a.5.5 0 0 0-.707.707l2.417 2.418a.5.5 0 0 0 .708 0l2.417-2.418a.5.5 0 0 0-.707-.707L18 19.248z\" clip-rule=\"evenodd\"/></g>"}, "cloud-arrow-up": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.33 18.5H7.375q-.09 0-.178-.005q-.098.005-.197.005A4.25 4.25 0 0 1 7 10a.445.445 0 0 0 .431-.334a5.502 5.502 0 0 1 10.793.634\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 12a5.5 5.5 0 1 1 0 11a5.5 5.5 0 0 1 0-11m.5 8.455a.5.5 0 0 1-1 0v-4.703l-1.564 1.564a.5.5 0 0 1-.707-.707l2.417-2.418a.5.5 0 0 1 .708 0l2.417 2.418a.5.5 0 0 1-.707.707L18 15.752z\" clip-rule=\"evenodd\"/></g>"}, "cloud-off": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path d=\"m2.75 21.25l18.5-18.5\"/><path stroke-linejoin=\"round\" d=\"M16.772 7.229A5.48 5.48 0 0 1 18.268 11c0 .3.21.563.497.65a3.502 3.502 0 0 1-1.015 6.85H7.375q-.09 0-.178-.005a4.3 4.3 0 0 1-1.495-.197m-2.387-1.93A4.25 4.25 0 0 1 7 10a.445.445 0 0 0 .431-.333A5.5 5.5 0 0 1 14 5.638\"/></g>"}, "code": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.75 6.5L3.25 12l5.5 5.5m6.5-11l5.5 5.5l-5.5 5.5\"/>"}, "code-square": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M10.143 8.857L7 12l3.143 3.143m3.714-6.286L17 12l-3.143 3.143\"/><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"4\"/></g>"}, "coffee-hot": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.5 10.682c0-.875.71-1.585 1.585-1.585H16.52c.876 0 1.585.71 1.585 1.585v3.265a7.303 7.303 0 0 1-7.302 7.303v0A7.303 7.303 0 0 1 3.5 13.947z\"/><path d=\"M18.105 10.556h1.464A2.43 2.43 0 0 1 22 12.986v0a2.43 2.43 0 0 1-2.43 2.43h-1.465M6.421 3.75v2.43m4.382-2.43v2.43m4.381-2.43v2.43\"/></g>"}, "color-palette": {"body": "<g fill=\"none\"><circle cx=\"1.25\" cy=\"1.25\" r=\"1.25\" fill=\"currentColor\" transform=\"matrix(-1 0 0 1 16.654 6.034)\"/><circle cx=\"1.25\" cy=\"1.25\" r=\"1.25\" fill=\"currentColor\" transform=\"matrix(-1 0 0 1 12.156 5.221)\"/><circle cx=\"1.25\" cy=\"1.25\" r=\"1.25\" fill=\"currentColor\" transform=\"matrix(-1 0 0 1 8.654 7.94)\"/><circle cx=\"1.25\" cy=\"1.25\" r=\"1.25\" fill=\"currentColor\" transform=\"matrix(-1 0 0 1 7.685 12.156)\"/><circle cx=\"1.25\" cy=\"1.25\" r=\"1.25\" fill=\"currentColor\" transform=\"matrix(-1 0 0 1 9.904 15.948)\"/><path stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.25 12A9.25 9.25 0 1 0 12 21.25c1.318 0 2.224-1.28 2.329-2.594l.117-1.473a3 3 0 0 1 2.758-2.752l1.651-.129c1.28-.1 2.395-1.019 2.395-2.302Z\"/></g>"}, "comment": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.09 2.75a4 4 0 0 0-4 4v6.208a4 4 0 0 0 4 4h.093v3.792a.5.5 0 0 0 .839.368l4.52-4.16h4.369a4 4 0 0 0 4-4V6.75a4 4 0 0 0-4-4z\"/>"}, "comment-add": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.91 12.958a4 4 0 0 1-4 4h-4.368l-4.52 4.16a.5.5 0 0 1-.839-.368v-3.792H7.09a4 4 0 0 1-4-4V6.749a4 4 0 0 1 4-4h4.161\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 12a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V6h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 0 1-1 0V7h-2.493a.5.5 0 1 1 0-1H17V3.507a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "comment-add-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.09 2.75a4 4 0 0 0-4 4v6.208a4 4 0 0 0 4 4h.093v3.792a.5.5 0 0 0 .839.368l4.52-4.16h4.369a4 4 0 0 0 4-4V6.75a4 4 0 0 0-4-4zM12 6.854v6m-3-3h6\"/>"}, "comment-exclamation": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.09 2.75a4 4 0 0 0-4 4v6.208a4 4 0 0 0 4 4h.093v3.792a.5.5 0 0 0 .839.368l4.52-4.16h4.369a4 4 0 0 0 4-4V6.75a4 4 0 0 0-4-4z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 6.25v4\"/><circle cx=\"12\" cy=\"13.25\" r=\"1\" fill=\"currentColor\"/></g>"}, "comment-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M5.985 5.76a3 3 0 0 0-3 3v5.982a3 3 0 0 0 3 3h.446v3.017a.5.5 0 0 0 .839.367l3.67-3.385h4.045a3 3 0 0 0 3-3V8.76a3 3 0 0 0-3-3z\"/><path d=\"M6.985 2.76h8a6 6 0 0 1 6 6v4.982\"/></g>"}, "compare-size": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18.75 8.75a2.5 2.5 0 0 1 2.5 2.5v6.5a2.5 2.5 0 0 1-2.5 2.5H5.25a2.5 2.5 0 0 1-2.5-2.5v-6.5a2.5 2.5 0 0 1 2.5-2.5zm-2.5 7.5v2.5m0-8v2.5m-8-9.5h2.5m3 0h.5a2 2 0 0 1 2 2v.5m-11-2.5h-.5a2 2 0 0 0-2 2v.5\"/>"}, "compass": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path d=\"M10.195 10.195q.221-.22.475-.404c.382-.275.835-.456 1.74-.818l2.357-.943c.632-.252.947-.379 1.148-.313c.**************.368.368c.066.2-.06.517-.313 1.148l-.943 2.357c-.362.905-.543 1.358-.818 1.74q-.183.255-.404.475m-3.61-3.61a4 4 0 0 0-.404.475c-.275.382-.456.835-.818 1.74l-.943 2.357c-.252.632-.379.947-.313 1.148c.058.174.194.31.368.368c.2.066.516-.06 1.148-.313l2.357-.943c.905-.362 1.358-.543 1.74-.818q.255-.183.475-.404m-3.61-3.61l3.61 3.61\"/></g>"}, "component": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"5.671\" height=\"5.671\" x=\"7.99\" y=\"17.86\" rx=\"1.5\" transform=\"rotate(-45 7.99 17.86)\"/><rect width=\"5.671\" height=\"5.671\" x=\"13.851\" y=\"12\" rx=\"1.5\" transform=\"rotate(-45 13.85 12)\"/><rect width=\"5.671\" height=\"5.671\" x=\"2.13\" y=\"12\" rx=\"1.5\" transform=\"rotate(-45 2.13 12)\"/><rect width=\"5.671\" height=\"5.671\" x=\"7.99\" y=\"6.14\" rx=\"1.5\" transform=\"rotate(-45 7.99 6.14)\"/></g>"}, "compose": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M10.371 4.25H8.25a5 5 0 0 0-5 5v6.5a5 5 0 0 0 5 5h6.5a5 5 0 0 0 5-5v-2.121\"/><path d=\"M12.299 14.75a1.86 1.86 0 0 0 1.316-.545l6.59-6.59a1.86 1.86 0 0 0 0-2.633l-1.187-1.187a1.86 1.86 0 0 0-2.633 0l-6.59 6.59a1.86 1.86 0 0 0-.545 1.316v3.049z\"/></g>"}, "computer": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"15.031\" x=\"2.75\" y=\"2.75\" rx=\"3.5\"/><path d=\"M9.11 17.781v3.469m5.78-3.469v3.469m-8.382 0h10.984\"/></g>"}, "computer-mac": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 6.25a3.5 3.5 0 0 1 3.5-3.5h11.5a3.5 3.5 0 0 1 3.5 3.5v8.031a3.5 3.5 0 0 1-3.5 3.5H6.25a3.5 3.5 0 0 1-3.5-3.5zm0 7.75h18.5M9.11 17.781v1.469a2 2 0 0 1-2 2h-.6m8.38-3.469v1.469a2 2 0 0 0 2 2h.6m-10.982 0h10.984\"/>"}, "cone": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.035 17.883c.716-.8 2.908-2.633 7.965-2.633s7.25 1.833 7.965 2.633m-15.93 0c-.043.43.084.852.424 1.15c1.01.89 3.239 2.217 7.541 2.217s6.531-1.328 7.541-2.216c.34-.3.467-.722.424-1.15m-15.93 0c.02-.201.077-.404.167-.595l6.44-13.678c.542-1.148 2.175-1.148 2.715 0l6.44 13.678c.09.191.148.394.168.594\"/>"}, "contract-down": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 20.25h4.25a4 4 0 0 0 4-4v-2.5M12 20.25H7.75a4 4 0 0 1-4-4V12M12 20.25V15a3 3 0 0 0-3-3H3.75m0 0V7.75a4 4 0 0 1 4-4h2.5m10 6.5h-5.5a1 1 0 0 1-.707-.293M13.75 3.75v5.5c0 .276.112.526.293.707M20.25 3.75l-5.5 5.5l-.707.707\"/>"}, "cookies": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M17.076 10.535a2.75 2.75 0 0 0 3.89 0v0c.127-.128.362-.076.393.102a9.25 9.25 0 0 1-15.65 8.154a9.25 9.25 0 0 1 8.154-15.65c.178.031.23.266.102.394v0a2.75 2.75 0 0 0 2.333 4.667a2.75 2.75 0 0 0 .778 2.333\"/><circle cx=\"8.5\" cy=\"15.5\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"7.5\" cy=\"9.5\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"12.5\" cy=\"12.5\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"15.5\" cy=\"16.5\" r=\"1.25\" fill=\"currentColor\"/></g>"}, "copy": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M16.75 5.75a3 3 0 0 0-3-3h-6.5a3 3 0 0 0-3 3v9.5a3 3 0 0 0 3 3h6.5a3 3 0 0 0 3-3z\"/><path d=\"M19.75 6.75v8.5a6 6 0 0 1-6 6h-5.5\"/></g>"}, "copy-var": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M16.869 5.75a3 3 0 0 0-3-3H7.211a3 3 0 0 0-3 3v9.579a3 3 0 0 0 3 3h6.658a3 3 0 0 0 3-3z\"/><path d=\"M19.79 6.67v8.579a6 6 0 0 1-6 6H8.132\"/></g>", "hidden": true}, "copyleft": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path d=\"M9.172 14.828a4 4 0 1 0 0-5.656\"/></g>"}, "copyright": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path d=\"M14.828 14.828a4 4 0 1 1 0-5.656\"/></g>"}, "corner-radius": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.25 4.75h-7.5a7 7 0 0 0-7 7v7.5\"/>"}, "credit-card": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"14\" x=\"2.75\" y=\"5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" rx=\"3\"/><path d=\"M2.75 9.5h18.5\"/><path stroke-linecap=\"round\" d=\"M14.75 14.25h3\"/></g>"}, "crop": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.25 17.653H9.347a3 3 0 0 1-3-3V2.75M2.75 6.347h3.597m11.306 11.306v3.597M8.917 6.347h5.736a3 3 0 0 1 3 3v5.736\"/>"}, "css": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"m20.754 4.792l-2.84 14.31a1.5 1.5 0 0 1-1.099 1.161l-4.069 1.045a3 3 0 0 1-1.492 0l-4.07-1.045a1.5 1.5 0 0 1-1.097-1.16L3.246 4.792A1.5 1.5 0 0 1 4.717 3h14.566a1.5 1.5 0 0 1 1.471 1.792\"/><path d=\"m15.998 11.891l-.891 4.458a1 1 0 0 1-.64.744l-2.126.77a1 1 0 0 1-.681 0l-2.146-.777a.99.99 0 0 1-.622-.74l-.222-1.114m7.328-3.341l.859-4.292a.6.6 0 0 0-.589-.718H7m8.998 5.01H8.002\"/></g>"}, "css-2": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.75 5.25a1.5 1.5 0 0 1 1.5-1.5h11.5a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5h-9.5a3.5 3.5 0 0 1-3.5-3.5z\"/><path d=\"M9.25 17.25h-1a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h1m4 0h-1a1 1 0 0 0-1 1v.5a1 1 0 0 0 1 1v0a1 1 0 0 1 1 1v.5a1 1 0 0 1-1 1h-1m6-5h-1a1 1 0 0 0-1 1v.5a1 1 0 0 0 1 1v0a1 1 0 0 1 1 1v.5a1 1 0 0 1-1 1h-1\"/></g>"}, "cube": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m12 12l8.073-4.625M12 12L3.926 7.375M12 12v9.25m8.073-13.875a3.17 3.17 0 0 0-1.165-1.156l-5.316-3.046a3.2 3.2 0 0 0-3.184 0L5.092 6.22c-.493.282-.89.684-1.166 1.156m16.147 0c.275.472.427 1.015.427 1.58v6.09a3.15 3.15 0 0 1-1.592 2.736l-5.316 3.046A3.2 3.2 0 0 1 12 21.25M3.926 7.375a3.14 3.14 0 0 0-.426 1.58v6.09c0 1.13.607 2.172 1.592 2.736l5.316 3.046A3.2 3.2 0 0 0 12 21.25\"/>"}, "cursor": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.084 20.276c-1.06 1.38-3.264.66-3.306-1.079L4.443 5.392C4.407 3.932 6 3.012 7.247 3.773l11.788 7.192c1.485.906 1.006 3.176-.719 3.403l-5.581.738a1.84 1.84 0 0 0-1.221.705z\"/>"}, "cursor-click": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12.394 20.734c-.757.985-2.332.471-2.362-.771l-.239-9.86a1.317 1.317 0 0 1 2.003-1.157l8.42 5.137c1.06.647.718 2.268-.513 2.431l-3.987.527c-.346.046-.66.227-.872.503z\"/><path stroke-linecap=\"round\" d=\"M3.797 8.75h2.5m3.75-3.502v-2.5M6.815 5.765L5.047 3.998m8.232 1.767l1.768-1.767\"/></g>"}, "cursor-drag": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.315 11.055v4.176m3.246-6.593V6.555c0-.892-.727-1.615-1.623-1.615s-1.623.723-1.623 1.615v3.747m3.246-.566V4.365a1.62 1.62 0 0 1 1.623-1.615c.897 0 1.623.723 1.623 1.615V9.73m0 .005v-3.18a1.624 1.624 0 0 1 3.246 0v2.19m0 0v.99m0-.99a1.62 1.62 0 0 1 1.624-1.615A1.62 1.62 0 0 1 20.3 8.745v5.935a6.57 6.57 0 0 1-6.57 6.57h-2.95a7.08 7.08 0 0 1-7.069-7.492l.015-.256a3.06 3.06 0 0 1 2.13-2.738l1.458-.462m0 0V11.4\"/>"}, "cut": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.651 14.43a3.75 3.75 0 1 0-4.302 6.143a3.75 3.75 0 0 0 4.302-6.144m0 0l3.35-4.446m5.45-7.235l-3.82 5.069m1.718 6.611a3.75 3.75 0 1 1 4.302 6.144a3.75 3.75 0 0 1-4.302-6.144m0 0L12 9.984M6.55 2.749L12 9.984\"/>"}, "cylinder": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.25 6.347c0 1.987-3.246 3.598-7.25 3.598s-7.25-1.61-7.25-3.598m14.5 0c0-1.986-3.246-3.597-7.25-3.597S4.75 4.36 4.75 6.347m14.5 0v11.306c0 1.986-3.246 3.597-7.25 3.597s-7.25-1.61-7.25-3.597V6.347\"/>"}, "dark-theme": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M2.75 12A9.25 9.25 0 0 0 12 21.25V2.75A9.25 9.25 0 0 0 2.75 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 21.25a9.25 9.25 0 0 0 0-18.5m0 18.5a9.25 9.25 0 0 1 0-18.5m0 18.5V2.75\"/></g>"}, "database": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.25 6.25c0 1.933-3.246 3.5-7.25 3.5s-7.25-1.567-7.25-3.5m14.5 0c0-1.933-3.246-3.5-7.25-3.5s-7.25 1.567-7.25 3.5m14.5 0V12M4.75 6.25V12m0 0v5.75c0 1.933 3.246 3.5 7.25 3.5s7.25-1.567 7.25-3.5V12m-14.5 0c0 1.933 3.246 3.5 7.25 3.5s7.25-1.567 7.25-3.5\"/>"}, "database-add": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.25 6.25c0 1.933-3.246 3.5-7.25 3.5s-7.25-1.567-7.25-3.5m14.5 0c0-1.933-3.246-3.5-7.25-3.5s-7.25 1.567-7.25 3.5m14.5 0v3.53M4.75 6.25V12m0 0v5.75c0 1.756 2.678 3.21 6.17 3.461M4.75 12c0 1.577 2.16 2.91 5.13 3.348\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 1 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "delete": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.687 6.213L6.8 18.976a2.5 2.5 0 0 0 2.466 2.092h3.348m6.698-14.855L17.2 18.976a2.5 2.5 0 0 1-2.466 2.092h-3.348m-1.364-9.952v5.049m3.956-5.049v5.049M2.75 6.213h18.5m-6.473 0v-1.78a1.5 1.5 0 0 0-1.5-1.5h-2.554a1.5 1.5 0 0 0-1.5 1.5v1.78z\"/>"}, "diamond": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.69 9.915h8.62m-8.62 0H2.75m4.94 0l3.65 10.051M7.69 9.915l2.4-3.933l.55-1.012m5.67 4.945h4.94m-4.94 0l-3.65 10.051m3.65-10.051l-2.4-3.933l-.566-1.012M2.75 9.915c0 .************* 1.236c.212.535.642 1.028 1.5 2.013l3.467 3.976c1.397 1.602 2.095 2.404 2.923 2.698q.229.081.464.128M2.75 9.915c0-.42.079-.84.236-1.237c.212-.535.642-1.027 1.5-2.013c.467-.534.7-.801.97-1.008a3.36 3.36 0 0 1 1.361-.619c.334-.068.688-.068 1.397-.068h2.427M21.25 9.915c0 .42-.079.84-.236 1.236c-.212.535-.642 1.028-1.5 2.013l-3.467 3.976c-1.397 1.602-2.095 2.404-2.923 2.698a3.4 3.4 0 0 1-.464.128m8.59-10.051c0-.42-.079-.84-.236-1.237c-.212-.535-.642-1.027-1.5-2.013c-.467-.534-.7-.801-.97-1.008a3.36 3.36 0 0 0-1.361-.619c-.334-.068-.688-.068-1.397-.068h-2.442m-.684 14.996a3.4 3.4 0 0 1-1.32 0M13.344 4.97H10.64\"/>"}, "diff": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5 10.25h7m0 0h7m-7 0v-7m0 7v7m-7 3.5h14\"/>"}, "directions": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.507 14.007a2.84 2.84 0 0 1 0-4.014l6.486-6.486a2.84 2.84 0 0 1 4.014 0l6.486 6.486a2.84 2.84 0 0 1 0 4.014l-6.486 6.486a2.84 2.84 0 0 1-4.014 0z\"/><path d=\"m14.46 9.02l1.394 1.395a1 1 0 0 1 .293.707m-1.688 2.102l1.395-1.395a1 1 0 0 0 .293-.707m-7.46 4.031v-2.53a1.5 1.5 0 0 1 1.5-1.5h5.96\"/></g>"}, "do-not-disturb": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path stroke-linecap=\"round\" d=\"M7.5 12h9\"/></g>"}, "document": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"15\" height=\"18.5\" x=\"4.5\" y=\"2.75\" rx=\"3.5\"/><path d=\"M8.5 6.755h7m-7 4h7m-7 4H12\"/></g>"}, "dollar": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M16.441 8.26S15.884 5.621 12 5.334m-4.376 10.52S8.75 18.518 12 18.684M12 2.75v2.584m0 15.916v-2.567m0-13.35a10 10 0 0 0-.704-.024c-1.688 0-3.881 1.405-3.881 3.367c0 1.963 1.589 2.732 4.388 3.21s4.782 1.531 4.782 3.696s-2.32 3.11-4.266 3.11a6 6 0 0 1-.319-.009\"/>"}, "dollar-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path d=\"M15.099 9.723s-.377-1.783-3.001-1.977m-2.957 7.108s.761 1.8 2.957 1.912m0-10.766v1.746m0 10.754v-1.734m0-9.02a7 7 0 0 0-.476-.017c-1.14 0-2.622.95-2.622 2.275c0 1.326 1.073 1.846 2.965 2.169s3.23 1.035 3.23 2.497s-1.566 2.101-2.881 2.101a4 4 0 0 1-.216-.005\"/><circle cx=\"12.25\" cy=\"12.25\" r=\"9.25\" stroke-linejoin=\"round\"/></g>"}, "door": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5.25 6.25a2.5 2.5 0 0 1 2.5-2.5h8.5a2.5 2.5 0 0 1 2.5 2.5v15H5.25zm-2.5 15h18.5\"/><circle cx=\"15.25\" cy=\"12.5\" r=\"1.5\" fill=\"currentColor\"/></g>"}, "door-open": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M16.157 20.517h.593a2.5 2.5 0 0 0 2.5-2.5V5.75a2.5 2.5 0 0 0-2.5-2.5h-9.5c-.55 0-1.06.178-1.473.48m-1.02 13.742a1.5 1.5 0 0 0 .86 1.357l5.896 2.775a1.5 1.5 0 0 0 2.138-1.358V8.387a1.5 1.5 0 0 0-.86-1.356l-7.014-3.3m0 0A2.5 2.5 0 0 0 4.75 5.75v11.722\"/><circle cx=\"10.25\" cy=\"12.75\" r=\"1.25\" fill=\"currentColor\"/></g>"}, "dot-circle": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/><circle cx=\"12\" cy=\"12\" r=\"2.5\" fill=\"currentColor\"/></g>"}, "dot-square": {"body": "<g fill=\"none\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"4\"/><circle cx=\"12\" cy=\"12\" r=\"2.5\" fill=\"currentColor\"/></g>"}, "draw-text": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m5.719 16.25l1.92-4.404m0 0h6.91m-6.91 0l2.94-6.747a.553.553 0 0 1 1.029 0l2.941 6.747m0 0l.337.774\"/><path fill=\"currentColor\" d=\"M15.586 20.936a2.5 2.5 0 0 0 1.219-.673l5.454-5.45a2.526 2.526 0 1 0-3.57-3.573l-5.453 5.452c-.335.336-.569.76-.674 1.222l-.536 2.354a1.007 1.007 0 0 0 1.206 1.206z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m3.75 19.687l.568.234c.638.263 1.364.175 1.956-.18c.69-.411 1.649-.915 2.483-1.1c.583-.13 1.243.199 1.091.776c-.17.642-.69 1.396-.192 1.745c.75.525 5.031-.818 5.031-.818\"/></g>"}, "drop": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" d=\"M12 21c-1.804 0-3.246-.566-4.397-1.446C2.38 15.557 5.832 8.09 10.801 3.522a1.767 1.767 0 0 1 2.398 0c4.97 4.568 8.42 12.035 3.198 16.032C15.246 20.434 13.804 21 12 21Z\"/>"}, "emoji": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/><circle cx=\"9\" cy=\"9.5\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"15\" cy=\"9.5\" r=\"1.25\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.464 14.25a4 4 0 0 1-6.928 0\"/></g>"}, "emoji-frown": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/><circle cx=\"9\" cy=\"9.5\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"15\" cy=\"9.5\" r=\"1.25\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.464 15.813a4 4 0 0 0-6.928 0\"/></g>"}, "emoji-grin": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/><circle cx=\"9\" cy=\"9\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"15\" cy=\"9\" r=\"1.25\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M16.5 12.75c.276 0 .503.224.475.5a4.999 4.999 0 0 1-9.594 1.413a5 5 0 0 1-.356-1.414c-.028-.275.199-.499.475-.499z\"/></g>"}, "emoji-laughter": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path d=\"M16.5 12.75c.276 0 .503.224.475.5a4.999 4.999 0 0 1-9.594 1.413a5 5 0 0 1-.356-1.414c-.028-.275.199-.499.475-.499zM7.264 9.082a1.797 1.797 0 0 1 3.472 0m2.528 0a1.796 1.796 0 0 1 3.472 0\"/></g>"}, "eraser": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.788 20.5h9.02m-9.02 0a3.47 3.47 0 0 0 2.486-1.02l1.29-1.29M9.788 20.5a3.47 3.47 0 0 1-2.438-1.02l-3.33-3.33a3.48 3.48 0 0 1 0-4.923l1.29-1.29m0 0l5.417-5.417a3.48 3.48 0 0 1 4.923 0l3.33 3.33a3.48 3.48 0 0 1 0 4.924l-5.417 5.416M5.31 9.936l.367.368l7.585 7.585l.301.301\"/>"}, "eraser-sparkle": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.788 20.5h9.02m-9.02 0a3.47 3.47 0 0 0 2.486-1.02l1.29-1.29M9.788 20.5a3.47 3.47 0 0 1-2.438-1.02l-3.33-3.33a3.47 3.47 0 0 1-1.003-2.802M6.6 11.226l6.662 6.663l.301.301m0 0l5.417-5.417a3.48 3.48 0 0 0 0-4.923l-3.33-3.33a3.47 3.47 0 0 0-2.643-1.015\"/><path fill=\"currentColor\" d=\"M8.6 1.419a.64.64 0 0 0-1.2 0l-.167.449a4 4 0 0 1-2.366 2.365l-.449.166a.64.64 0 0 0 0 1.202l.45.166a4 4 0 0 1 2.365 2.366l.166.449a.64.64 0 0 0 1.202 0l.166-.449a4 4 0 0 1 2.366-2.366l.449-.166a.64.64 0 0 0 0-1.202l-.45-.166a4 4 0 0 1-2.365-2.365zM3.876 7.262a.4.4 0 0 0-.752 0l-.103.28a2.5 2.5 0 0 1-1.479 1.479l-.28.104a.4.4 0 0 0 0 .75l.28.105a2.5 2.5 0 0 1 1.479 1.478l.103.28a.4.4 0 0 0 .752 0l.103-.28A2.5 2.5 0 0 1 5.458 9.98l.28-.104a.4.4 0 0 0 0-.751l-.28-.104a2.5 2.5 0 0 1-1.479-1.479z\"/></g>"}, "exclamation-mark": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"19.38\" r=\"1.25\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 4.13v11\"/></g>"}, "expand": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 20.25h4.25a4 4 0 0 0 4-4v-2.5M12 20.25H7.75a4 4 0 0 1-4-4V12M12 20.25V15a3 3 0 0 0-3-3H3.75m0 0V7.75a4 4 0 0 1 4-4h2.5m3.5 0h5.5c.276 0 .526.112.707.293m.293 6.207v-5.5a1 1 0 0 0-.293-.707M13.75 10.25l5.5-5.5l.707-.707\"/>"}, "extension": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M13 2.75A2.25 2.25 0 0 0 10.75 5c0 .14 0 .21-.012.267a.6.6 0 0 1-.471.471c-.058.012-.128.012-.267.012H8.75a2 2 0 0 0-2 2V9c0 .14 0 .21-.012.267a.6.6 0 0 1-.471.471c-.058.012-.128.012-.267.012a2.25 2.25 0 0 0 0 4.5c.14 0 .209 0 .267.011a.6.6 0 0 1 .471.472c.012.058.012.128.012.267v1.25a2 2 0 0 0 2 2H10c.14 0 .21 0 .267.012a.6.6 0 0 1 .471.47c.012.059.012.129.012.268a2.25 2.25 0 1 0 4.5 0c0-.14 0-.21.011-.267a.6.6 0 0 1 .472-.471c.058-.012.128-.012.267-.012h1.25a2 2 0 0 0 2-2V15c0-.14 0-.21-.012-.267a.6.6 0 0 0-.471-.472c-.058-.011-.128-.011-.267-.011a2.25 2.25 0 0 1 0-4.5c.14 0 .209 0 .267-.012a.6.6 0 0 0 .471-.471c.012-.058.012-.128.012-.267V7.75a2 2 0 0 0-2-2H16c-.14 0-.21 0-.267-.012a.6.6 0 0 1-.472-.471C15.25 5.21 15.25 5.14 15.25 5A2.25 2.25 0 0 0 13 2.75\"/>"}, "eye": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.182 12.808C4.233 14.613 7.195 18.81 12 18.81c4.813 0 7.77-4.199 8.82-6.002a1.6 1.6 0 0 0-.001-1.615C19.769 9.389 16.809 5.19 12 5.19s-7.768 4.197-8.818 6.001a1.6 1.6 0 0 0 0 1.617Z\"/><path d=\"M12 14.625a2.625 2.625 0 1 0 0-5.25a2.625 2.625 0 0 0 0 5.25Z\"/></g>"}, "eye-off": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path d=\"m2.75 21.25l18.5-18.5\"/><path fill=\"currentColor\" d=\"M14.625 12a2.625 2.625 0 0 1-4.481 1.856l3.712-3.712c.475.475.769 1.131.769 1.856Z\"/><path d=\"M6.924 17.076c1.364.993 3.057 1.734 5.076 1.734c4.813 0 7.771-4.199 8.82-6.002a1.6 1.6 0 0 0-.001-1.615c-.609-1.046-1.86-2.898-3.742-4.27m-2.81-1.409A8 8 0 0 0 12 5.19c-4.808 0-7.768 4.197-8.818 6.001a1.6 1.6 0 0 0 0 1.617c.326.56.836 1.35 1.528 2.173\"/></g>"}, "eyedropper": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.047 7.819L20.92 9.69m-1.872-1.87l-2.864-2.864m2.864 2.864l1.496-1.496a1 1 0 0 0 0-1.414l-1.45-1.45a1 1 0 0 0-1.414 0l-1.496 1.496m-1.872-1.872l1.872 1.872m-3.796 2.751a1.5 1.5 0 0 1 2.121 0l1.787 1.786a1.5 1.5 0 0 1 0 2.12l-8.562 8.563a1.5 1.5 0 0 1-.829.421l-2.12.332a1.5 1.5 0 0 1-1.714-1.715l.334-2.118a1.5 1.5 0 0 1 .42-.827z\"/>"}, "eyedropper-color": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.047 7.819L20.92 9.69m-1.872-1.87l-2.864-2.864m2.864 2.864l1.496-1.496a1 1 0 0 0 0-1.414l-1.45-1.45a1 1 0 0 0-1.414 0l-1.496 1.496m-1.872-1.872l1.872 1.872M6.235 13.86l-2.202 2.202c-.205.205-.308.307-.387.425q-.107.157-.171.335c-.048.134-.07.277-.116.563l-.096.608c-.173 1.1-.26 1.65-.087 2.05c.151.35.43.629.78.78c.399.174.949.087 2.05-.085l.61-.096c.286-.044.43-.067.563-.115q.18-.065.337-.17c.117-.08.22-.183.425-.389l7.718-7.718c.594-.594.891-.89 1.002-1.233a1.5 1.5 0 0 0 0-.927c-.11-.343-.408-.64-1.002-1.234l-.513-.513c-.594-.594-.891-.891-1.234-1.002a1.5 1.5 0 0 0-.927 0c-.342.11-.64.408-1.233 1.002zm0 0h7.815\"/>"}, "eyedropper-color-accent": {"body": "<path fill=\"currentColor\" d=\"m7.941 19.968l6.109-6.108H6.235l-2.202 2.202c-.205.205-.308.307-.387.425a1.5 1.5 0 0 0-.171.335c-.048.134-.07.277-.116.563l-.096.608c-.173 1.1-.26 1.65-.087 2.05c.151.35.43.629.78.78c.399.174.949.087 2.05-.085l.61-.096c.286-.044.43-.067.563-.115q.18-.065.337-.17c.117-.08.22-.183.425-.389\"/>"}, "facebook": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M10.478 21.125a9.3 9.3 0 0 0 3.037.002m-3.038-.002A9.25 9.25 0 0 1 2.75 12a9.25 9.25 0 1 1 10.765 9.127m-3.038-.002V16.12H8.58a.6.6 0 0 1-.6-.6v-1.838a.6.6 0 0 1 .6-.6h1.897V9.95a3 3 0 0 1 3-3h1.81a1 1 0 0 1 1 1v1.04a1 1 0 0 1-1 1h-.772a1 1 0 0 0-1 1v2.092h2.297a.6.6 0 0 1 .592.698l-.25 1.504a1 1 0 0 1-.986.836h-1.653v5.007\"/>"}, "fast-forward": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M5.996 18.323c-1.02.784-2.496.057-2.496-1.229V6.906c0-1.286 1.476-2.013 2.496-1.229l6.224 5.192a1.473 1.473 0 0 1 0 2.262z\"/><path d=\"M15.246 18.323c-1.02.784-2.496.057-2.496-1.229V6.906c0-1.286 1.476-2.013 2.496-1.229l6.224 5.192a1.473 1.473 0 0 1 0 2.262z\"/></g>"}, "figma": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M5.833 5.833A3.083 3.083 0 0 1 8.917 2.75H12v6.167H8.917a3.083 3.083 0 0 1-3.084-3.084m12.334 0a3.083 3.083 0 0 0-3.084-3.083H12v6.167h3.083a3.083 3.083 0 0 0 3.084-3.084\"/><rect width=\"6.167\" height=\"6.167\" rx=\"3.083\" transform=\"matrix(-1 0 0 1 18.167 8.917)\"/><path d=\"M5.833 12a3.083 3.083 0 0 1 3.084-3.083H12v6.166H8.917A3.083 3.083 0 0 1 5.833 12\"/><path d=\"M5.833 18.167a3.083 3.083 0 0 1 3.084-3.084H12v3.084a3.083 3.083 0 0 1-3.083 3.083v0a3.083 3.083 0 0 1-3.084-3.083\"/></g>"}, "file": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M11.688 3.063a3.5 3.5 0 0 1 1.027.712l5.968 5.97c.3.3.54.647.711 1.026m-7.706-7.708a3.5 3.5 0 0 0-1.448-.313H7.792a3.5 3.5 0 0 0-3.5 3.5v11.5a3.5 3.5 0 0 0 3.5 3.5h8.416a3.5 3.5 0 0 0 3.5-3.5v-5.53c0-.505-.109-.999-.314-1.45m-7.706-7.707V8.77a2 2 0 0 0 2 2h5.706\"/>"}, "file-add": {"body": "<g fill=\"none\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H7v2.493a.5.5 0 1 1-1 0V18H3.507a.5.5 0 0 1 0-1H6v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.292 10.25v-4a3.5 3.5 0 0 1 3.5-3.5h2.448a3.5 3.5 0 0 1 1.447.313M13.75 21.25h2.458a3.5 3.5 0 0 0 3.5-3.5v-5.53c0-.505-.109-.999-.314-1.45m-7.706-7.707a3.5 3.5 0 0 1 1.027.712l5.968 5.97c.3.3.54.647.711 1.026m-7.706-7.708V8.77a2 2 0 0 0 2 2h5.706\"/></g>"}, "file-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M13.237 3.013c.319.144.613.345.866.596l5.033 5.002c.252.25.454.542.6.859m-6.5-6.457a3 3 0 0 0-1.22-.263H9.5A2.5 2.5 0 0 0 7 5.25v10.5a2.5 2.5 0 0 0 2.5 2.5h8a2.5 2.5 0 0 0 2.5-2.5v-5.066c0-.423-.092-.836-.265-1.214m-6.498-6.457v4.781a1.68 1.68 0 0 0 1.686 1.676h4.812\"/><path d=\"M4 6.75v9a5.5 5.5 0 0 0 5.5 5.5H16\"/></g>"}, "file-sync": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.292 10.25v-4a3.5 3.5 0 0 1 3.5-3.5h2.448a3.5 3.5 0 0 1 1.447.313M13.75 21.25h2.458a3.5 3.5 0 0 0 3.5-3.5v-5.53c0-.505-.109-.999-.314-1.45m-7.706-7.707a3.5 3.5 0 0 1 1.027.712l5.968 5.97c.3.3.54.647.711 1.026m-7.706-7.708V8.77a2 2 0 0 0 2 2h5.706\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m1.548-8.64a3.5 3.5 0 0 0-4.929 2.234a.5.5 0 0 0 .966.259A2.5 2.5 0 0 1 8.3 15.765h-.565a.5.5 0 0 0 0 1H9.5a.5.5 0 0 0 .5-.5V14.5a.5.5 0 0 0-1 0v.55a3.5 3.5 0 0 0-.952-.69m1.833 4.046a.5.5 0 0 0-.966-.259A2.5 2.5 0 0 1 4.7 19.235h.565a.5.5 0 0 0 0-1H3.5a.5.5 0 0 0-.5.5V20.5a.5.5 0 0 0 1 0v-.55a3.5 3.5 0 0 0 5.88-1.544\" clip-rule=\"evenodd\"/></g>"}, "file-text": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path stroke-linejoin=\"round\" d=\"M11.688 3.063a3.5 3.5 0 0 1 1.027.712l5.968 5.97c.3.3.54.647.711 1.026m-7.706-7.708a3.5 3.5 0 0 0-1.448-.313H7.792a3.5 3.5 0 0 0-3.5 3.5v11.5a3.5 3.5 0 0 0 3.5 3.5h8.416a3.5 3.5 0 0 0 3.5-3.5v-5.53c0-.505-.109-.999-.314-1.45m-7.706-7.707V8.77a2 2 0 0 0 2 2h5.706\"/><path d=\"M7.29 13.77h9.42m-9.42 3.48h6.42\"/></g>"}, "filter": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.5 7.25h15M7.385 12h9.23m-6.345 4.75h3.46\"/>"}, "filter-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.145 17.704v-3.976c0-.381 0-.572-.044-.75a1.5 1.5 0 0 0-.192-.446c-.1-.155-.238-.286-.515-.548L3.578 7.43a2.648 2.648 0 0 1 1.82-4.572h13.189a2.664 2.664 0 0 1 1.852 4.579l-4.765 4.607c-.27.261-.405.392-.501.545a1.5 1.5 0 0 0-.187.441c-.044.176-.044.364-.044.74v3.935c0 .542 0 .813-.062 1.057a2 2 0 0 1-.641 1.027c-.192.163-.436.282-.923.52c-1.14.557-1.709.835-2.172.835a2 2 0 0 1-1.795-1.121c-.204-.416-.204-1.05-.204-2.318\"/>"}, "filter-cancel": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.5 7.25h5.75M7.385 12H12m-1.73 4.75h3.46\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 12a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m-2.352-7.852a.5.5 0 0 1 .707 0L17.5 5.793l1.645-1.645a.5.5 0 1 1 .707.707L18.207 6.5l1.645 1.645a.5.5 0 0 1-.707.707L17.5 7.207l-1.645 1.645a.5.5 0 0 1-.707-.707L16.793 6.5l-1.645-1.645a.5.5 0 0 1 0-.707\" clip-rule=\"evenodd\"/></g>"}, "filter-cancel-2": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m17.52 10.25l2.919-2.814a2.664 2.664 0 0 0-1.852-4.579H5.397a2.648 2.648 0 0 0-1.82 4.572l4.817 4.555c.277.262.415.393.515.548a1.5 1.5 0 0 1 .192.446c.044.178.044.369.044.75v3.976c0 1.268 0 1.902.204 2.318a2 2 0 0 0 .901.91\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m-2.352-7.852a.5.5 0 0 1 .707 0l1.645 1.645l1.645-1.645a.5.5 0 1 1 .707.707L18.207 17.5l1.645 1.645a.5.5 0 0 1-.707.707L17.5 18.207l-1.645 1.645a.5.5 0 1 1-.707-.707l1.645-1.645l-1.645-1.645a.5.5 0 0 1 0-.707\" clip-rule=\"evenodd\"/></g>"}, "flag": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5.25 21.25v-6m0 0v-10a1.5 1.5 0 0 1 1.5-1.5h11.086a1 1 0 0 1 .821 1.571L15.75 9.5l2.907 4.179a1 1 0 0 1-.82 1.571z\"/>"}, "flag-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5.25 15.182v6.07m0-6.07a7.5 7.5 0 0 1 7.25 0a7.5 7.5 0 0 0 6.936.164a.57.57 0 0 0 .314-.518V3.682a7.5 7.5 0 0 1-7.25 0a7.5 7.5 0 0 0-6.722-.265a.93.93 0 0 0-.528.855z\"/>"}, "flashlight": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M6.512 7.71h10.976m-8.73 3.78h6.477v8.11a1.9 1.9 0 0 1-1.9 1.9h-2.677a1.9 1.9 0 0 1-1.9-1.9z\"/><path d=\"m5.806 7.99l2.952 3.5h6.477l2.959-3.5a1 1 0 0 0 .236-.645V4.85A1.85 1.85 0 0 0 16.58 3H7.42a1.85 1.85 0 0 0-1.85 1.85v2.495a1 1 0 0 0 .236.645m6.191 7.039v1.766\"/></g>"}, "foldable-horizontal": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 5.25L5.223 3.601A2 2 0 0 0 2.75 5.545v12.91A2 2 0 0 0 5.223 20.4L12 18.75m0-13.5v13.5m0-13.5l6.777-1.649a2 2 0 0 1 2.473 1.944v12.91a2 2 0 0 1-2.473 1.944L12 18.75\"/>"}, "foldable-horizontal-half": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"m12 5.25l6.777-1.649a2 2 0 0 1 2.473 1.944v12.91a2 2 0 0 1-2.473 1.944L12 18.75z\"/><path stroke-dasharray=\"2.5 3\" d=\"m9.5 19.358l-4.895 1.19a1.5 1.5 0 0 1-1.855-1.457V4.91a1.5 1.5 0 0 1 1.855-1.46L9.5 4.641\"/></g>"}, "foldable-vertical": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m6.375 12l-1.39 6.853a2 2 0 0 0 1.961 2.397h10.108a2 2 0 0 0 1.96-2.397L17.625 12m-11.25 0h11.25m-11.25 0l-1.39-6.853A2 2 0 0 1 6.947 2.75h10.108a2 2 0 0 1 1.96 2.397L17.625 12\"/>"}, "foldable-vertical-half": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"m17.25 12l1.648 6.777a2 2 0 0 1-1.943 2.473h-9.91A2 2 0 0 1 5.1 18.777L6.75 12z\"/><path stroke-dasharray=\"2 3\" d=\"m6.142 9.5l-1.19-4.895A1.5 1.5 0 0 1 6.408 2.75H17.59a1.5 1.5 0 0 1 1.458 1.855L17.859 9.5\"/></g>"}, "folder": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 8.623v7.379a4 4 0 0 0 4 4h10.5a4 4 0 0 0 4-4v-5.69a4 4 0 0 0-4-4H12M2.75 8.624V6.998a3 3 0 0 1 3-3h2.9a2.5 2.5 0 0 1 1.768.732L12 6.313m-9.25 2.31h5.904a2.5 2.5 0 0 0 1.768-.732L12 6.313\"/>"}, "folder-add": {"body": "<g fill=\"none\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.494a.5.5 0 0 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 8.623v7.379a4 4 0 0 0 4 4h3.35M2.75 8.623V6.998a3 3 0 0 1 3-3h2.9a2.5 2.5 0 0 1 1.768.732L12 6.313m-9.25 2.31h5.904a2.5 2.5 0 0 0 1.768-.732L12 6.313m0 0l5.25-.002a4 4 0 0 1 4 4v.669\"/></g>"}, "folder-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 7.624v6.127a3.5 3.5 0 0 0 3.5 3.5h8.5a3.5 3.5 0 0 0 3.5-3.5V9.187a3.5 3.5 0 0 0-3.5-3.5l-4.25.001M2.75 7.624V5.749a2 2 0 0 1 2-2h2.775a2.5 2.5 0 0 1 1.768.732L10.5 5.688M2.75 7.624h4.779a2.5 2.5 0 0 0 1.767-.732L10.5 5.688\"/><path stroke-linecap=\"round\" d=\"M21.25 9.687v4.064a6.5 6.5 0 0 1-6.5 6.5h-8\"/></g>"}, "folder-open": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m3.882 18.043l4.041-5.623a4 4 0 0 1 3.249-1.665h8.752M3.882 18.043a3.65 3.65 0 0 0 2.777 1.277h8.343a4 4 0 0 0 3.405-1.9l2.918-4.734a1.287 1.287 0 0 0-1.115-1.931h-.286M3.882 18.043A3.65 3.65 0 0 1 3 15.661V7.424A2.744 2.744 0 0 1 5.744 4.68h2.653c.607 0 1.189.24 1.618.67l.911.91a1.83 1.83 0 0 0 1.294.537l4.044-.001a3.66 3.66 0 0 1 3.66 3.66v.299\"/>"}, "full-screen-maximize": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 8.345V6.25a2.5 2.5 0 0 1 2.5-2.5h2.095M3.75 15.655v2.095a2.5 2.5 0 0 0 2.5 2.5h2.095M20.25 8.345V6.25a2.5 2.5 0 0 0-2.5-2.5h-2.095m4.595 11.905v2.095a2.5 2.5 0 0 1-2.5 2.5h-2.095\"/>"}, "full-screen-minimize": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.345 3.75v2.095a2.5 2.5 0 0 1-2.5 2.5H3.75M8.345 20.25v-2.095a2.5 2.5 0 0 0-2.5-2.5H3.75M15.655 3.75v2.095a2.5 2.5 0 0 0 2.5 2.5h2.095M15.655 20.25v-2.095a2.5 2.5 0 0 1 2.5-2.5h2.095\"/>"}, "game": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M16.031 3.887H7.97a5.22 5.22 0 0 0-5.219 5.22v8.265c0 2.075 2.533 3.085 3.962 1.581l2.976-3.134h4.624l2.875 3.46c1.374 1.654 4.063.682 4.063-1.467V9.106a5.22 5.22 0 0 0-5.219-5.219M8.138 8.39v4m-2-2h4\"/><circle cx=\"14.662\" cy=\"9.39\" r=\"1\" fill=\"currentColor\"/><circle cx=\"16.862\" cy=\"11.59\" r=\"1\" fill=\"currentColor\"/></g>"}, "gift": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.5 12.5H12V21H7a2.5 2.5 0 0 1-2.5-2.5zm-1.75-3A1.5 1.5 0 0 1 4.25 8H12v4.5H4.25a1.5 1.5 0 0 1-1.5-1.5zm9.25 3h7.5v6A2.5 2.5 0 0 1 17 21h-5zM12 8h7.75a1.5 1.5 0 0 1 1.5 1.5V11a1.5 1.5 0 0 1-1.5 1.5H12zM7 5.5A2.5 2.5 0 0 1 9.5 3v0A2.5 2.5 0 0 1 12 5.5V8H9.5A2.5 2.5 0 0 1 7 5.5m10 0A2.5 2.5 0 0 0 14.5 3v0A2.5 2.5 0 0 0 12 5.5V8h2.5A2.5 2.5 0 0 0 17 5.5\"/>"}, "git-commit": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.75 12a3.75 3.75 0 1 1-7.5 0a3.75 3.75 0 0 1 7.5 0m0 0h5.5m-18.5 0h5.5\"/>"}, "github": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M9.096 21.25v-3.146a3.33 3.33 0 0 1 .758-2.115c-3.005-.4-5.28-1.859-5.28-5.798c0-1.666 1.432-3.89 1.432-3.89c-.514-1.13-.5-3.084.06-3.551c0 0 1.95.175 3.847 1.75c1.838-.495 3.764-.554 5.661 0c1.897-1.575 3.848-1.75 3.848-1.75c.558.467.573 2.422.06 3.551c0 0 1.432 2.224 1.432 3.89c0 3.94-2.276 5.398-5.28 5.798a3.33 3.33 0 0 1 .757 2.115v3.146\"/><path d=\"M3.086 16.57c.163.554.463 1.066.878 1.496c.414.431.932.77 1.513.988a4.46 4.46 0 0 0 3.62-.216\"/></g>"}, "gitlab": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.063 12.052a3.19 3.19 0 0 1-1.058 3.606l-6.706 5.164a2.13 2.13 0 0 1-2.598 0l-6.706-5.164a3.19 3.19 0 0 1-1.058-3.606L5.915 3.73l2.37 6.621c.15.423.552.706 1.001.706h5.428c.45 0 .85-.283 1.002-.706l2.37-6.621z\"/>"}, "globe": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.25 12A9.25 9.25 0 0 0 12 2.75M21.25 12H2.75m18.5 0A9.25 9.25 0 0 1 12 21.25m0-18.5A9.25 9.25 0 0 0 2.75 12M12 2.75c-.5 0-4 4.141-4 9.25s3.5 9.25 4 9.25m0-18.5c.5 0 4 4.141 4 9.25s-3.5 9.25-4 9.25M2.75 12A9.25 9.25 0 0 0 12 21.25\"/>"}, "google": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M13.365 2.83a9.25 9.25 0 0 1 4.744 2.089c.338.284.336.794.024 1.106l-1.616 1.616c-.312.312-.816.306-1.171.044a5.365 5.365 0 1 0 1.615 6.705h-3.91a.8.8 0 0 1-.8-.8V11.3a.8.8 0 0 1 .8-.8h7.493c.316 0 .61.186.681.495c.313 1.362-.125 3.246-.158 3.384l-.004.016c-.528 1.963-1.661 3.706-3.274 4.944a9.25 9.25 0 1 1-4.424-16.51\"/>"}, "google-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.201 9.947a5.365 5.365 0 0 1 8.145-2.262c.355.262.858.268 1.17-.044l1.617-1.616c.312-.312.314-.822-.024-1.106A9.25 9.25 0 0 0 3.612 8.46M7.2 9.947a5.365 5.365 0 0 0 1.69 6.31M7.2 9.947L3.611 8.46m5.28 7.796a5.365 5.365 0 0 0 6.533 0m-6.532 0l-2.365 3.082m8.897-3.082a5.4 5.4 0 0 0 1.537-1.866h-3.91a.8.8 0 0 1-.8-.8V11.3a.8.8 0 0 1 .8-.8h7.493c.316 0 .61.186.681.495c.313 1.362-.125 3.246-.158 3.384l-.004.016c-.528 1.963-1.661 3.706-3.274 4.944m-2.365-3.083l2.365 3.082m0 0a9.25 9.25 0 0 1-11.262 0m0 0A9.25 9.25 0 0 1 3.612 8.46\"/>"}, "google-chrome": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 8.25a3.75 3.75 0 0 0-3.747 3.904M12 8.25a3.75 3.75 0 0 1 3.608 4.775M12 8.25h8.458m-4.85 4.775a3.752 3.752 0 0 1-7.355-.871m7.355.871l-3.08 8.21m7.93-12.985A9.252 9.252 0 0 0 4.6 6.45m15.858 1.8q.085.19.161.386a9.25 9.25 0 0 1-8.09 12.599m0 0A9.25 9.25 0 0 1 2.75 12c0-2.083.688-4.004 1.85-5.55m3.653 5.704L4.6 6.45\"/>"}, "google-play": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m16.28 16.311l3.29-1.861A2.79 2.79 0 0 0 21 12c0-.955-.476-1.91-1.43-2.45l-3.29-1.86m0 8.622L8.209 20.88a2.8 2.8 0 0 1-2.779 0a2.9 2.9 0 0 1-.7-.557m11.552-4.012L4.729 3.677m0 16.646A2.78 2.78 0 0 1 4 18.43V5.57a2.78 2.78 0 0 1 1.061-2.202a2.81 2.81 0 0 1 3.147-.248l8.073 4.569M4.729 20.323L16.281 7.69\"/>"}, "graph": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.5 4v13.5a3 3 0 0 0 3 3H20\"/><path d=\"m6.5 15l4.5-4.5l3.5 3.5L20 8.5\"/></g>"}, "grid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"6.5\" height=\"6.5\" x=\"3.75\" y=\"13.75\" rx=\"2\"/><rect width=\"6.5\" height=\"6.5\" x=\"13.75\" y=\"13.75\" rx=\"2\"/><rect width=\"6.5\" height=\"6.5\" x=\"3.75\" y=\"3.75\" rx=\"2\"/><rect width=\"6.5\" height=\"6.5\" x=\"13.75\" y=\"3.75\" rx=\"2\"/></g>"}, "grid-dots": {"body": "<g fill=\"currentColor\"><circle cx=\"5\" cy=\"5\" r=\"1.5\"/><circle cx=\"12\" cy=\"5\" r=\"1.5\"/><circle cx=\"19\" cy=\"5\" r=\"1.5\"/><circle cx=\"5\" cy=\"12\" r=\"1.5\"/><circle cx=\"12\" cy=\"12\" r=\"1.5\"/><circle cx=\"19\" cy=\"12\" r=\"1.5\"/><circle cx=\"5\" cy=\"19\" r=\"1.5\"/><circle cx=\"12\" cy=\"19\" r=\"1.5\"/><circle cx=\"19\" cy=\"19\" r=\"1.5\"/></g>"}, "hamburger": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12 3.724c-4.302 0-7.79 3.051-7.79 6.816h15.58c0-3.765-3.488-6.816-7.79-6.816\"/><rect width=\"18.5\" height=\"5.355\" x=\"2.75\" y=\"10.54\" rx=\"2\"/><path d=\"M4.21 15.895h15.58l-.278 1.249a4 4 0 0 1-3.905 3.132H8.393a4 4 0 0 1-3.905-3.132zm10.926-2.833l-2.162-2.522h5.842l-2.162 2.522a1 1 0 0 1-1.518 0\"/></g>"}, "hand": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M4 10.059v3.424c0 1.853 0 2.78.221 3.536c.527 1.8 1.935 3.216 3.735 3.846c.6.176 1.196.363 2.344.532a5.8 5.8 0 0 0 2.014-.066c.303-.062.55-.115.758-.16c.49-.106.98-.233 1.43-.454c.508-.248.903-.506 1.475-.933c.342-.255.655-.566 1.28-1.188l3.247-3.23a1.68 1.68 0 0 0 0-2.384a1.7 1.7 0 0 0-2.396 0l-2.25 2.239v-5.162\"/><path d=\"M12.893 7.852V5.95c0-.815.664-1.475 1.483-1.475c.818 0 1.482.66 1.482 1.475v4.424m-5.929-.319V3.95c0-.815.664-1.475 1.482-1.475c.819 0 1.482.66 1.482 1.475v6.109M6.964 7.32v2.739v-5.104a1.483 1.483 0 0 1 2.965 0v5.104M6.964 8.854V7.95c0-.815-.663-1.475-1.482-1.475C4.664 6.475 4 7.135 4 7.95v2.738\"/></g>"}, "hard-drive": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.25 16.75v2.5a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2v-2.5m18.5 0a2 2 0 0 0-2-2H4.75a2 2 0 0 0-2 2m18.5 0v-1.63a3 3 0 0 0-.09-.728l-2.342-9.37a3 3 0 0 0-2.91-2.272H8.092a3 3 0 0 0-2.91 2.272l-2.342 9.37a3 3 0 0 0-.09.727v1.631\"/><circle cx=\"18\" cy=\"18\" r=\"1\" fill=\"currentColor\"/></g>"}, "hash": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.778 8.395H21.25m-18.5 7.21h17.472M6.282 21.13L9.495 2.87m5.01 18.26l3.212-18.26\"/>"}, "hat-graduation": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.652 14.714V9.78m3.18 2.07l4.049 2.667a4 4 0 0 0 4.402 0l4.049-2.668m-12.5 0L3.099 10.05a.99.99 0 0 1-.45-.815m3.183 2.616v5.061c0 .495.119.987.44 1.364c.747.877 2.514 2.39 5.81 2.39s5.063-1.513 5.81-2.39c.32-.377.44-.869.44-1.364V11.85m0 0l2.48-1.634a1.2 1.2 0 0 0 0-2.004l-6.53-4.302a4 4 0 0 0-4.401 0L3.099 8.379a.99.99 0 0 0-.45.855m0 0v.547\"/>"}, "headphones": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M21.25 17.368V12A9.25 9.25 0 0 0 12 2.75v0A9.25 9.25 0 0 0 2.75 12v5.368\"/><path d=\"M2.75 13.321h4a1.5 1.5 0 0 1 1.5 1.5v4.429a2 2 0 0 1-2 2h-1.5a2 2 0 0 1-2-2zm13 1.5a1.5 1.5 0 0 1 1.5-1.5h4v5.929a2 2 0 0 1-2 2h-1.5a2 2 0 0 1-2-2z\"/></g>"}, "headphones-off": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M21.25 21.25L2.75 2.75\"/><path fill=\"currentColor\" d=\"M17.75 21.25h1.5c.552 0 1.052-.224 1.414-.586L15.75 15.75v3.5a2 2 0 0 0 2 2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M21.25 17v-5A9.25 9.25 0 0 0 12 2.75c-1.433 0-2.79.326-4 .907M2.75 17.368V12a9.22 9.22 0 0 1 2.71-6.54m15.79 7.861H17.5m-14.75 0h4a1.5 1.5 0 0 1 1.5 1.5v4.429a2 2 0 0 1-2 2h-1.5a2 2 0 0 1-2-2zm15 7.929h1.5c.552 0 1.052-.224 1.414-.586L15.75 15.75v3.5a2 2 0 0 0 2 2\"/></g>"}, "heart": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.087 6.477a4.565 4.565 0 0 1 6.456 0L12 7.934l1.457-1.457a4.565 4.565 0 0 1 6.456 6.457l-1.457 1.456l.013.013l-6.456 6.457l-.013-.013l-.013.013l-6.456-6.457l.013-.013l-1.457-1.456a4.565 4.565 0 0 1 0-6.457Z\"/>"}, "heart-stylistic": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m12 7.934l-1.457-1.457a4.565 4.565 0 1 0-6.456 6.457l1.457 1.456M12 7.934l1.457-1.457a4.565 4.565 0 0 1 6.456 6.457l-1.457 1.456l.013.013l-6.456 6.457l-.013-.013l-.013.013l-6.456-6.457l.013-.013M12 7.934L5.544 14.39\"/>"}, "hexagon": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.42 3.173a3.16 3.16 0 0 1 3.16 0l5.275 3.046a3.16 3.16 0 0 1 1.579 2.735v6.092a3.16 3.16 0 0 1-1.58 2.735l-5.275 3.046a3.16 3.16 0 0 1-3.158 0L5.145 17.78a3.16 3.16 0 0 1-1.579-2.735V8.954c0-1.128.602-2.17 1.58-2.735z\"/>"}, "highlighter": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 2.75v3.5a3.5 3.5 0 0 0 3.5 3.5h9.5a3.5 3.5 0 0 0 3.5-3.5v-3.5m-14.5 7h12.5v1.8a2.2 2.2 0 0 1-2.2 2.2h-8.1a2.2 2.2 0 0 1-2.2-2.2zm10.5 4h-8.5v5.663a1.3 1.3 0 0 0 1.733 1.226l5.433-1.918a2 2 0 0 0 1.334-1.886z\"/>"}, "highlighter-accent": {"body": "<path fill=\"currentColor\" d=\"M20.25 6.25v-3.5H3.75v3.5a3.5 3.5 0 0 0 3.5 3.5h9.5a3.5 3.5 0 0 0 3.5-3.5m-4 7.5h-8.5v5.663a1.3 1.3 0 0 0 1.733 1.226l5.433-1.918a2 2 0 0 0 1.334-1.886z\"/>"}, "history": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M4.281 14.385a8.25 8.25 0 1 0 .824-6.26l-.477.88m-.523-4.63v3.75a1 1 0 0 0 .523.88m4.227.12h-3.75a1 1 0 0 1-.477-.12\"/><path d=\"M12.25 8v4.25l3.685 2.117\"/></g>"}, "home": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.29 20.663h3.017a2.194 2.194 0 0 0 2.193-2.194v-6.454a3.3 3.3 0 0 0-1.13-2.48l-5.93-5.166a2.194 2.194 0 0 0-2.88 0L4.63 9.534a3.3 3.3 0 0 0-1.13 2.481v6.454c0 1.212.982 2.194 2.194 2.194h3.29m6.306 0v-6.581c0-.908-.736-1.645-1.645-1.645H10.63c-.909 0-1.645.737-1.645 1.645v6.581m6.306 0H8.984\"/>"}, "home-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.71 18.65v-7.622a3 3 0 0 0-1.151-2.362l-6.326-4.951a2 2 0 0 0-2.466 0l-6.326 4.95a3 3 0 0 0-1.15 2.363v7.622c0 1.16.94 2.1 2.1 2.1h3.97v-7.965h5.278v7.965h3.97a2.1 2.1 0 0 0 2.1-2.1\"/>"}, "hourglass": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" d=\"m4.095 3.298l15.81-.048m-15.81 17.5l15.81-.048\"/><path d=\"M18.426 3.31H5.574l.079 1.449a7.38 7.38 0 0 0 2.251 4.913l1.242 1.195a1.58 1.58 0 0 1 0 2.279L7.904 14.34a7.38 7.38 0 0 0-2.251 4.913l-.08 1.448h12.853l-.079-1.445a7.38 7.38 0 0 0-2.256-4.917l-1.242-1.194a1.58 1.58 0 0 1 0-2.28l1.242-1.193a7.38 7.38 0 0 0 2.256-4.918z\"/></g>"}, "html": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"m20.754 4.792l-2.84 14.31a1.5 1.5 0 0 1-1.099 1.161l-4.069 1.045a3 3 0 0 1-1.492 0l-4.07-1.045a1.5 1.5 0 0 1-1.097-1.16L3.246 4.792A1.5 1.5 0 0 1 4.717 3h14.566a1.5 1.5 0 0 1 1.471 1.792\"/><path d=\"M17 6.881H7.732a.6.6 0 0 0-.589.718l.859 4.292h7.996l-.891 4.458a1 1 0 0 1-.64.744l-2.126.77a1 1 0 0 1-.681 0l-2.127-.77a1 1 0 0 1-.64-.744l-.224-1.119\"/></g>"}, "infinity": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.872 9.172a3.72 3.72 0 0 1 5.419 0L12 12l-2.71 2.828a3.72 3.72 0 0 1-5.418 0c-1.496-1.562-1.496-4.094 0-5.656Zm16.256 0a3.72 3.72 0 0 0-5.419 0L12 12l2.71 2.828a3.72 3.72 0 0 0 5.418 0c1.496-1.562 1.496-4.094 0-5.656Z\"/>"}, "info": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-width=\"1.5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 11.813v5\"/><circle cx=\"12\" cy=\"8.438\" r=\"1.25\" fill=\"currentColor\"/></g>"}, "info-square": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-width=\"1.5\" d=\"M8.25 3.75a4.5 4.5 0 0 0-4.5 4.5v7.5a4.5 4.5 0 0 0 4.5 4.5h7.5a4.5 4.5 0 0 0 4.5-4.5v-7.5a4.5 4.5 0 0 0-4.5-4.5z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 11.435v5\"/><circle cx=\"12\" cy=\"8.06\" r=\"1.25\" fill=\"currentColor\"/></g>"}, "instagram": {"body": "<g fill=\"none\"><rect width=\"17\" height=\"17\" x=\"3.5\" y=\"3.5\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"5.5\"/><circle cx=\"12\" cy=\"12\" r=\"3.606\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/><circle cx=\"16.894\" cy=\"7.106\" r=\"1.03\" fill=\"currentColor\"/></g>"}, "javascript": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"2\"/><path d=\"M11.5 11.25v5a1 1 0 0 1-1 1H9m8.25-6h-2a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-2\"/></g>"}, "keyboard": {"body": "<g fill=\"none\"><rect width=\"18.5\" height=\"13.5\" x=\"2.75\" y=\"5.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"3\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8 15.38h8\"/><circle cx=\"7.5\" cy=\"8.875\" r=\"1\" fill=\"currentColor\"/><circle cx=\"10.5\" cy=\"8.875\" r=\"1\" fill=\"currentColor\"/><circle cx=\"13.5\" cy=\"8.875\" r=\"1\" fill=\"currentColor\"/><circle cx=\"16.5\" cy=\"8.875\" r=\"1\" fill=\"currentColor\"/><circle cx=\"7.5\" cy=\"11.875\" r=\"1\" fill=\"currentColor\"/><circle cx=\"10.5\" cy=\"11.875\" r=\"1\" fill=\"currentColor\"/><circle cx=\"13.5\" cy=\"11.875\" r=\"1\" fill=\"currentColor\"/><circle cx=\"16.5\" cy=\"11.875\" r=\"1\" fill=\"currentColor\"/></g>"}, "keyboard-command": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 5.751a3 3 0 0 1 3-3v0a3 3 0 0 1 3 3v3h-3a3 3 0 0 1-3-3m0 12.498a3 3 0 0 0 3 3v0a3 3 0 0 0 3-3v-3h-3a3 3 0 0 0-3 3m18.5-12.498a3 3 0 0 0-3-3v0a3 3 0 0 0-3 3v3h3a3 3 0 0 0 3-3m0 12.498a3 3 0 0 1-3 3v0a3 3 0 0 1-3-3v-3h3a3 3 0 0 1 3 3M8.75 8.751h6.5v6.5h-6.5z\"/>"}, "keyboard-shift": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m3.503 11.913l6.601-8.252a2.428 2.428 0 0 1 3.792 0l6.6 8.251c.83 1.037.092 2.573-1.235 2.573h-4.095v3.725c0 1.064 0 1.596-.207 2.003a1.9 1.9 0 0 1-.83.83c-.406.207-.938.207-2.002.207h-.254c-1.064 0-1.596 0-2.002-.207a1.9 1.9 0 0 1-.83-.83c-.207-.407-.207-.939-.207-2.003v-3.725H4.739c-1.327 0-2.065-1.536-1.236-2.572\"/>"}, "kotlin": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.456 5.25a1.5 1.5 0 0 1 1.5-1.5h12.586c.89 0 1.337 1.077.707 1.707L12.706 12l6.543 6.543c.63.63.184 1.707-.707 1.707H5.956a1.5 1.5 0 0 1-1.06-.44a1.5 1.5 0 0 1-.44-1.06zm8.25-1.5L4.456 12m8.25 0l-7.81 7.81\"/>"}, "laptop": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.75 7a2 2 0 0 1 2-2h10.5a2 2 0 0 1 2 2v9H4.75zm-2 10a1 1 0 0 1 1-1h16.5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2z\"/>"}, "layers": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12.991 3.066a2 2 0 0 0-1.982 0L2.75 7.778l8.259 4.712a2 2 0 0 0 1.982 0l8.259-4.712z\"/><path stroke-linecap=\"round\" d=\"m2.75 12l7.268 4.147a4 4 0 0 0 3.964 0L21.25 12\"/><path stroke-linecap=\"round\" d=\"m2.75 16.222l7.268 4.147a4 4 0 0 0 3.964 0l7.268-4.147\"/></g>"}, "layout": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 8.75v8a3 3 0 0 0 3 3H10m-7.25-11v-1.5a3 3 0 0 1 3-3h12.5a3 3 0 0 1 3 3v1.5m-18.5 0H10m11.25 0v8a3 3 0 0 1-3 3H10m11.25-11H10m0 0v11\"/>"}, "leaf": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M16.508 16.223a6.38 6.38 0 0 1-9.016 0a6.367 6.367 0 0 1 0-9.009l3.094-3.091a2 2 0 0 1 2.828 0l3.094 3.091a6.367 6.367 0 0 1 0 9.01M12 12.265v9.025\"/>"}, "leaf-three": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.526 7.499a4.75 4.75 0 0 1 4.75-4.75h2.217c.887 0 1.33 0 1.669.172c.298.152.54.394.692.692c.172.339.172.782.172 1.669v2.217c0 .433-.058.853-.167 1.252m-10.716 2.84l2.3 2.301m5.624 1.288l-6.071 6.071M12.012 9.81a6.03 6.03 0 0 1 3.423-1.059h2.424M12.012 9.81a4.85 4.85 0 0 0-4.163-2.361H5.586c-.905 0-1.358 0-1.704.176a1.6 1.6 0 0 0-.706.706C3 8.677 3 9.13 3 10.035v2.262a4.85 4.85 0 0 0 6.7 4.483m2.312-6.97a6.1 6.1 0 0 0-1.54 1.523a6 6 0 0 0-1.029 2.56M17.86 8.75h.404c1.132 0 1.698 0 2.13.22c.38.194.689.503.883.884c.22.432.22.997.22 2.129v2.828A6.06 6.06 0 0 1 9.7 16.78m-.258-2.888A6.1 6.1 0 0 0 9.7 16.78\"/>"}, "leaf-two": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m7.35 8.1l1.93 1.93m5.277 3.663L8 20.25m4.872-13.162a6.5 6.5 0 0 1 2.082-.338h3.055c1.222 0 1.833 0 2.3.238c.41.209.744.543.953.953c.238.467.238 1.078.238 2.3v3.055a6.546 6.546 0 0 1-13.062.625m4.434-6.833a5.09 5.09 0 0 0-4.78-3.338H5.714c-.95 0-1.425 0-1.788.185a1.7 1.7 0 0 0-.742.742C3 5.04 3 5.515 3 6.465v2.376a5.09 5.09 0 0 0 5.438 5.08m4.434-6.833A6.57 6.57 0 0 0 9.28 10.03m-.842 3.89a6.5 6.5 0 0 1 .842-3.89\"/>"}, "library": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"3.998\" height=\"15\" x=\"2.75\" y=\"4.504\" rx=\"1.5\"/><rect width=\"3.998\" height=\"15\" x=\"9.201\" y=\"4.504\" rx=\"1.5\"/><path d=\"M15.267 8.378c-.165-.615.2-1.247.814-1.411l1.038-.278c.614-.165 1.245.2 1.41.814l2.681 10.014a1.15 1.15 0 0 1-.814 1.41l-1.038.279a1.15 1.15 0 0 1-1.41-.815z\"/></g>"}, "lightbulb": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.013 17.104c.126-.958.736-1.764 1.464-2.4a6.816 6.816 0 1 0-8.955 0c.729.636 1.34 1.442 1.465 2.4l.084.633l.233 1.774a2 2 0 0 0 1.983 1.739h1.426a2 2 0 0 0 1.983-1.739l.233-1.774zm-5.943.633h5.86\"/>"}, "line-diagonal": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"m21.25 2.75l-18.5 18.5\"/>"}, "link": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.621 7.5H7.25a4.5 4.5 0 0 0-4.5 4.5v0a4.5 4.5 0 0 0 4.5 4.5h2.371m4.758-9h2.371a4.5 4.5 0 0 1 4.5 4.5v0a4.5 4.5 0 0 1-4.5 4.5h-2.371M7.243 12h9.514\"/>"}, "linux": {"body": "<g fill=\"none\"><rect width=\"1.48\" height=\"1.48\" x=\"12.671\" y=\"5.713\" fill=\"currentColor\" rx=\".74\"/><rect width=\"1.48\" height=\"1.48\" x=\"10.381\" y=\"5.713\" fill=\"currentColor\" rx=\".74\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.603 20.846a1.916 1.916 0 0 0 3.253.436l1.852-2.308a.69.69 0 0 0-.193-1.03l-1.538-.888m-3.374 3.79a5.9 5.9 0 0 1-2.337.476a5.9 5.9 0 0 1-2.453-.526m4.79.05a1.92 1.92 0 0 1-.028-1.457l.89-2.29m-5.652 3.697a1.916 1.916 0 0 1-2.474 1.074L4.58 20.795a.69.69 0 0 1-.347-.989l1.661-2.878a.69.69 0 0 1 .542-.343m3.377 4.21a1.92 1.92 0 0 0-.58-2.207l-.02-.017m-2.777-1.986a6 6 0 0 1-.125-1.218a5.92 5.92 0 0 1 1.16-3.531c.809-1.098 1.588-2.313 1.588-3.677V6.206a3.206 3.206 0 1 1 6.413 0V8.16c0 1.364.78 2.58 1.589 3.676a5.93 5.93 0 0 1 1.16 3.532c0 .586-.085 1.153-.244 1.688m-11.541-.47a.7.7 0 0 1 .488.15l2.288 1.836m8.766-1.516l-1.341-.774a.69.69 0 0 0-.989.348l-.182.47m0 0c-.106-2.161-1.498-4.939-3.2-4.939c-1.771 0-3.207 3.007-3.207 5.197c0 .423.054.831.153 1.214\"/><path fill=\"currentColor\" d=\"M11.08 8.272a.6.6 0 0 0-.46.984l1.185 1.423a.6.6 0 0 0 .922 0l1.185-1.423a.6.6 0 0 0-.461-.984z\"/></g>"}, "location": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M6.4 4.76a7.92 7.92 0 0 1 11.2 11.2l-4.186 4.186a2 2 0 0 1-2.828 0L6.4 15.96a7.92 7.92 0 0 1 0-11.2Z\"/><circle cx=\"12\" cy=\"10.36\" r=\"3\" stroke-linecap=\"round\"/></g>"}, "lock": {"body": "<g fill=\"none\"><rect width=\"14.478\" height=\"12.87\" x=\"4.761\" y=\"8.38\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"3\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.217 8.38V5.967A3.217 3.217 0 0 0 12 2.75v0a3.217 3.217 0 0 0-3.217 3.217V8.38\"/><circle cx=\"12\" cy=\"14.815\" r=\"1.5\" fill=\"currentColor\"/></g>"}, "lock-open": {"body": "<g fill=\"none\"><rect width=\"14.478\" height=\"12.87\" x=\"4.761\" y=\"8.38\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"3\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.783 8.38V5.967a3.217 3.217 0 0 1 6.132-1.363\"/><circle cx=\"12\" cy=\"14.815\" r=\"1.5\" fill=\"currentColor\"/></g>"}, "magnet": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.238 15.75v3.703a1.8 1.8 0 0 0 1.8 1.8h2.412a1.8 1.8 0 0 0 1.8-1.8V15.75m-6.012 0v-3.752a3.238 3.238 0 1 0-6.475 0v3.752m6.475 0h6.012m0 0v-3.752a9.25 9.25 0 0 0-18.5 0v3.752m6.012 0v3.703a1.8 1.8 0 0 1-1.8 1.8H4.55a1.8 1.8 0 0 1-1.8-1.8V15.75m6.012 0H2.75\"/>"}, "mail": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"15.5\" x=\"2.75\" y=\"4.25\" rx=\"3\"/><path d=\"m2.75 8l8.415 3.866a2 2 0 0 0 1.67 0L21.25 8\"/></g>"}, "mail-open": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m20.59 8.949l-7.755 3.562a2 2 0 0 1-1.67 0L3.41 8.95m14.84 11.3H5.75a3 3 0 0 1-3-3v-7.215A2.5 2.5 0 0 1 3.93 7.91l7.014-4.36a2 2 0 0 1 2.112 0l7.014 4.36a2.5 2.5 0 0 1 1.18 2.124v7.215a3 3 0 0 1-3 3\"/>"}, "map": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.496 4.439L4.247 6.91a1 1 0 0 0-.497.864V18.26a1 1 0 0 0 1.503.865l3.243-1.887a1.5 1.5 0 0 1 1.508 0l3.992 2.322a1.5 1.5 0 0 0 1.508 0l4.249-2.472a1 1 0 0 0 .497-.864V5.739a1 1 0 0 0-1.503-.865l-3.243 1.887a1.5 1.5 0 0 1-1.508 0L10.004 4.44a1.5 1.5 0 0 0-1.508 0Zm.754.311v11.8m5.5-9.1v11.8\"/>"}, "mask": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/><path fill=\"currentColor\" d=\"M8.844 20.698a9.254 9.254 0 0 1 0-17.396a9.254 9.254 0 0 1 0 17.396\"/></g>"}, "math": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M2.75 6.248h7m-3.5-3.5v7m8-3.5h7M3.45 20.552l2.8-2.8m0 0l2.8-2.8m-2.8 2.8l-2.8-2.8m2.8 2.8l2.8 2.8m5.2-4.9h7m-7 4.2h7\"/>"}, "megaphone": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" d=\"m7.142 15.9l-2.5-.627a2.5 2.5 0 0 1-1.892-2.425V10.78a2.5 2.5 0 0 1 1.891-2.424l13.5-3.39a2.5 2.5 0 0 1 3.109 2.425v8.847a2.5 2.5 0 0 1-3.109 2.425l-5.19-1.304m-5.81-1.458a3 3 0 1 0 5.809 1.459M7.143 15.9l5.809 1.46\"/>"}, "megaphone-loud": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><path d=\"m8.784 19.283l-1.251.75c-.632.378-.948.568-1.272.637c-.44.094-.897.037-1.3-.16c-.298-.147-.558-.408-1.08-.93c-.52-.52-.781-.781-.927-1.079a2 2 0 0 1-.16-1.3c.068-.325.258-.642.636-1.274l4.356-7.282c.718-1.2 1.077-1.8 1.57-2.061c.433-.23.935-.291 1.41-.175c.544.133 1.038.628 2.026 1.617l2.636 2.638c.988.989 1.482 1.483 1.616 2.026c.116.476.054.979-.175 1.412c-.261.494-.86.853-2.06 1.572l-1.27.761m-4.755 2.848a2.778 2.778 0 0 0 4.463.744a2.784 2.784 0 0 0 .291-3.592m-4.754 2.848l4.754-2.848\"/><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M20.007 4.404L17.542 6.87m3.709 3.007h-2.49m-4.224-6.719V5.65\"/></g>"}, "mention": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M16.625 20.01A9.25 9.25 0 1 1 21.25 12v1.5a2.5 2.5 0 0 1-2.5 2.5v0a2.5 2.5 0 0 1-2.5-2.5V12m0 0a4.25 4.25 0 1 1-8.5 0a4.25 4.25 0 0 1 8.5 0m0 0V7.75\"/>"}, "menu": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 4.75h18.5M2.75 12h18.5m-18.5 7.25h18.5\"/>"}, "microphone": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.801 6.449a3.199 3.199 0 1 1 6.398 0v4.95a3.199 3.199 0 0 1-6.398 0zM12 18.181a6.78 6.78 0 0 1-6.779-6.779M12 18.182a6.78 6.78 0 0 0 6.779-6.78M12 18.182v2.568\"/>"}, "microphone-off": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"m2.75 21.25l18.5-18.5\"/><path fill=\"currentColor\" d=\"M15.199 11.398a3.199 3.199 0 0 1-5.14 2.543l5.14-5.14z\"/><path fill=\"currentColor\" d=\"M8.051 11.125a.75.75 0 0 0 1.5 0zm-2.08.277a.75.75 0 0 0-1.5 0zm13.558 0a.75.75 0 0 0-1.5 0zM11.25 20.75a.75.75 0 0 0 1.5 0zm3.949-11.949h.75v-1.81l-1.28 1.28zm-7.188 7.122a.75.75 0 0 0-.993 1.124zm2.048-1.982l-.53-.53l-.606.606l.68.52zm4.129-8.594a.75.75 0 0 0 1.339-.676zm-9.132 8.97a.75.75 0 1 0 1.383-.582zm4.495-3.192V6.449h-1.5v4.676zm4.898.273A2.45 2.45 0 0 1 12 13.848v1.5a3.95 3.95 0 0 0 3.949-3.95zM12 2.5a3.95 3.95 0 0 0-3.949 3.949h1.5A2.45 2.45 0 0 1 12 4zm6.029 8.902c0 3.33-2.7 6.03-6.029 6.03v1.5a7.53 7.53 0 0 0 7.529-7.53zm-6.779 6.78v2.568h1.5v-2.569zM14.449 8.8v2.597h1.5V8.801zM12 17.431a6 6 0 0 1-3.989-1.508l-.993 1.124A7.5 7.5 0 0 0 12 18.932zm0-3.584c-.56 0-1.073-.187-1.486-.502l-.91 1.192c.664.508 1.496.81 2.396.81zM12 4c.955 0 1.784.546 2.188 1.347l1.339-.676A3.95 3.95 0 0 0 12 2.5zm-5.561 9.735a6 6 0 0 1-.468-2.333h-1.5c0 1.032.208 2.017.585 2.914zm8.23-5.464l-5.14 5.14l1.06 1.06l5.14-5.14z\"/></g>"}, "microsoft": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 3.75H5.75a2 2 0 0 0-2 2V12M12 3.75h6.25a2 2 0 0 1 2 2V12M12 3.75v16.5m0 0h6.25a2 2 0 0 0 2-2V12M12 20.25H5.75a2 2 0 0 1-2-2V12m0 0h16.5\"/>"}, "microsoft-edge": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.76 12a9.25 9.25 0 0 0 11.527 8.968M2.761 12a9.25 9.25 0 0 1 9.25-9.25c3.795 0 7.996 1.777 9.017 6.798c.223 1.1.09 2.258-.463 3.235c-.582 1.025-1.559 2.182-3.01 2.182c-.55.088-4.164.176-3.979-1.312A2.27 2.27 0 0 0 14.287 12M2.761 12s.444-4.849 5.78-4.833C14.117 7.184 14.288 12 14.288 12m0 0a2.277 2.277 0 0 0-2.277-2.277c-.642 0-1.636.694-1.636.694m3.913 10.55a9.26 9.26 0 0 0 5.406-3.814c.185-.275.058-.694-.26-.6a.5.5 0 0 0-.174.092c-1.364.617-4.22 1.12-6.685-.257c-1.5-.838-2.374-2.135-2.639-3.202c-.099-.255-.201-.896-.201-1.186c0-.615.243-1.173.64-1.583m3.913 10.55s-6.012.49-6.546-5.053c-.297-3.09 1.307-4.702 2.633-5.497\"/>"}, "moon": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M11.578 3.512a6.307 6.307 0 0 0 8.91 8.91a.45.45 0 0 1 .466-.095c.176.067.29.24.275.428A9.255 9.255 0 1 1 5.461 5.45a9.22 9.22 0 0 1 5.784-2.68a.42.42 0 0 1 .428.275c.06.16.02.34-.095.466Z\"/>"}, "more": {"body": "<g fill=\"currentColor\"><circle cx=\"6\" cy=\"12\" r=\"1.75\"/><circle cx=\"12\" cy=\"12\" r=\"1.75\"/><circle cx=\"18\" cy=\"12\" r=\"1.75\"/></g>"}, "more-vertical": {"body": "<g fill=\"currentColor\"><circle cx=\"12\" cy=\"18\" r=\"1.75\" transform=\"rotate(-90 12 18)\"/><circle cx=\"12\" cy=\"12\" r=\"1.75\" transform=\"rotate(-90 12 12)\"/><circle cx=\"12\" cy=\"6\" r=\"1.75\" transform=\"rotate(-90 12 6)\"/></g>"}, "motherboard": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"13.5\" height=\"13.5\" x=\"5.25\" y=\"5.25\" rx=\"3\"/><path d=\"M18.75 8.75h2.5M18.75 12h2.5m-2.5 3.25h2.5m-6 3.5v2.5M12 18.75v2.5m-3.25-2.5v2.5m-6-12.5h2.5M2.75 12h2.5m-2.5 3.25h2.5m10-12.5v2.5M12 2.75v2.5m-3.25-2.5v2.5\"/><rect width=\"5\" height=\"5\" x=\"9.5\" y=\"9.5\" rx=\"1\"/></g>"}, "movie": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 7.5a3 3 0 0 1 3-3h12.5a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3H5.75a3 3 0 0 1-3-3zM7 5v14M17 5v14M2.75 9.5H7m-4.25 5H7m10-5h4.25m-4.25 5h4.25\"/>"}, "music-note": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M8.962 17.968V6.696a1.5 1.5 0 0 1 1.106-1.447l8.15-2.223a1.5 1.5 0 0 1 1.895 1.447v11.468M8.963 9.92l11.15-3.04M8.962 17.968a3.041 3.041 0 1 1-6.082 0a3.041 3.041 0 0 1 6.082 0\"/><path d=\"M20.113 15.94a3.041 3.041 0 1 1-6.082 0a3.041 3.041 0 0 1 6.082 0\"/></g>"}, "music-note-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12.5 17.25a4 4 0 1 1-8 0a4 4 0 0 1 8 0m0 0v-9m0 0l4.83 2.415a1.5 1.5 0 0 0 2.17-1.342V7.177a1.5 1.5 0 0 0-.83-1.342l-4.723-2.361a1 1 0 0 0-1.447.894z\"/>"}, "narrator": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.09 2.75a4 4 0 0 0-4 4v6.208a4 4 0 0 0 4 4h.093v3.792a.5.5 0 0 0 .839.368l4.52-4.16h4.369a4 4 0 0 0 4-4V6.75a4 4 0 0 0-4-4zM12 14V6m3.25 5.788V8.212m-6.5 3.576V8.212\"/>"}, "nodejs": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M9.75 8.5v7.964c0 .866-.456 1.626-1.142 2.052c-.745.463-1.672.13-2.432-.308l-1.43-.826a2.07 2.07 0 0 1-1.034-1.792V8.41c0-.74.394-1.423 1.035-1.792l6.218-3.59a2.07 2.07 0 0 1 2.07 0l6.218 3.59a2.07 2.07 0 0 1 1.035 1.792v7.18a2.07 2.07 0 0 1-1.035 1.792l-6.218 3.59a2.07 2.07 0 0 1-2.07 0l-.989-.57\"/><path d=\"M17.29 8.5h-2.75a1.75 1.75 0 0 0-1.75 1.75v0c0 .966.784 1.75 1.75 1.75h1c.966 0 1.75.784 1.75 1.75v0a1.75 1.75 0 0 1-1.75 1.75h-2.75\"/></g>"}, "note": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M16.5 4H8a4 4 0 0 0-4 4v8.5a4 4 0 0 0 4 4h6.843a4 4 0 0 0 2.829-1.172l1.656-1.656a4 4 0 0 0 1.172-2.829V8a4 4 0 0 0-4-4\"/><path d=\"M20.5 14H17a3 3 0 0 0-3 3v3.5M8 8h7.5M8 12h5\"/></g>"}, "note-add": {"body": "<g fill=\"none\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.5 12a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V6h2.493a.5.5 0 0 1 0 1H7v2.493a.5.5 0 1 1-1 0V7H3.507a.5.5 0 0 1 0-1H6V3.507a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.5 14h-1.7c-1.68 0-2.52 0-3.162.327a3 3 0 0 0-1.311 1.311C14 16.28 14 17.12 14 18.8v1.7M15.5 8H14m-2.5 4H13m.75-8h.35c2.24 0 3.36 0 4.216.436a4 4 0 0 1 1.748 1.748c.436.856.436 1.976.436 4.216v3.449c0 .978 0 1.468-.11 1.928c-.099.408-.26.798-.48 1.156c-.247.404-.593.75-1.285 1.442l-.25.25c-.692.692-1.038 1.038-1.442 1.286a4 4 0 0 1-1.156.479c-.46.11-.95.11-1.928.11H10.4c-2.24 0-3.36 0-4.216-.436a4 4 0 0 1-1.748-1.748C4 17.46 4 16.34 4 14.1v-.35\"/></g>"}, "npm": {"body": "<g fill=\"none\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"2\"/><path fill=\"currentColor\" d=\"M12 9a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v11.25h-4z\"/></g>"}, "octagon": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.583 3.336a2 2 0 0 1 1.414-.586h6.006a2 2 0 0 1 1.414.586l4.247 4.247a2 2 0 0 1 .586 1.414v6.006a2 2 0 0 1-.586 1.414l-4.247 4.247a2 2 0 0 1-1.414.586H8.997a2 2 0 0 1-1.414-.586l-4.247-4.247a2 2 0 0 1-.586-1.414V8.997a2 2 0 0 1 .586-1.414z\"/>"}, "open": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.25 3.75h-2.5a4 4 0 0 0-4 4v8.5a4 4 0 0 0 4 4h8.5a4 4 0 0 0 4-4v-2.5m-6.5-10h5.5c.276 0 .526.112.707.293m.293 6.207v-5.5a1 1 0 0 0-.293-.707M12.75 11.25l6.5-6.5l.707-.707\"/>"}, "open-source": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.157 20.136c.211.51.8.757 1.284.492a9.25 9.25 0 1 0-8.882 0c.484.265 1.073.018 1.284-.492l1.358-3.28c.212-.51-.043-1.086-.478-1.426a3.7 3.7 0 1 1 4.554 0c-.435.34-.69.916-.478 1.426z\"/>"}, "openai": {"body": "<path fill=\"currentColor\" d=\"m14.805 10.432l.375-.65zm-8.162-5.32l-.65-.374zm6.18-1.656l.375-.65zm2.463.46v-.75zm4.525 4.525h.75zm.832 2.363l.65-.375zm-1.656 6.182l.375.65zm-1.63 1.901l.65.375zm-6.18 1.657l-.375.65zm-2.463-.46v.75zM4.19 15.559h-.75zm-.832-2.363l-.65.375zm1.656-6.18l-.375-.65zm10.117-.01l.374-.65l-.374-.216l-.376.216zm2.76 5.207h.75v-.433l-.375-.216zM9.195 6.817l-.375-.65a.75.75 0 0 0-.375.65zm5.61 10.364l.375.65a.75.75 0 0 0 .375-.65zm-5.845-.134l-.375.649a.75.75 0 0 0 .75 0zM6.11 11.89h-.75a.75.75 0 0 0 .374.65zM12 8.812l-.375.65l2.805 1.62l.375-.65l.375-.65l-2.805-1.62zm2.805 1.62h-.75v3.24h1.5v-3.24zm0 3.24l-.375-.65l-2.805 1.62l.375.65l.375.649l2.805-1.62zM12 15.292l.375-.65l-2.805-1.62l-.375.65l-.375.65l2.805 1.619zm-2.805-1.62h.75v-3.24h-1.5v3.24zm0-3.24l.375.65l2.805-1.62l-.375-.65l-.375-.65l-2.805 1.62zm-2.552-5.32l.65.376a3.775 3.775 0 0 1 5.155-1.382l.375-.65l.375-.65a5.275 5.275 0 0 0-7.205 1.932zm6.18-1.656l-.375.65q.441.256.786.603l.53-.53l.53-.53a5.3 5.3 0 0 0-1.096-.842zm.941.723l.252.706a3.8 3.8 0 0 1 1.27-.219v-1.5c-.621 0-1.219.108-1.774.307zm1.522-.263v.75a3.775 3.775 0 0 1 3.775 3.775h1.5a5.275 5.275 0 0 0-5.275-5.275zm4.525 4.525h-.75q-.002.511-.13.983l.724.194l.724.195a5.3 5.3 0 0 0 .182-1.372zm-.156 1.177l-.486.572c.321.272.602.603.825.99l.65-.376l.649-.375a5.3 5.3 0 0 0-1.152-1.382zm.988 1.186l-.65.375a3.775 3.775 0 0 1-1.381 5.157l.375.65l.375.65a5.275 5.275 0 0 0 1.93-7.207zm-1.656 6.182l-.375-.65c-.293.17-.6.293-.916.377l.194.725l.194.725c.435-.117.866-.29 1.277-.528zm-1.097.452l-.738-.135a3.8 3.8 0 0 1-.444 1.21l.65.374l.649.375c.31-.538.515-1.11.62-1.69zm-.533 1.45l-.65-.376a3.775 3.775 0 0 1-5.155 1.382l-.375.65l-.375.65a5.275 5.275 0 0 0 7.205-1.932zm-6.18 1.656l.375-.65a3.8 3.8 0 0 1-.787-.604l-.53.53l-.532.53c.32.32.688.606 1.099.843zm-.943-.724l-.252-.706a3.8 3.8 0 0 1-1.268.22v1.5a5.3 5.3 0 0 0 1.772-.308zm-1.52.264v-.75a3.775 3.775 0 0 1-3.775-3.775h-1.5a5.275 5.275 0 0 0 5.275 5.275zM4.19 15.559h.75q.001-.512.13-.983l-.725-.194l-.724-.195a5.3 5.3 0 0 0-.18 1.372zm.155-1.177l.486-.572c-.32-.272-.6-.603-.823-.99l-.65.376l-.65.375c.31.537.703 1 1.151 1.382zm-.987-1.186l.65-.375a3.774 3.774 0 0 1 1.38-5.156l-.374-.65l-.375-.65a5.274 5.274 0 0 0-1.93 7.206zm1.656-6.18l.375.649a3.8 3.8 0 0 1 .915-.38l-.194-.724l-.194-.725a5.3 5.3 0 0 0-1.277.53zm1.096-.455l.738.135c.075-.413.222-.821.445-1.208l-.649-.375l-.65-.375a5.3 5.3 0 0 0-.622 1.688zM12 8.812l.375.65l1.565-.903l1.565-.904l-.376-.65l-.374-.649l-1.565.903l-1.565.904zm2.805 1.62l-.375.65l3.085 1.78l.375-.649l.375-.65l-3.085-1.78zm3.085 7.006h.75v-5.225h-1.5v5.225zm-8.695-7.006h.75V6.817h-1.5v3.615zm5.61 3.24h-.75v3.509h1.5v-3.51zM12 15.292l-.375-.65l-3.04 1.755l.375.65l.375.649l3.04-1.755zM6.11 6.56h-.75v5.33h1.5V6.56zm3.085 7.11l.375-.649l-3.086-1.781l-.375.65l-.375.649L8.82 14.32zm5.935-6.665l-.376.65l4.526 2.612l.375-.65l.375-.65l-4.526-2.612zm-5.935-.189l.375.65l4.57-2.639l-.376-.65l-.375-.649l-4.57 2.639zm1.04 13.003l.374.65l4.571-2.64l-.375-.65l-.375-.649l-4.57 2.64zm-5.89-5.438l-.376.65l4.616 2.664l.375-.65l.375-.649l-4.616-2.665z\"/>"}, "page-margins": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"15.5\" height=\"18.5\" x=\"4.25\" y=\"2.75\" rx=\"2.5\"/><path d=\"M8.25 2.75v18.5m-4-14.5h15.5m-15.5 10.5h15.5m-4-14.5v18.5\"/></g>"}, "paint-bucket": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"m18.677 13.35l-1.923 3.17c-.985 2.093 1.314 4.206 3.317 3.05a2.36 2.36 0 0 0 .864-3.225z\"/><path stroke-linecap=\"round\" d=\"m8.778 3.558l6.972 6.972M8.778 3.558L4.38 7.956c-.901.9-1.352 1.351-1.52 1.87a2.3 2.3 0 0 0-.112.704m6.03-6.972l-1.06-1.059m8.032 8.031l-4.398 4.398c-.9.9-1.351 1.351-1.871 1.52c-.457.149-.95.149-1.406 0c-.52-.169-.97-.62-1.871-1.52L4.38 13.104c-.901-.9-1.352-1.351-1.52-1.87a2.3 2.3 0 0 1-.112-.704m13.002 0H2.748m10.188 9.971H3.748\"/></g>"}, "paint-bucket-accent": {"body": "<path fill=\"currentColor\" d=\"m18.677 13.35l-1.923 3.17c-.985 2.092 1.314 4.206 3.317 3.05a2.36 2.36 0 0 0 .864-3.225zm-7.325 1.578l4.398-4.398H2.748c0 .237.037.475.112.703c.168.52.619.97 1.52 1.871l1.824 1.824c.9.9 1.351 1.351 1.87 1.52c.458.149.95.149 1.407 0c.52-.169.97-.62 1.871-1.52\"/>"}, "paintbrush": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5.32 5.75a3 3 0 0 1 3-3h7.36a3 3 0 0 1 3 3V12H5.32zM18.68 12H5.32v2.611a1.5 1.5 0 0 0 1.5 1.5h3.38v3.34a1.799 1.799 0 0 0 3.598 0v-3.34h3.382a1.5 1.5 0 0 0 1.5-1.5zM15.5 2.75V6.5m-3-3.75v2.5\"/>"}, "paintbrush-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.781 10.832A4.79 4.79 0 0 0 3.75 15.46v3.79a1 1 0 0 0 1 1h3.79a4.79 4.79 0 0 0 4.628-6.03m-3.387-3.388a4.8 4.8 0 0 1 3.387 3.387m-3.387-3.387l3.19-3.19m.197 6.577l3.19-3.19m-3.387-3.387l3.19-3.19a2.395 2.395 0 0 1 3.387 3.387l-3.19 3.19m-3.387-3.387l3.387 3.387\"/>"}, "paintbrush-2-sparkle": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M8.6 1.418a.64.64 0 0 0-1.2 0l-.167.45a4 4 0 0 1-2.366 2.365l-.449.166a.64.64 0 0 0 0 1.202l.45.166a4 4 0 0 1 2.365 2.366l.166.449a.64.64 0 0 0 1.202 0l.166-.45a4 4 0 0 1 2.366-2.365l.449-.166a.64.64 0 0 0 0-1.202l-.45-.166a4 4 0 0 1-2.365-2.366zM3.876 7.261a.4.4 0 0 0-.752 0l-.103.281a2.5 2.5 0 0 1-1.479 1.479l-.28.103a.4.4 0 0 0 0 .752l.28.103a2.5 2.5 0 0 1 1.479 1.479l.103.28a.4.4 0 0 0 .752 0l.103-.28a2.5 2.5 0 0 1 1.479-1.479l.28-.103a.4.4 0 0 0 0-.752l-.28-.103a2.5 2.5 0 0 1-1.479-1.479z\"/><path stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.781 11.832A4.795 4.795 0 0 0 4.75 16.46v3.79a1 1 0 0 0 1 1h3.79a4.79 4.79 0 0 0 4.628-6.03m-3.387-3.388a4.8 4.8 0 0 1 3.387 3.387m-3.387-3.387l3.19-3.19m.197 6.577l3.19-3.19m-3.387-3.387l3.19-3.19a2.395 2.395 0 0 1 3.387 3.387l-3.19 3.19m-3.387-3.387l3.387 3.387\"/></g>"}, "panel-bottom": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.25 20.25a3.5 3.5 0 0 1-3.5-3.5v-9.5a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5zm-3.5-5.797h16.5\"/>"}, "panel-bottom-open": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M3.75 16.75a3.5 3.5 0 0 0 3.5 3.5h9.5a3.5 3.5 0 0 0 3.5-3.5v-2.297H3.75z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 14.453v2.297a3.5 3.5 0 0 0 3.5 3.5h9.5a3.5 3.5 0 0 0 3.5-3.5v-2.297m-16.5 0V7.25a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5v7.203m-16.5 0h16.5\"/></g>"}, "panel-left": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 7.25a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5h-9.5a3.5 3.5 0 0 1-3.5-3.5zm5.797-3.5v16.5\"/>"}, "panel-left-contract": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 7.25a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5h-9.5a3.5 3.5 0 0 1-3.5-3.5zm5.797-3.5v16.5m5.213-6L12.55 12m0 0l2.21-2.25M12.55 12h4.7\"/>"}, "panel-left-expand": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 7.25a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5h-9.5a3.5 3.5 0 0 1-3.5-3.5zm5.797-3.5v16.5m5.493-6L17.25 12m0 0l-2.21-2.25M17.25 12h-4.7\"/>"}, "panel-left-open": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M7.25 3.75a3.5 3.5 0 0 0-3.5 3.5v9.5a3.5 3.5 0 0 0 3.5 3.5h2.297V3.75z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.547 3.75H7.25a3.5 3.5 0 0 0-3.5 3.5v9.5a3.5 3.5 0 0 0 3.5 3.5h2.297m0-16.5h7.203a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5H9.547m0-16.5v16.5\"/></g>"}, "panel-right": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.25 16.75a3.5 3.5 0 0 1-3.5 3.5h-9.5a3.5 3.5 0 0 1-3.5-3.5v-9.5a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5zm-5.797 3.5V3.75\"/>"}, "panel-right-contract": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.25 16.75a3.5 3.5 0 0 1-3.5 3.5h-9.5a3.5 3.5 0 0 1-3.5-3.5v-9.5a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5zm-5.797 3.5V3.75M9.24 14.25L11.45 12m0 0L9.24 9.75M11.45 12h-4.7\"/>"}, "panel-right-expand": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.25 16.75a3.5 3.5 0 0 1-3.5 3.5h-9.5a3.5 3.5 0 0 1-3.5-3.5v-9.5a3.5 3.5 0 0 1 3.5-3.5h9.5a3.5 3.5 0 0 1 3.5 3.5zm-5.797 3.5V3.75M8.96 14.25L6.75 12m0 0l2.21-2.25M6.75 12h4.7\"/>"}, "panel-right-open": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M16.75 3.75a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5h-2.297V3.75z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.453 3.75h2.297a3.5 3.5 0 0 1 3.5 3.5v9.5a3.5 3.5 0 0 1-3.5 3.5h-2.297m0-16.5H7.25a3.5 3.5 0 0 0-3.5 3.5v9.5a3.5 3.5 0 0 0 3.5 3.5h7.203m0-16.5v16.5\"/></g>"}, "parentheses": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.25 20.25a11.7 11.7 0 0 1-2.59-3.785c-.6-1.416-.91-2.933-.91-4.465s.31-3.05.91-4.465S7.14 4.833 8.25 3.75m7.5 16.5a11.7 11.7 0 0 0 2.59-3.785c.6-1.416.91-2.933.91-4.465s-.31-3.05-.91-4.465a11.7 11.7 0 0 0-2.59-3.785\"/>"}, "pause": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"5\" height=\"16.5\" x=\"5\" y=\"3.75\" rx=\"2\"/><rect width=\"5\" height=\"16.5\" x=\"14\" y=\"3.75\" rx=\"2\"/></g>"}, "pdf": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.792 21.25h8.416a3.5 3.5 0 0 0 3.5-3.5v-5.53a3.5 3.5 0 0 0-1.024-2.475l-5.969-5.97A3.5 3.5 0 0 0 10.24 2.75H7.792a3.5 3.5 0 0 0-3.5 3.5v11.5a3.5 3.5 0 0 0 3.5 3.5\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10.437 7.141c-.239.078-.392.236-.436.411c-.09.352 0 .73.253 1.203c.126.234.28.471.45.725l.092.137l.145.215l.019-.068l.086-.306q.148-.503.23-1.02c.089-.642-.011-1.018-.309-1.26c-.08-.065-.278-.119-.53-.037m.055 4.152l-.27-.362l-.032-.048c-.115-.19-.243-.38-.382-.585l-.1-.149a10 10 0 0 1-.512-.828c-.31-.578-.558-1.286-.358-2.067c.17-.664.698-1.081 1.227-1.254c.517-.168 1.174-.147 1.66.247c.792.644.848 1.573.739 2.357a9 9 0 0 1-.261 1.174l-.096.34q-.112.382-.208.769l-.067.194l1.392 1.864c.65-.078 1.364-.125 2.03-.077c.769.054 1.595.242 2.158.776a1.56 1.56 0 0 1 .395 1.441c-.117.48-.454.88-.919 1.123c-.985.515-1.902.105-2.583-.416c-.533-.407-1.045-.975-1.476-1.453l-.104-.114c-.37.057-.72.121-1.004.175c-.305.057-.684.128-1.096.22l-.151.443q-.125.288-.238.58l-.122.303a8 8 0 0 1-.427.91c-.33.578-.857 1.192-1.741 1.241c-1.184.066-1.986-.985-1.756-2.108l.006-.027c.2-.791.894-1.31 1.565-1.653c.597-.306 1.294-.532 1.941-.701zm.87 1.165l-.287.843l.421-.08l.004-.001l.38-.07zm2.84 1.604c.274.29.547.56.831.777c.55.42.94.493 1.299.305c.2-.105.284-.241.309-.342a.35.35 0 0 0-.08-.309c-.257-.228-.722-.38-1.392-.428a8 8 0 0 0-.967-.003m-5.005.947c-.318.109-.62.23-.89.368c-.587.3-.87.604-.944.867c-.078.415.192.673.516.655c.27-.015.506-.184.766-.639q.204-.372.358-.767l.107-.266z\" clip-rule=\"evenodd\"/></g>"}, "pdf-2": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-width=\"1.5\" d=\"M7.792 21.25h8.416a3.5 3.5 0 0 0 3.5-3.5v-5.53a3.5 3.5 0 0 0-1.024-2.475l-5.969-5.97A3.5 3.5 0 0 0 10.24 2.75H7.792a3.5 3.5 0 0 0-3.5 3.5v11.5a3.5 3.5 0 0 0 3.5 3.5\"/><path stroke-width=\"1.5\" d=\"M11.688 3.11v5.66a2 2 0 0 0 2 2h5.662\"/><path d=\"M7.25 16.5v-1m0 0v-2h1a1 1 0 0 1 1 1v0a1 1 0 0 1-1 1zm4 1v-3h.5a1.5 1.5 0 0 1 0 3zm4 0v-1.25m1.5-1.75h-1.5v1.75m0 0h1.5\"/></g>"}, "pencil": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.44 5.78L4.198 16.02a2 2 0 0 0-.565 1.125l-.553 3.774l3.775-.553A2 2 0 0 0 7.98 19.8L18.22 9.56m-3.78-3.78l2.229-2.23a1.6 1.6 0 0 1 2.263 0l1.518 1.518a1.6 1.6 0 0 1 0 2.263l-2.23 2.23M14.44 5.78l3.78 3.78\"/>"}, "pencil-sparkle": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m14.44 5.78l2.229-2.23a1.6 1.6 0 0 1 2.263 0l1.518 1.518a1.6 1.6 0 0 1 0 2.263l-2.23 2.23M14.44 5.78l3.78 3.78m-3.78-3.78l-1.815 1.814m5.596 1.967L7.98 19.8a2 2 0 0 1-1.124.565l-3.775.553l.553-3.774A2 2 0 0 1 4.2 16.02l3.312-3.312\"/><path fill=\"currentColor\" d=\"M4.4 1.418a.64.64 0 0 1 1.2 0l.167.45a4 4 0 0 0 2.366 2.365l.449.166a.64.64 0 0 1 0 1.202l-.45.166a4 4 0 0 0-2.365 2.366l-.166.449a.64.64 0 0 1-1.202 0l-.166-.45a4 4 0 0 0-2.366-2.365l-.449-.166a.64.64 0 0 1 0-1.202l.45-.166a4 4 0 0 0 2.365-2.366zm4.724 5.843a.4.4 0 0 1 .752 0l.103.281a2.5 2.5 0 0 0 1.479 1.479l.28.103a.4.4 0 0 1 0 .752l-.28.103a2.5 2.5 0 0 0-1.479 1.479l-.103.28a.4.4 0 0 1-.752 0l-.103-.28a2.5 2.5 0 0 0-1.479-1.479l-.28-.103a.4.4 0 0 1 0-.752l.28-.103a2.5 2.5 0 0 0 1.479-1.479z\"/></g>"}, "pentagon": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.13 3.655a3.18 3.18 0 0 1 3.74 0l6.069 4.409a3.18 3.18 0 0 1 1.155 3.557l-2.318 7.134a3.18 3.18 0 0 1-3.025 2.198H8.249a3.18 3.18 0 0 1-3.025-2.198L2.906 11.62A3.18 3.18 0 0 1 4.06 8.063z\"/>"}, "person": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19 20.75a1 1 0 0 0 1-1v-1.246c.004-2.806-3.974-5.004-8-5.004s-8 2.198-8 5.004v1.246a1 1 0 0 0 1 1zM15.604 6.854a3.604 3.604 0 1 1-7.208 0a3.604 3.604 0 0 1 7.208 0\"/>"}, "person-2": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"8.196\" r=\"4.446\"/><path d=\"M19.608 20.25a7.608 7.608 0 0 0-15.216 0\"/></g>"}, "person-add": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.104 6.854a3.604 3.604 0 1 1-7.208 0a3.604 3.604 0 0 1 7.208 0M10.87 20.75H3.5a1 1 0 0 1-1-1v-1.246c0-2.806 3.974-5.004 8-5.004q.387 0 .77.027\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.494a.5.5 0 0 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "person-add-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M19.25 1.75v6m-3-3h6m-11.814 8.814a3.907 3.907 0 1 0 0-7.814a3.907 3.907 0 0 0 0 7.814m0 0a6.686 6.686 0 0 1 6.685 6.686m-6.685-6.686A6.686 6.686 0 0 0 3.75 20.25\"/>"}, "person-circle": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 13.826a3.506 3.506 0 1 0 0-7.013a3.506 3.506 0 0 0 0 7.013m0 0a6 6 0 0 1 5.953 5.254M12 13.826a6 6 0 0 0-5.953 5.254m0 0A9.2 9.2 0 0 0 12 21.25a9.2 9.2 0 0 0 5.953-2.17m-11.906 0a9.25 9.25 0 1 1 11.907 0\"/>"}, "person-multiple": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M13.322 7.209c0 .749-.237 1.442-.64 2.009a3.42 3.42 0 0 1-2.796 1.45c-1.898 0-3.436-1.55-3.436-3.46S7.988 3.75 9.886 3.75a3.445 3.445 0 0 1 3.436 3.459M2.75 18.107c0-2.677 3.545-4.774 7.136-4.774c1.432 0 2.857.333 4.053.904c1.803.86 3.084 2.26 3.082 3.87v1.143a1 1 0 0 1-1 1H3.75a1 1 0 0 1-1-1zM15.172 3.75a3.445 3.445 0 0 1 3.435 3.459c0 .749-.236 1.442-.639 2.009a3.42 3.42 0 0 1-2.796 1.45m3.452 2.569c1.536.86 2.628 2.763 2.626 4.373v2.64\"/>"}, "phone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"12.5\" height=\"18.5\" x=\"5.75\" y=\"2.75\" rx=\"3\"/><path d=\"M11 17.75h2\"/></g>"}, "phone-accept": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" d=\"M8.14 15.733c2.158 2.158 4.278 3.28 5.89 3.864c1.768.64 3.606-.117 4.935-1.446l.459-.458a1.5 1.5 0 0 0 0-2.122l-1.149-1.149a1.5 1.5 0 0 0-2.121 0l-.387.387a2 2 0 0 1-2.828 0l-3.713-3.712a2 2 0 0 1 0-2.829l.387-.387a1.5 1.5 0 0 0 0-2.12l-1.15-1.15a1.5 1.5 0 0 0-2.12 0l-.572.572c-1.262 1.262-2.013 2.99-1.438 4.68c.538 1.58 1.622 3.685 3.806 5.87Z\"/>", "hidden": true}, "phone-hang-up": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" d=\"M12.116 7.953c-3.053 0-5.346.706-6.899 1.433c-1.702.797-2.467 2.632-2.467 4.512v.649a1.5 1.5 0 0 0 1.5 1.5h1.625a1.5 1.5 0 0 0 1.5-1.5V14a2 2 0 0 1 2-2h5.25a2 2 0 0 1 2 2v.547a1.5 1.5 0 0 0 1.5 1.5h1.625a1.5 1.5 0 0 0 1.5-1.5v-.81c0-1.784-.691-3.537-2.293-4.325c-1.496-.736-3.752-1.459-6.841-1.459Z\"/>", "hidden": true}, "photo": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.33 17.657c.11-.366.17-.755.17-1.157v-9a4 4 0 0 0-4-4h-9a4 4 0 0 0-4 4v9.07m16.83 1.087l-.088-.104l-2.466-2.976a2 2 0 0 0-3.073-.008l-1.312 1.566l-.214.261m7.153 1.26a4 4 0 0 1-3.713 2.842m0 0l-.117.002h-9a4 4 0 0 1-4-3.93m13.117 3.928l-.093-.106l-3.347-3.996m-9.676.175l.177-.201l3.206-3.827a2 2 0 0 1 3.066 0l3.227 3.853\"/><circle cx=\"15.091\" cy=\"8.909\" r=\"1.5\" fill=\"currentColor\"/></g>"}, "photo-add": {"body": "<g fill=\"none\"><circle cx=\"15.091\" cy=\"8.909\" r=\"1.5\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.5 16.57a4 4 0 0 0 4 3.93h3.48M3.5 16.57V7.5a4 4 0 0 1 4-4h9a4 4 0 0 1 4 4v3.48m-17 5.59l.178-.2l3.206-3.827a2 2 0 0 1 3.066 0l1.242 1.482\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.494a.5.5 0 0 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "photo-filter": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"8.73\" r=\"5.98\"/><circle cx=\"8.729\" cy=\"15.27\" r=\"5.98\"/><circle cx=\"15.271\" cy=\"15.27\" r=\"5.98\"/></g>"}, "photo-multiple": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m17.603 18.25l.107-.002l-.085-.096l-3.052-3.644m3.03 3.742h.647a3 3 0 0 0 2.952-2.465m-3.599 2.465H8.75a3 3 0 0 1-3-3v-.583m8.824-.159l-2.943-3.513a1.823 1.823 0 0 0-2.796 0l-3.084 3.672m8.822-.159l.195-.237l1.196-1.429a1.823 1.823 0 0 1 2.803.008l2.435 2.935M5.751 14.667V5.75a3 3 0 0 1 3-3h9.499a3 3 0 0 1 3 3v9.5q0 .274-.048.535\"/><circle cx=\"16.318\" cy=\"7.682\" r=\"1.368\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M2.75 6.75v8.5a6 6 0 0 0 6 6h8.5\"/></g>"}, "photo-sparkle": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M13.177 3.5H16.5a4 4 0 0 1 4 4v9c0 .402-.06.79-.17 1.157M3.5 13.875v2.695m16.83 1.087l-.088-.104l-2.466-2.976a2 2 0 0 0-3.073-.008l-1.312 1.566l-.214.261m7.153 1.26a4 4 0 0 1-3.713 2.842m0 0l-.117.002h-9a4 4 0 0 1-4-3.93m13.117 3.928l-.093-.106l-3.347-3.996m-9.676.175l.177-.201l3.206-3.827a2 2 0 0 1 3.066 0l3.227 3.853\"/><circle cx=\"15.091\" cy=\"8.909\" r=\"1.5\" fill=\"currentColor\"/><path fill=\"currentColor\" d=\"M8.6 1.418a.64.64 0 0 0-1.2 0l-.167.45a4 4 0 0 1-2.366 2.365l-.449.166a.64.64 0 0 0 0 1.202l.45.166a4 4 0 0 1 2.365 2.366l.166.449a.64.64 0 0 0 1.202 0l.166-.45a4 4 0 0 1 2.366-2.365l.449-.166a.64.64 0 0 0 0-1.202l-.45-.166a4 4 0 0 1-2.365-2.366zM3.876 7.261a.4.4 0 0 0-.752 0l-.103.281a2.5 2.5 0 0 1-1.479 1.479l-.28.103a.4.4 0 0 0 0 .752l.28.103a2.5 2.5 0 0 1 1.479 1.479l.103.28a.4.4 0 0 0 .752 0l.103-.28a2.5 2.5 0 0 1 1.479-1.479l.28-.103a.4.4 0 0 0 0-.752l-.28-.103a2.5 2.5 0 0 1-1.479-1.479z\"/></g>"}, "picture-in-picture": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.25 18.25h-3.5a3 3 0 0 1-3-3v-8.5a3 3 0 0 1 3-3h12.5a3 3 0 0 1 3 3v3.5\"/><rect width=\"12\" height=\"10\" x=\"11\" y=\"12\" fill=\"currentColor\" rx=\"2\"/></g>"}, "picture-in-picture-2": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18.25 4H5.75a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h12.5a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3\"/><rect width=\"8.5\" height=\"7.083\" x=\"10.25\" y=\"10.42\" fill=\"currentColor\" rx=\"1.5\"/></g>"}, "picture-in-picture-enter": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.25 18.25h-3.5a3 3 0 0 1-3-3v-8.5a3 3 0 0 1 3-3h12.5a3 3 0 0 1 3 3v3.5\"/><rect width=\"12\" height=\"10\" x=\"11\" y=\"12\" fill=\"currentColor\" rx=\"2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10 7.667V10.4a.6.6 0 0 1-.176.424M6.667 11H9.4a.6.6 0 0 0 .424-.176M6 7l3 3l.824.824\"/></g>"}, "picture-in-picture-exit": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.75 5.75h3.5a3 3 0 0 1 3 3v8.5a3 3 0 0 1-3 3H5.75a3 3 0 0 1-3-3v-3.5\"/><rect width=\"12\" height=\"10\" x=\"13\" y=\"12\" fill=\"currentColor\" rx=\"2\" transform=\"rotate(180 13 12)\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18 13.667V16.4a.6.6 0 0 1-.176.424M14.667 17H17.4a.6.6 0 0 0 .424-.176M14 13l3 3l.824.824\"/></g>"}, "pie-chart": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\"/><path d=\"M12 2.75a9.25 9.25 0 1 0 8.01 4.625L12 12z\"/><path d=\"M12 2.75a9.25 9.25 0 1 0 8.01 13.875L12 12z\"/></g>"}, "pin": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m7.77 16.233l-4.02 4.02M14.976 3.336l5.69 5.691a2 2 0 0 1-.698 3.282L16.595 13.6a4 4 0 0 0-2.426 2.674l-.689 2.5a1.5 1.5 0 0 1-2.507.662L4.568 13.03a1.5 1.5 0 0 1 .662-2.507l2.5-.688a4 4 0 0 0 2.673-2.427l1.291-3.372a2 2 0 0 1 3.282-.7\"/>"}, "pin-off": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path stroke-linejoin=\"round\" d=\"m7.77 16.233l-4.02 4.02m11.123-5.38c-.32.41-.562.885-.704 1.4l-.689 2.5a1.5 1.5 0 0 1-2.507.663L4.568 13.03a1.5 1.5 0 0 1 .662-2.507l2.5-.688c.515-.142.99-.384 1.4-.705m1.62-2.63l.944-2.464a2 2 0 0 1 3.282-.7l5.69 5.691a2 2 0 0 1-.698 3.282l-2.603.996\"/><path d=\"M21.25 21.25L2.75 2.75\"/></g>"}, "play": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5.5 12V5.624c0-1.974 2.18-3.17 3.844-2.108l10 6.376c1.541.983 1.541 3.233 0 4.216l-10 6.376C7.68 21.545 5.5 20.35 5.5 18.376z\"/>"}, "play-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M8.93 13.29c0 1.098 0 1.646.23 1.964c.202.277.51.456.85.492c.391.041.867-.232 1.818-.779l2.244-1.29c.957-.55 1.435-.825 1.595-1.185c.14-.313.14-.671 0-.984c-.16-.36-.639-.635-1.595-1.184l-2.244-1.291c-.951-.547-1.427-.82-1.817-.779c-.34.036-.65.215-.85.492c-.23.318-.23.866-.23 1.963z\"/></g>"}, "power": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18.54 5.46a9.25 9.25 0 1 1-13.08 0M12 3.367V12\"/>"}, "printer": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M6.75 17v1.05c0 1.12 0 1.68.218 2.108a2 2 0 0 0 .874.874c.428.218.988.218 2.108.218h4.1c1.12 0 1.68 0 2.108-.218a2 2 0 0 0 .874-.874c.218-.428.218-.988.218-2.108V17m-10.5 0v-1.05c0-1.12 0-1.68.218-2.108a2 2 0 0 1 .874-.874c.428-.218.988-.218 2.108-.218h4.1c1.12 0 1.68 0 2.108.218a2 2 0 0 1 .874.874c.218.428.218.988.218 2.108V17m-10.5 0h-.8c-1.12 0-1.68 0-2.108-.218a2 2 0 0 1-.874-.874c-.218-.428-.218-.988-.218-2.108v-3c0-1.68 0-2.52.327-3.162a3 3 0 0 1 1.311-1.311C5.03 6 5.87 6 7.55 6h8.9c1.68 0 2.52 0 3.162.327a3 3 0 0 1 1.311 1.311c.327.642.327 1.482.327 3.162v3c0 1.12 0 1.68-.218 2.108a2 2 0 0 1-.874.874C19.73 17 19.17 17 18.05 17h-.8M6.75 4.25a1.5 1.5 0 0 1 1.5-1.5h7.5a1.5 1.5 0 0 1 1.5 1.5V6H6.75z\"/>"}, "prohibited": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5.46 18.54A9.25 9.25 0 0 0 18.54 5.46M5.459 18.541A9.25 9.25 0 0 1 18.54 5.46M5.46 18.54L18.54 5.46\"/>"}, "python": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 7.5H7.5m4.5 9h4.5m0 0h1.521c.807 0 1.634-.188 2.13-.824c.531-.679 1.099-1.835 1.099-3.676c0-1.84-.568-2.997-1.098-3.676c-.497-.636-1.324-.824-2.13-.824H16.5m0 9v1.521c0 .807-.188 1.634-.824 2.13c-.679.531-1.835 1.099-3.676 1.099c-1.84 0-2.997-.568-3.676-1.098c-.636-.497-.824-1.324-.824-2.13V16.5m0-9H5.978c-.807 0-1.633.188-2.13.824c-.53.679-1.098 1.835-1.098 3.676c0 1.84.568 2.997 1.098 3.676c.497.636 1.323.824 2.13.824H7.5m0-9V5.978c0-.807.188-1.633.824-2.13c.679-.53 1.835-1.098 3.676-1.098c1.84 0 2.997.568 3.676 1.098c.636.497.824 1.323.824 2.13V7.5m-9 9V14a2 2 0 0 1 2-2h5a2 2 0 0 0 2-2V7.5\"/><path fill=\"currentColor\" d=\"M15 18.5a.75.75 0 1 1-1.5 0a.75.75 0 0 1 1.5 0m-6-13a.75.75 0 1 1 1.5 0a.75.75 0 0 1-1.5 0\"/></g>"}, "qr-code": {"body": "<g fill=\"none\"><rect width=\"7.5\" height=\"7.5\" x=\"2.75\" y=\"2.75\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"2.5\"/><rect width=\"7.5\" height=\"7.5\" x=\"13.75\" y=\"2.75\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"2.5\"/><rect width=\"7.5\" height=\"7.5\" x=\"2.75\" y=\"13.75\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"2.5\"/><rect width=\"3\" height=\"3\" x=\"5\" y=\"5\" fill=\"currentColor\" rx=\"1.5\"/><rect width=\"3\" height=\"3\" x=\"16\" y=\"5\" fill=\"currentColor\" rx=\"1.5\"/><rect width=\"3\" height=\"3\" x=\"5\" y=\"16\" fill=\"currentColor\" rx=\"1.5\"/><rect width=\"3\" height=\"3\" x=\"13\" y=\"13\" fill=\"currentColor\" rx=\"1.5\"/><rect width=\"3\" height=\"3\" x=\"16\" y=\"16\" fill=\"currentColor\" rx=\"1.5\"/><rect width=\"3\" height=\"3\" x=\"19\" y=\"19\" fill=\"currentColor\" rx=\"1.5\"/><rect width=\"3\" height=\"3\" x=\"19\" y=\"13\" fill=\"currentColor\" rx=\"1.5\"/><rect width=\"3\" height=\"3\" x=\"13\" y=\"19\" fill=\"currentColor\" rx=\"1.5\"/></g>"}, "question": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.817 8.808a4.183 4.183 0 1 1 7.518 2.526l-.133.145c-.065.07-.29.286-.363.347a4 4 0 0 1-.353.266l-1.517 1.045a2.81 2.81 0 0 0-1.215 2.315\"/><circle cx=\"11.754\" cy=\"19.141\" r=\".984\" fill=\"currentColor\"/></g>"}, "question-circle": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.301 9.709a2.699 2.699 0 1 1 4.85 1.63a4 4 0 0 1-.32.317c-.092.078-.137.11-.227.171l-.979.675a1.81 1.81 0 0 0-.784 1.493\"/><circle cx=\"11.828\" cy=\"16.74\" r=\"1\" fill=\"currentColor\"/><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/></g>"}, "quote": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m9.937 10.453l-.01.13c0 3.35-2.038 5.115-4.63 6.058m4.64-6.188a3.093 3.093 0 1 1-6.187.001a3.093 3.093 0 0 1 6.187-.001m10.313 0l-.01.13c0 3.35-2.038 5.115-4.63 6.058m4.64-6.188a3.093 3.093 0 1 1-6.187 0a3.093 3.093 0 0 1 6.187 0\"/>"}, "reactjs": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 8.013c-5.385 0-9.75 1.785-9.75 3.987s4.365 3.987 9.75 3.987s9.75-1.785 9.75-3.987S17.385 8.013 12 8.013\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.423 10.006c-2.692-4.704-6.407-7.625-8.298-6.524c-1.89 1.101-1.24 5.807 1.452 10.512c2.692 4.704 6.408 7.625 8.298 6.524s1.24-5.807-1.452-10.512\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.423 13.994c2.693-4.705 3.343-9.41 1.452-10.512c-1.89-1.1-5.606 1.82-8.298 6.524s-3.342 9.41-1.452 10.512c1.89 1.1 5.606-1.82 8.298-6.524\"/><circle cx=\"12\" cy=\"12\" r=\"1.5\" fill=\"currentColor\"/></g>"}, "record": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/><circle cx=\"12\" cy=\"12\" r=\"5\" fill=\"currentColor\"/></g>"}, "record-stop": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"/><rect width=\"8\" height=\"8\" x=\"8\" y=\"8\" fill=\"currentColor\" rx=\"2\"/></g>"}, "rectangle-wide": {"body": "<rect width=\"18.5\" height=\"14.5\" x=\"2.75\" y=\"4.75\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"4\"/>"}, "regular-expression": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><circle cx=\"6.75\" cy=\"17.25\" r=\"3\"/><path d=\"m16 3.75l.003 4.996L20.25 6.25l-4.245 2.5l4.245 2.5l-4.247-2.496L16 13.75l-.002-4.996l-4.248 2.496l4.245-2.5l-4.245-2.5l4.248 2.496z\"/></g>"}, "reverse": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M18.005 18.323c1.019.784 2.495.057 2.495-1.229V6.906c0-1.286-1.476-2.013-2.495-1.229L11.78 10.87a1.473 1.473 0 0 0 0 2.262z\"/><path d=\"M8.754 18.323c1.02.784 2.496.057 2.496-1.229V6.906c0-1.286-1.476-2.013-2.496-1.229L2.53 10.87a1.473 1.473 0 0 0 0 2.262z\"/></g>"}, "rhombus": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.775 14.475a3.5 3.5 0 0 1 0-4.95l5.75-5.75a3.5 3.5 0 0 1 4.95 0l5.75 5.75a3.5 3.5 0 0 1 0 4.95l-5.75 5.75a3.5 3.5 0 0 1-4.95 0z\"/>"}, "ribbon": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M18.41 9.16a6.4 6.4 0 0 1-2.426 5.02A6.38 6.38 0 0 1 12 15.57c-1.506 0-2.89-.52-3.984-1.388A6.41 6.41 0 1 1 18.41 9.16\"/><path d=\"M15.984 14.18v7.07L12 18.267L8.016 21.25v-7.07\"/></g>"}, "ribbon-star": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18.41 9.16a6.4 6.4 0 0 1-2.426 5.02A6.38 6.38 0 0 1 12 15.57c-1.506 0-2.89-.52-3.984-1.388A6.41 6.41 0 1 1 18.41 9.16\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.984 14.18v7.07L12 18.267L8.016 21.25v-7.07\"/><path fill=\"currentColor\" d=\"M11.455 6.74c.179-.307.268-.461.385-.513a.4.4 0 0 1 .32 0c.117.052.206.206.385.513l.488.838a1 1 0 0 0 .112.168q.045.047.106.076a1 1 0 0 0 .194.055l.947.205c.348.075.522.113.607.208c.075.083.11.193.1.304c-.014.127-.133.26-.37.525l-.646.723a1 1 0 0 0-.125.159a.4.4 0 0 0-.04.123a1 1 0 0 0 .008.202l.098.964c.036.355.054.532-.01.642a.4.4 0 0 1-.26.188c-.124.027-.287-.045-.612-.188l-.887-.391a1 1 0 0 0-.19-.07a.4.4 0 0 0-.13 0a1 1 0 0 0-.19.07l-.886.39c-.326.144-.49.216-.614.19a.4.4 0 0 1-.259-.189c-.064-.11-.046-.287-.01-.642l.098-.964c.01-.102.015-.153.008-.202a.4.4 0 0 0-.04-.123a1 1 0 0 0-.125-.159L9.27 9.12c-.238-.265-.356-.398-.37-.525a.4.4 0 0 1 .1-.304c.085-.095.259-.133.607-.208l.947-.205c.1-.022.15-.033.194-.055a.4.4 0 0 0 .106-.076a1 1 0 0 0 .112-.168z\"/></g>"}, "road-barrier": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" d=\"M5.75 15.25v6m0-18.5v3m12.5 9.5v6m0-18.5v3\"/><path d=\"M4.75 5.75a2 2 0 0 0-2 2v5.5a2 2 0 0 0 2 2h14.5a2 2 0 0 0 2-2v-5.5a2 2 0 0 0-2-2z\"/><path stroke-linecap=\"round\" d=\"m8.917 5.75l-5.701 8.783M15.087 5.75l-6.167 9.5m6.17 0l5.698-8.778\"/></g>"}, "road-cone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 21.25h18.5\"/><path stroke-linejoin=\"round\" d=\"m4.75 21.25l1.502-4.625M19.25 21.25l-1.502-4.625M7.754 12l-1.502 4.625M7.754 12l1.501-4.625l1.278-3.934a1 1 0 0 1 .95-.691h1.033a1 1 0 0 1 .951.691l1.278 3.934L16.246 12m-8.492 0h8.492m-9.994 4.625h11.496M16.246 12l1.502 4.625\"/></g>"}, "roblox": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.02\" height=\"16.02\" x=\"6.336\" y=\"2.19\" rx=\"2.5\" transform=\"rotate(15 6.336 2.19)\"/><rect width=\"5.34\" height=\"5.34\" x=\"10.112\" y=\"8.73\" rx=\".8\" transform=\"rotate(15 10.112 8.73)\"/></g>"}, "ruler": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><rect width=\"9.5\" height=\"18.5\" x=\"6.75\" y=\"2.75\" stroke-linejoin=\"round\" rx=\"2\"/><path d=\"M6.75 12h4.5m-4.5-4.5h4.5m-4.5 9h4.5\"/></g>"}, "ruler-diagonal": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><rect width=\"9\" height=\"19.5\" x=\"15.712\" y=\"1.924\" stroke-linejoin=\"round\" rx=\"2\" transform=\"rotate(45 15.712 1.924)\"/><path d=\"M8.818 8.818L12 12m0-6.364l3.182 3.182M5.636 12l3.182 3.182\"/></g>"}, "save": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.558 3.75H7.25a3.5 3.5 0 0 0-3.5 3.5v9.827a3.173 3.173 0 0 0 3.173 3.173v0m.635-16.5v2.442a2 2 0 0 0 2 2h2.346a2 2 0 0 0 2-2V3.75m-6.346 0h6.346m0 0h.026a3 3 0 0 1 2.122.879l3.173 3.173a3.5 3.5 0 0 1 1.025 2.475v6.8a3.173 3.173 0 0 1-3.173 3.173v0m-10.154 0V15a3 3 0 0 1 3-3h4.154a3 3 0 0 1 3 3v5.25m-10.154 0h10.154\"/>"}, "save-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M9.558 2.75H8.75a3 3 0 0 0-3 3v9.5a3 3 0 0 0 3 3h.173m.635-15.5v2.942a1.5 1.5 0 0 0 1.5 1.5h2.346a1.5 1.5 0 0 0 1.5-1.5V2.75m-5.346 0h5.346m0 0h.026a3 3 0 0 1 2.122.879l3.32 3.32a3 3 0 0 1 .878 2.12v6.181a3 3 0 0 1-3 3h-.173m-9.154 0V12.5a2 2 0 0 1 2-2h5.154a2 2 0 0 1 2 2v5.75m-9.154 0h9.154\"/><path d=\"M17.25 21.25h-8.5a6 6 0 0 1-6-6v-8.5\"/></g>"}, "save-pencil": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.558 3.75H7.25a3.5 3.5 0 0 0-3.5 3.5v9.827a3.173 3.173 0 0 0 3.173 3.173m.635-16.5v2.442a2 2 0 0 0 2 2h2.346a2 2 0 0 0 2-2V3.75m-6.346 0h6.346m0 0h.026a3 3 0 0 1 2.122.879l3.173 3.173a3.5 3.5 0 0 1 .903 1.558M6.923 20.25V15a3 3 0 0 1 3-3h4.154q.353 0 .683.078M6.923 20.25H9.33\"/><path fill=\"currentColor\" d=\"M14.586 21.436a2.5 2.5 0 0 0 1.219-.674l5.454-5.45a2.526 2.526 0 1 0-3.57-3.572l-5.453 5.452a2.5 2.5 0 0 0-.674 1.222l-.536 2.354a1.007 1.007 0 0 0 1.206 1.206z\"/></g>"}, "screen-size": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"15.5\" x=\"2.75\" y=\"4.25\" rx=\"3\"/><path d=\"M6.75 12.25v-4h4m6.5 3.5v4h-4\"/></g>"}, "script": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.25 21.25H4A2.25 2.25 0 0 1 1.75 19v-1.25a1.5 1.5 0 0 1 1.5-1.5h1.5m10.5 5a2.5 2.5 0 0 0 2.5-2.5v-11m-2.5 13.5a2.5 2.5 0 0 1-2.5-2.5v-1.5a1 1 0 0 0-1-1h-7m15.376-13.5H8.25a3.5 3.5 0 0 0-3.5 3.5v10m13-8.5h3.5a1 1 0 0 0 1-1V5a2.25 2.25 0 0 0-4.5 0zm-9.25-.5h6m-6 4h4\"/>"}, "script-2": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.25 21.25H4A2.25 2.25 0 0 1 1.75 19v-1.25a1.5 1.5 0 0 1 1.5-1.5h1.5m10.5 5a2.5 2.5 0 0 0 2.5-2.5v-11m-2.5 13.5a2.5 2.5 0 0 1-2.5-2.5v-1.5a1 1 0 0 0-1-1h-7m15.376-13.5H8.25a3.5 3.5 0 0 0-3.5 3.5v10m13-8.5h3.5a1 1 0 0 0 1-1V5a2.25 2.25 0 0 0-4.5 0z\"/>"}, "search": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.553 15.553a7.06 7.06 0 1 0-9.985-9.985a7.06 7.06 0 0 0 9.985 9.985m0 0L20 20\"/>"}, "search-cancel": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m8.44 12.682l2.12-2.121m0 0l2.122-2.122m-2.121 2.122l2.12 2.12m-2.12-2.12L8.439 8.439m7.114 7.114a7.06 7.06 0 1 0-9.985-9.985a7.06 7.06 0 0 0 9.985 9.985m0 0L20 20\"/>"}, "section-break": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 12h18.5M4 2.75V5.5a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2.75M4 21.25V18.5a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v2.75\"/>"}, "send": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.76 12H6.832m0 0c0-.275-.057-.55-.17-.808L4.285 5.814c-.76-1.72 1.058-3.442 2.734-2.591L20.8 10.217c1.46.74 1.46 2.826 0 3.566L7.02 20.777c-1.677.851-3.495-.872-2.735-2.591l2.375-5.378A2 2 0 0 0 6.83 12\"/>"}, "server": {"body": "<g fill=\"none\"><rect width=\"18.5\" height=\"7.5\" x=\"2.75\" y=\"2.751\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"2\"/><circle cx=\"6.25\" cy=\"6.501\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"10.25\" cy=\"6.501\" r=\"1.25\" fill=\"currentColor\"/><rect width=\"18.5\" height=\"7.5\" x=\"2.75\" y=\"13.749\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"2\"/><circle cx=\"6.25\" cy=\"17.499\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"10.25\" cy=\"17.499\" r=\"1.25\" fill=\"currentColor\"/></g>"}, "settings": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.05 6.462a2 2 0 0 0 2.63-1.519l.32-1.72a9 9 0 0 1 3.998 0l.322 1.72a2 2 0 0 0 2.63 1.519l1.649-.58a9 9 0 0 1 2.001 3.46l-1.33 1.14a2 2 0 0 0 0 3.037l1.33 1.139a9 9 0 0 1-2.001 3.46l-1.65-.58a2 2 0 0 0-2.63 1.519L14 20.777a9 9 0 0 1-3.998 0l-.322-1.72a2 2 0 0 0-2.63-1.519l-1.649.58a9 9 0 0 1-2.001-3.46l1.33-1.14a2 2 0 0 0 0-3.036L3.4 9.342a9 9 0 0 1 2-3.46zM12 9a3 3 0 1 1 0 6a3 3 0 0 1 0-6\" clip-rule=\"evenodd\"/>"}, "shape-difference": {"body": "<g fill=\"currentColor\"><path d=\"M1.998 5.75A3.75 3.75 0 0 1 5.748 2h7a3.75 3.75 0 0 1 3.75 3.75V7.5h-5.246a3.75 3.75 0 0 0-3.75 3.75v5.25H5.748a3.75 3.75 0 0 1-3.75-3.75z\"/><path d=\"M7.502 16.5h5.246a3.75 3.75 0 0 0 3.75-3.75V7.5h1.754a3.75 3.75 0 0 1 3.75 3.75v7a3.75 3.75 0 0 1-3.75 3.75h-7a3.75 3.75 0 0 1-3.75-3.75z\"/></g>"}, "shape-intersect": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M12.748 15.75a3 3 0 0 0 3-3v-4.5h-4.496a3 3 0 0 0-3 3v4.5z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.252 15.75v2.5a3 3 0 0 0 3 3h7a3 3 0 0 0 3-3v-7a3 3 0 0 0-3-3h-2.504m-7.496 7.5v-4.5a3 3 0 0 1 3-3h4.496m-7.496 7.5h4.496a3 3 0 0 0 3-3v-4.5m-7.496 7.5H5.748a3 3 0 0 1-3-3v-7a3 3 0 0 1 3-3h7a3 3 0 0 1 3 3v2.5\"/></g>"}, "shape-subtract": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M12.748 2.75h-7a3 3 0 0 0-3 3v7a3 3 0 0 0 3 3h2.504v-4.5a3 3 0 0 1 3-3h4.496v-2.5a3 3 0 0 0-3-3\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.252 15.75v2.5a3 3 0 0 0 3 3h7a3 3 0 0 0 3-3v-7a3 3 0 0 0-3-3h-2.504m-7.496 7.5v-4.5a3 3 0 0 1 3-3h4.496m-7.496 7.5H5.748a3 3 0 0 1-3-3v-7a3 3 0 0 1 3-3h7a3 3 0 0 1 3 3v2.5\"/></g>"}, "shape-union": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M2.748 5.75a3 3 0 0 1 3-3h7a3 3 0 0 1 3 3v7a3 3 0 0 1-3 3h-7a3 3 0 0 1-3-3z\"/><path fill=\"currentColor\" d=\"M8.252 11.25a3 3 0 0 1 3-3h7a3 3 0 0 1 3 3v7a3 3 0 0 1-3 3h-7a3 3 0 0 1-3-3z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.748 5.75a3 3 0 0 1 3-3h7a3 3 0 0 1 3 3v7a3 3 0 0 1-3 3h-7a3 3 0 0 1-3-3z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M8.252 11.25a3 3 0 0 1 3-3h7a3 3 0 0 1 3 3v7a3 3 0 0 1-3 3h-7a3 3 0 0 1-3-3z\"/></g>"}, "shield": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.25 10.907V7.272c0-.829-.633-1.521-1.453-1.644c-.951-.142-2.18-.376-3.078-.722c-.907-.349-1.997-1.007-2.762-1.505a1.76 1.76 0 0 0-1.914 0c-.764.498-1.855 1.156-2.762 1.505c-.899.346-2.127.58-3.078.722c-.82.123-1.453.815-1.453 1.644v3.635a10.13 10.13 0 0 0 5.363 8.939l.23.123l1.962.946a1.6 1.6 0 0 0 1.39 0l1.961-.946l.23-.123a10.13 10.13 0 0 0 5.364-8.939\"/>"}, "shield-cancel": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.25 10.907V7.272c0-.829-.633-1.521-1.453-1.644c-.951-.142-2.18-.376-3.078-.722c-.907-.349-1.997-1.007-2.762-1.505a1.76 1.76 0 0 0-1.914 0c-.764.498-1.855 1.156-2.762 1.505c-.899.346-2.127.58-3.078.722c-.82.123-1.453.815-1.453 1.644v3.635a10.13 10.13 0 0 0 5.363 8.939l.23.123l1.962.946a1.6 1.6 0 0 0 1.39 0l1.961-.946l.23-.123a10.13 10.13 0 0 0 5.364-8.939M9.5 9.5l5 5m0-5l-5 5\"/>"}, "shield-checkmark": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M20.25 10.907V7.272c0-.829-.633-1.521-1.453-1.644c-.951-.142-2.18-.376-3.078-.722c-.907-.349-1.997-1.007-2.762-1.505a1.76 1.76 0 0 0-1.914 0c-.764.498-1.855 1.156-2.762 1.505c-.899.346-2.127.58-3.078.722c-.82.123-1.453.815-1.453 1.644v3.635a10.13 10.13 0 0 0 5.363 8.939l.23.123l1.962.946a1.6 1.6 0 0 0 1.39 0l1.961-.946l.23-.123a10.13 10.13 0 0 0 5.364-8.939\"/><path d=\"m15.509 10l-4.076 4.076a.6.6 0 0 1-.849 0l-2.093-2.09\"/></g>"}, "shield-keyhole": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.25 10.907V7.272c0-.829-.633-1.521-1.453-1.644c-.951-.142-2.18-.376-3.078-.722c-.907-.349-1.997-1.007-2.762-1.505a1.76 1.76 0 0 0-1.914 0c-.764.498-1.855 1.156-2.762 1.505c-.899.346-2.127.58-3.078.722c-.82.123-1.453.815-1.453 1.644v3.635a10.13 10.13 0 0 0 5.363 8.939l.23.123l1.962.946a1.6 1.6 0 0 0 1.39 0l1.961-.946l.23-.123a10.13 10.13 0 0 0 5.364-8.939\"/><circle cx=\"12\" cy=\"10.5\" r=\"2\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 14.75v-2.5\"/></g>"}, "skull": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M17.99 17.284c2.225-1.528 3.26-3.442 3.26-6.164c0-4.62-4.141-8.37-9.25-8.37S2.75 6.5 2.75 11.12c0 2.722 1.437 4.636 3.663 6.164c-.175.61-.132 2.251.187 2.78c.414.687 1.219 1.186 1.87 1.186c.743 0 1.396-.474 1.765-1.186c.37.712 1.022 1.186 1.765 1.186s1.396-.473 1.765-1.186c.37.713 1.022 1.186 1.765 1.186c.651 0 1.454-.499 1.94-1.186c.374-.529.52-2.17.52-2.78\"/><path fill=\"currentColor\" d=\"M5.75 10.25a2.5 2.5 0 0 1 5 0v1a1.5 1.5 0 0 1-1.5 1.5h-1a2.5 2.5 0 0 1-2.5-2.5m7.5 0a2.5 2.5 0 1 1 2.5 2.5h-1a1.5 1.5 0 0 1-1.5-1.5zm-1.773 2.93l-1.224 2.176a.6.6 0 0 0 .523.894h2.448a.6.6 0 0 0 .523-.894l-1.224-2.176a.6.6 0 0 0-1.046 0\"/></g>"}, "slash-square": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"4\"/><path d=\"m14.5 7l-5 10\"/></g>"}, "soundwave": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 20.75V3.25m8 11.71V9.04M4 14.96V9.04m12 8.872V6.088M8 17.912V6.088\"/>"}, "spacebar": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 10v2.5a2 2 0 0 0 2 2h12.5a2 2 0 0 0 2-2V10\"/>"}, "sparkle": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M11.256 3.567c.266-.675 1.222-.675 1.488 0l2.047 5.19a.8.8 0 0 0 .451.452l5.191 2.047c.675.266.675 1.222 0 1.488l-5.19 2.047a.8.8 0 0 0-.452.451l-2.047 5.191c-.266.675-1.222.675-1.488 0l-2.047-5.19a.8.8 0 0 0-.451-.452l-5.191-2.047c-.675-.266-.675-1.222 0-1.488l5.19-2.047a.8.8 0 0 0 .452-.451z\"/><circle cx=\"5.25\" cy=\"5.25\" r=\"1.25\" fill=\"currentColor\"/><circle cx=\"19\" cy=\"19\" r=\"1\" fill=\"currentColor\"/></g>"}, "sparkle-2": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path fill=\"currentColor\" d=\"m17.503 14.751l.306.777c.3.763.904 1.366 1.666 1.667l.777.306l-.777.307c-.762.3-1.365.904-1.666 1.666l-.306.777l-.307-.777a2.96 2.96 0 0 0-1.666-1.666l-.777-.307l.777-.306a2.96 2.96 0 0 0 1.666-1.667z\"/><path d=\"M9.61 3.976c.08-.296.5-.296.58 0l.154.572a6.96 6.96 0 0 0 4.908 4.908l.572.154c.296.08.296.5 0 .58l-.572.154a6.96 6.96 0 0 0-4.908 4.908l-.154.572c-.08.296-.5.296-.58 0l-.154-.572a6.96 6.96 0 0 0-4.908-4.908l-.572-.154c-.296-.08-.296-.5 0-.58l.572-.154a6.96 6.96 0 0 0 4.908-4.908z\"/></g>"}, "spinner": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 21.25A9.25 9.25 0 1 0 2.75 12\"/>"}, "split-horizontal": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.25 7.25a3.5 3.5 0 0 0-3.5-3.5h-9.5a3.5 3.5 0 0 0-3.5 3.5v9.5a3.5 3.5 0 0 0 3.5 3.5h9.5a3.5 3.5 0 0 0 3.5-3.5zM12 3.75v16.5\"/>"}, "split-vertical": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.25 3.75a3.5 3.5 0 0 0-3.5 3.5v9.5a3.5 3.5 0 0 0 3.5 3.5h9.5a3.5 3.5 0 0 0 3.5-3.5v-9.5a3.5 3.5 0 0 0-3.5-3.5zM3.75 12h16.5\"/>"}, "square": {"body": "<rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" rx=\"4\"/>"}, "square-drag": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12.073 17.533v-3.864m2.544-2.937c0-.699-.57-1.265-1.272-1.265s-1.272.566-1.272 1.265v2.937m2.544-.444v-4.21c0-.698.57-1.265 1.272-1.265s1.272.567 1.272 1.266v1.716m0 0v2.493m0-2.493a1.273 1.273 0 0 1 2.545 0v1.717m0 0v.776m0-.776c0-.7.57-1.266 1.272-1.266s1.272.567 1.272 1.266V17.1a5.15 5.15 0 0 1-5.15 5.15h-2.31a5.55 5.55 0 0 1-5.541-5.872l.012-.201a2.4 2.4 0 0 1 1.67-2.146l1.142-.362\"/><path d=\"M6.75 18.25a3 3 0 0 1-3-3v-8.5a3 3 0 0 1 3-3h8.5a3 3 0 0 1 2.959 2.5\"/></g>"}, "square-margins": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"3\"/><path d=\"M7.75 3.75v16.5m8.5 0V3.75m-12.5 4h16.5m0 8.5H3.75\"/></g>"}, "star": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.704 4.325a1.5 1.5 0 0 1 2.592 0l1.818 3.12a1.5 1.5 0 0 0 .978.712l3.53.764a1.5 1.5 0 0 1 .8 2.465l-2.405 2.693a1.5 1.5 0 0 0-.374 1.15l.363 3.593a1.5 1.5 0 0 1-2.097 1.524l-3.304-1.456a1.5 1.5 0 0 0-1.21 0l-3.304 1.456a1.5 1.5 0 0 1-2.097-1.524l.363-3.593a1.5 1.5 0 0 0-.373-1.15l-2.406-2.693a1.5 1.5 0 0 1 .8-2.465l3.53-.764a1.5 1.5 0 0 0 .979-.711z\"/>"}, "stroke-thickness": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 4.5h18.5M20.5 10h-17a.75.75 0 0 0 0 1.5h17a.75.75 0 0 0 0-1.5\"/><path fill=\"currentColor\" d=\"M19.75 17H4.25a1.5 1.5 0 0 0 0 3h15.5a1.5 1.5 0 0 0 0-3\"/></g>"}, "subtract": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 12h16.5\"/>"}, "subtract-square": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M8.25 12h7.5\"/><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"4\"/></g>"}, "subtract-square-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M7.38 10.63h6.5\"/><rect width=\"14\" height=\"14\" x=\"3.63\" y=\"3.63\" rx=\"3\"/><path d=\"M20.63 7.63v7a6 6 0 0 1-6 6h-7\"/></g>"}, "svelte": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M18.878 10.881a5.63 5.63 0 0 1-2.181 7.44l-3.757 2.175a5.596 5.596 0 0 1-7.656-2.057a5.63 5.63 0 0 1-.162-5.32a5.63 5.63 0 0 1 2.181-7.44l3.757-2.175a5.596 5.596 0 0 1 7.656 2.057a5.63 5.63 0 0 1 .162 5.32\"/><path d=\"M16.011 8.912a2.2 2.2 0 0 0-.228-1.653a2.195 2.195 0 0 0-3-.813l-3.8 2.2a2.207 2.207 0 0 0-.798 3.012a2.195 2.195 0 0 0 3.001.813l1.628-.942a2.195 2.195 0 0 1 3 .813a2.207 2.207 0 0 1-.797 3.012l-3.8 2.2a2.195 2.195 0 0 1-3-.813a2.2 2.2 0 0 1-.228-1.653\"/></g>"}, "symbols": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M13.9 3.918a1.955 1.955 0 0 1 2.765 0l.624.624l.624-.624a1.955 1.955 0 1 1 2.764 2.765l-.623.624l.005.005l-2.764 2.765l-.006-.006l-.006.006l-2.764-2.765l.005-.005l-.623-.624a1.955 1.955 0 0 1 0-2.765Z\"/><path stroke-linecap=\"round\" d=\"M3.736 9.091a3.366 3.366 0 1 0 4.76-4.76m-4.76 4.76a3.366 3.366 0 1 1 4.76-4.76m-4.76 4.76l4.76-4.76M2.75 17.289h3.366m0 0H9.48m-3.365 0v-3.366m0 3.366v3.366m7.328-4.237a3.365 3.365 0 0 1 6.165-.812l.189.297m.262-1.98v1.98h-.262m-1.718 0h1.718m-6.47 4.752v-1.98h.267m1.714 0h-1.714m6.35-.515a3.366 3.366 0 0 1-6.165.812l-.185-.297\"/></g>"}, "table": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"16.5\" x=\"2.75\" y=\"3.75\" rx=\"3\"/><path d=\"M2.75 7.75h18.5M2.75 14h18.5M8.92 7.75v12.5m6.17-12.5v12.5\"/></g>"}, "table-simple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"3\"/><path d=\"M3.75 9.25h16.5m-16.5 5.5h16.5m-11-11v16.5m5.5-16.5v16.5\"/></g>"}, "tablet": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"15\" x=\"2.75\" y=\"4.5\" rx=\"3\"/><path d=\"M10 16h4\"/></g>"}, "tag": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.162 10.926L13.716 4.48a2.5 2.5 0 0 0-1.767-.732h-5.2a3 3 0 0 0-3 3v5.2a2.5 2.5 0 0 0 .731 1.768l6.445 6.446a4 4 0 0 0 5.657 0l1.79-1.79l1.79-1.79a4 4 0 0 0 0-5.657\"/><circle cx=\"7.738\" cy=\"7.738\" r=\"1.277\" fill=\"currentColor\" transform=\"rotate(-45 7.738 7.738)\"/></g>"}, "tag-accent": {"body": "<path fill=\"currentColor\" d=\"M20.162 10.925L13.716 4.48a2.5 2.5 0 0 0-1.767-.732h-5.2a3 3 0 0 0-3 3v5.2a2.5 2.5 0 0 0 .731 1.768l6.445 6.446a4 4 0 0 0 5.657 0l3.58-3.58a4 4 0 0 0 0-5.657\"/>"}, "tag-add": {"body": "<g fill=\"none\"><circle cx=\"8.658\" cy=\"6.738\" r=\"1.277\" fill=\"currentColor\" transform=\"rotate(-45 8.658 6.738)\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.668 10.5V5.748a3 3 0 0 1 3-3h5.2a2.5 2.5 0 0 1 1.768.732l6.445 6.446a4 4 0 0 1 0 5.656l-3.579 3.58a4 4 0 0 1-4.172.94\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H7v2.493a.5.5 0 1 1-1 0V18H3.507a.5.5 0 0 1 0-1H6v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "tag-multiple": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m18.828 9.629l-5.48-5.492a3.02 3.02 0 0 0-2.196-.886l-4.324.086a2.52 2.52 0 0 0-2.467 2.472l-.086 4.334a3.03 3.03 0 0 0 .884 2.2l5.48 5.493a3.016 3.016 0 0 0 4.273 0l3.916-3.925a3.03 3.03 0 0 0 0-4.282\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m4.25 15.714l4.254 4.262a6.03 6.03 0 0 0 8.544 0l3.202-3.209\"/><path fill=\"currentColor\" d=\"M7.967 5.798a1.15 1.15 0 1 1 .002 2.298a1.15 1.15 0 0 1-.002-2.298\"/></g>"}, "tag-multiple-var": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m19.14 10.015l-6.209-6.21a2 2 0 0 0-1.414-.585H6.115a2 2 0 0 0-2 2v5.402a2 2 0 0 0 .586 1.414l6.209 6.209a3 3 0 0 0 4.243 0l3.987-3.988a3 3 0 0 0 0-4.242\"/><circle cx=\"8.562\" cy=\"7.667\" r=\"1.138\" fill=\"currentColor\" transform=\"rotate(-45 8.562 7.667)\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m3.701 14.456l5.795 5.794a5 5 0 0 0 7.07 0l3.696-3.694\"/></g>", "hidden": true}, "tag-remove": {"body": "<g fill=\"none\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m-2.352-7.852a.5.5 0 0 1 .707 0L6.5 16.793l1.645-1.645a.5.5 0 1 1 .707.707L7.207 17.5l1.645 1.645a.5.5 0 1 1-.707.707L6.5 18.207l-1.645 1.645a.5.5 0 1 1-.707-.707L5.793 17.5l-1.645-1.645a.5.5 0 0 1 0-.707\" clip-rule=\"evenodd\"/><circle cx=\"8.658\" cy=\"6.738\" r=\"1.277\" fill=\"currentColor\" transform=\"rotate(-45 8.658 6.738)\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.668 10.5V5.748a3 3 0 0 1 3-3h5.2a2.5 2.5 0 0 1 1.768.732l6.445 6.446a4 4 0 0 1 0 5.656l-3.579 3.58a4 4 0 0 1-4.172.94\"/></g>"}, "target": {"body": "<g fill=\"none\"><circle cx=\"12\" cy=\"12\" r=\"9.25\" stroke=\"currentColor\" stroke-width=\"1.5\"/><circle cx=\"12\" cy=\"12\" r=\"5.25\" stroke=\"currentColor\" stroke-width=\"1.5\"/><circle cx=\"12\" cy=\"12\" r=\"2\" fill=\"currentColor\"/></g>"}, "task-list": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.25 4.5h11m-14-1.446L4.357 5.946L2.75 4.34m7.5 7.66h11m-14-1.446l-2.893 2.892L2.75 11.84m7.5 7.66h11m-14-1.446l-2.893 2.892L2.75 19.34\"/>"}, "terminal": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"15.5\" x=\"2.75\" y=\"4.25\" rx=\"3.5\"/><path d=\"m7.25 9l3 3l-3 3m5.5 0h4\"/></g>"}, "text": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m19.25 8.389l-.62-1.235A3 3 0 0 0 15.95 5.5h-7.9a3 3 0 0 0-2.68 1.654L4.75 8.39M12 5.5v13m0 0h-1.45m1.45 0h1.45\"/>"}, "text-add": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m17.25 8.389l-.62-1.235A3 3 0 0 0 13.95 5.5h-7.9a3 3 0 0 0-2.68 1.654L2.75 8.39M10 5.5v13H8.55\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 1 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "text-align-center": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M5.25 5.25h13.5M2.75 12h18.5M7 18.75h10\"/>"}, "text-align-justify": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M2.75 5.25h18.5M2.75 12h18.5m-18.5 6.75h18.5\"/>"}, "text-align-left": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M2.75 5.25h13.5M2.75 12h18.5m-18.5 6.75h10\"/>"}, "text-align-right": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M7.75 5.25h13.5M2.75 12h18.5m-10 6.75h10\"/>"}, "text-bold": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M6.75 6.5c0-.966.784-1.75 1.75-1.75h3.551c2.135 0 3.849 1.75 3.849 3.888a3.9 3.9 0 0 1-1.114 2.735a4.11 4.11 0 0 1 2.464 3.765a4.12 4.12 0 0 1-4.125 4.112H8.5a1.75 1.75 0 0 1-1.75-1.75zm1.5 6.026V17.5c0 .*************.25h4.625a2.62 2.62 0 0 0 2.625-2.612a2.62 2.62 0 0 0-2.625-2.612zm0-1.5h3.801c1.289 0 2.349-1.06 2.349-2.388S13.34 6.25 12.051 6.25H8.5a.25.25 0 0 0-.25.25z\" clip-rule=\"evenodd\"/>"}, "text-case-lowercase": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m9.81 12.919l-.074-.015c-3.049-.578-6.676-.49-6.676 2.555c0 .854.491 1.658 1.206 2.095c.71.434 1.604.446 2.44.446c1.15 0 2.41-.65 2.914-1.662q.146-.29.19-.657v-1.167m0-1.595v-.609c0-.762-.09-1.768-.547-2.384c-.497-.67-1.38-1.426-2.828-1.426c-2.393 0-3.166 1.654-3.166 1.654M9.81 12.92v1.595m0 3.383v-3.383M13.56 6.5V18m8-4a4 4 0 1 1-8 0a4 4 0 0 1 8 0\"/>"}, "text-case-title": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m1.88 18l1.83-4.404m0 0h6.59m-6.59 0L6.515 6.85c.193-.465.787-.465.98 0l2.805 6.747m0 0L12.13 18m2.66-11.5V18m7.59-3.795a3.795 3.795 0 1 1-7.59 0a3.795 3.795 0 0 1 7.59 0\"/>"}, "text-case-uppercase": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m1.01 18l1.92-4.404m0 0h6.91m-6.91 0L5.87 6.85a.553.553 0 0 1 1.028 0l2.941 6.747m0 0L11.76 18m3-5.948h4.046a2.765 2.765 0 0 0 2.754-2.776A2.765 2.765 0 0 0 18.806 6.5H14.76zm0 0h5c1.657 0 3 1.331 3 2.974c0 1.642-1.343 2.974-3 2.974h-5z\"/>"}, "text-clear-formatting": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M14.77 22.252h6.476m-9.69-7.733l5.925 5.925m-6.195-.808l1.078 1.078c.849.848 1.273 1.272 1.762 1.431c.43.14.894.14 1.324 0c.49-.159.913-.583 1.762-1.431l3.502-3.502c.848-.849 1.272-1.273 1.431-1.762c.14-.43.14-.894 0-1.324c-.159-.49-.583-.913-1.431-1.762l-1.078-1.078c-.849-.848-1.273-1.272-1.762-1.431a2.14 2.14 0 0 0-1.324 0c-.49.159-.913.583-1.762 1.431l-3.502 3.502c-.848.849-1.272 1.273-1.431 1.762c-.14.43-.14.894 0 1.324c.159.49.583.913 1.431 1.762M2.75 12.427L4.328 8.72m0 0h5.68m-5.68 0l2.417-5.678a.45.45 0 0 1 .845 0l2.418 5.678m0 0l1.137 2.673M14.09 2.75v5.739l.112-.096a3.195 3.195 0 0 1 6.202.158\"/>"}, "text-collapse": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.125 4.5h14.5m-14.5 15h14.5m-7.5-10h7.5m-7.5 5h7.5\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.875 16.5a4.5 4.5 0 1 0 0-9a4.5 4.5 0 0 0 0 9m-2-5a.5.5 0 1 0 0 1h4a.5.5 0 0 0 0-1z\" clip-rule=\"evenodd\"/></g>"}, "text-color": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"m7.154 13.088l1.73-3.959m0 0h6.231m-6.23 0l2.652-6.065a.5.5 0 0 1 .926 0l2.652 6.065m0 0l1.731 3.96\"/><rect width=\"15.5\" height=\"4.353\" x=\"4.25\" y=\"16.897\" rx=\"1.5\"/></g>"}, "text-color-accent": {"body": "<path fill=\"currentColor\" d=\"M4.25 18.397a1.5 1.5 0 0 1 1.5-1.5h12.5a1.5 1.5 0 0 1 1.5 1.5v1.353a1.5 1.5 0 0 1-1.5 1.5H5.75a1.5 1.5 0 0 1-1.5-1.5z\"/>"}, "text-description": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M2.75 5.25h18.5M2.75 12h18.5m-18.5 6.75h13.5\"/>"}, "text-edit-style": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m3.25 14.25l1.875-4.403m0 0h6.75m-6.75 0l2.873-6.748a.536.536 0 0 1 1.004 0l2.873 6.748m0 0l.575 1.349m.886 9.49a2.5 2.5 0 0 0 1.219-.673l5.454-5.45a2.526 2.526 0 1 0-3.57-3.573l-5.453 5.452c-.335.336-.569.76-.674 1.222l-.536 2.354a1.007 1.007 0 0 0 1.206 1.206z\"/>"}, "text-effects": {"body": "<g fill=\"none\" clip-rule=\"evenodd\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 3.5a1.75 1.75 0 0 0-1.588 1.015l-6.25 13.5a1.75 1.75 0 0 0 3.176 1.47L9.1 15.678h5.8l1.762 3.807a1.75 1.75 0 1 0 3.176-1.47l-6.25-13.5A1.75 1.75 0 0 0 12 3.5\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m12 7.63l2.453 5.298H9.547z\"/></g>"}, "text-expand": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7.125 4.5h14.5m-14.5 15h14.5m-7.5-10h7.5m-7.5 5h7.5\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.875 16.5a4.5 4.5 0 1 0 0-9a4.5 4.5 0 0 0 0 9m0-7a.5.5 0 0 1 .5.5v1.5h1.5a.5.5 0 1 1 0 1h-1.5V14a.5.5 0 1 1-1 0v-1.5h-1.5a.5.5 0 0 1 0-1h1.5V10a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "text-font": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m20.08 21.25l-2.133-4.842m0 0H10.27m7.677 0l-3.268-7.42a.617.617 0 0 0-1.142 0l-3.267 7.42m0 0L8.137 21.25m-1.174 0h2.812m8.663 0h2.812M4.423 8.82L2.75 12.584M4.423 8.82h6.021L7.881 3.05a.485.485 0 0 0-.895 0z\"/>"}, "text-font-size": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m2.75 17.75l1.875-4.404m0 0h6.75m-6.75 0L7.498 6.6a.536.536 0 0 1 1.004 0l2.873 6.747m0 0l1.875 4.404m0-.001l1.429-3.277m0 0h5.142m-5.142 0l2.188-5.022a.412.412 0 0 1 .765 0l2.19 5.022m0 0l1.428 3.277\"/>"}, "text-footnote": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m2.75 17.248l1.44-3.384m0 0h5.188m-5.187 0l2.208-5.186a.412.412 0 0 1 .771 0l2.208 5.186m0 0l1.441 3.384m2.398-8.838v8.838m5.833-2.916a2.916 2.916 0 1 1-5.833 0a2.916 2.916 0 0 1 5.833 0m0-7.707l2.2-1.375v5.5\"/>"}, "text-highlight-color": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"4\"/><path d=\"m8.25 16l1.34-3.063m0 0h4.82m-4.82 0l2.051-4.694a.386.386 0 0 1 .718 0l2.052 4.694m0 0L15.75 16\"/></g>"}, "text-highlight-color-accent": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.75 3.75a4 4 0 0 0-4 4v8.5a4 4 0 0 0 4 4h8.5a4 4 0 0 0 4-4v-8.5a4 4 0 0 0-4-4zm2.986 8.437L12 9.295l1.264 2.892zm-.002 2.5h2.532l.88 2.014a1.75 1.75 0 0 0 3.207-1.402l-3.391-7.757c-.753-1.723-3.171-1.723-3.924 0l-3.391 7.757a1.75 1.75 0 0 0 3.206 1.402zm2.312-6.745l3.391 7.758a.75.75 0 1 1-1.374.6l-1.143-2.613h-3.84L8.937 16.3a.75.75 0 1 1-1.374-.6l3.391-7.758a1.136 1.136 0 0 1 2.092 0\" clip-rule=\"evenodd\"/>"}, "text-indent-decrease": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path d=\"M6.75 18.75h13.5M6.75 5.25h13.5M10.75 12h9.5\"/><path stroke-linejoin=\"round\" d=\"m6.25 9l-3 3l3 3\"/></g>"}, "text-indent-increase": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path d=\"M6.75 18.75h13.5M6.75 5.25h13.5M10.75 12h9.5\"/><path stroke-linejoin=\"round\" d=\"m3.75 9l3 3l-3 3\"/></g>"}, "text-italic": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M9.25 4.75h9m-13.5 14.5h9m-4.75 0l5.263-14.5\"/>"}, "text-large": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m5.5 19.207l2.321-5.325m0 0h8.358m-8.358 0l3.558-8.16a.669.669 0 0 1 1.242 0l3.558 8.16m0 0l2.321 5.325\"/>"}, "text-letter-spacing": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path stroke-linejoin=\"round\" d=\"m7.6 16.937l1.571-3.781m0 0h5.658m-5.658 0l2.408-5.793c.166-.4.676-.4.842 0l2.408 5.793m0 0l1.571 3.78\"/><path d=\"M21.25 20.25V3.75m-18.5 16.5V3.75\"/></g>"}, "text-line-height": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path stroke-linejoin=\"round\" d=\"m2.75 18.345l1.992 2.037c.235.24.547.361.858.361m2.85-2.398l-1.992 2.037c-.235.24-.547.361-.858.361M2.75 5.61l2.002-2.002c.234-.234.54-.351.848-.351M8.45 5.61l-2-2.002a1.2 1.2 0 0 0-.849-.351m0 17.486V3.257\"/><path d=\"M11.55 4.25h9.7m-9.7 15.5h9.7\"/><path stroke-linejoin=\"round\" d=\"m13.12 15.594l1.171-2.752m0 0h4.219m-4.219 0l1.796-4.218a.335.335 0 0 1 .627 0l1.796 4.217m0 0l1.172 2.753\"/></g>"}, "text-line-spacing": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\"><path stroke-linejoin=\"round\" d=\"m2.75 18.345l1.992 2.037c.235.24.547.361.858.361m2.85-2.398l-1.992 2.037c-.235.24-.547.361-.858.361M2.75 5.61l2.002-2.002c.234-.234.54-.351.848-.351M8.45 5.61l-2-2.002a1.2 1.2 0 0 0-.849-.351m0 0v17.486\"/><path d=\"M11.55 4.25h9.7m-9.7 5.17h9.7m-9.7 5.17h9.7m-9.7 5.17h6.7\"/></g>"}, "text-number-list": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9.75 5.247h10M9.75 12h10m-10 6.753h10M3.694 4.123L5.494 3v4.493m-2.223 3.582A1.32 1.32 0 0 1 4.465 9.76a1.325 1.325 0 0 1 1.428 1.057a1.32 1.32 0 0 1-.363 1.192l-1.994 1.973v.265H5.86m-2.265 2.26h2.344l-2.103 1.927h1.037c.589 0 1.066.477 1.066 1.065s-.172 1.242-1.241 1.483c-.587.132-1.448-.482-1.448-.964\"/>"}, "text-position-bottom": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 2.75v13.379m-6.287-5.666l5.226 5.226c.293.293.677.44 1.061.44m6.287-5.666l-5.226 5.226c-.293.293-.677.44-1.061.44M2.75 21.25h18.5\"/>"}, "text-position-middle": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 2.75v6.086M9.081 6.33l2.212 2.212a1 1 0 0 0 .707.293m2.919-2.505l-2.212 2.212a1 1 0 0 1-.707.293m0 12.415v-6.086M9.081 17.67l2.212-2.212a1 1 0 0 1 .707-.293m2.919 2.505l-2.212-2.212a1 1 0 0 0-.707-.293M2.75 12h18.5\"/>"}, "text-position-top": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 21.25V7.871m-6.287 5.666l5.226-5.226c.293-.293.677-.44 1.061-.44m6.287 5.666l-5.226-5.226A1.5 1.5 0 0 0 12 7.87M2.75 2.75h18.5\"/>"}, "text-small": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m6.5 18.138l1.964-4.507m0 0h7.072m-7.072 0l3.01-6.904a.566.566 0 0 1 1.052 0l3.01 6.905m0 0l1.964 4.506\"/>"}, "text-strikethrough": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M17.059 8.113s-.71-3.363-5.861-3.363c-1.923 0-4.42 1.601-4.42 3.838c0 .493.087.92.259 1.292q.194.418.526.75m-.526 6.365S8.382 20 12.364 20c2.217 0 4.859-1.078 4.859-3.544c0-1.571-.918-2.63-2.408-3.331m-10.065 0h10.065m4.435 0h-4.435\"/>"}, "text-subscript": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m4.75 4.75l10.5 12.5m0-12.5l-10.5 12.5m12.677-1.088a1.912 1.912 0 1 1 3.263 1.352l-2.88 3.236h3.359\"/>"}, "text-superscript": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m4.75 19.25l10.5-12.5m0 12.5L4.75 6.75m12.677-1.588a1.912 1.912 0 1 1 3.263 1.352L17.81 9.75h3.359\"/>"}, "text-typography": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m2.25 18.25l1.875-4.404m0 0h6.75m-6.75 0L6.998 7.1a.536.536 0 0 1 1.004 0l2.873 6.747m0 0l1.875 4.404m9-4.948l-.07-.014c-2.88-.557-6.308-.472-6.308 2.462c0 .824.464 1.598 1.14 2.02c.67.417 1.515.429 2.305.429c1.086 0 2.277-.626 2.754-1.602q.137-.279.179-.633V14.84m0-1.537v-.587c0-.734-.085-1.704-.517-2.297c-.47-.646-1.303-1.375-2.672-1.375c-2.261 0-2.991 1.595-2.991 1.595m6.18 2.664v1.537m0 3.26v-3.26\"/>"}, "text-underline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M6.5 4.75v6.5a5.5 5.5 0 0 0 5.5 5.5v0a5.5 5.5 0 0 0 5.5-5.5v-6.5m-11 15h11\"/>"}, "thumbs-down": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"4.2\" height=\"13.296\" x=\"21.25\" y=\"16.799\" rx=\"1.5\" transform=\"rotate(180 21.25 16.8)\"/><path d=\"M17.05 14.475V6.503a3 3 0 0 0-3-3H7.177a2.5 2.5 0 0 0-2.412 1.843l-1.958 7.188a2.5 2.5 0 0 0 2.412 3.157h4.095V19.5a2 2 0 0 0 2 2h.036a2 2 0 0 0 1.67-.9z\"/></g>"}, "thumbs-up": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"4.2\" height=\"13.296\" x=\"3.25\" y=\"7.201\" rx=\"1.5\"/><path d=\"M7.45 9.526v7.97a3 3 0 0 0 3 3h6.873a2.5 2.5 0 0 0 2.412-1.842l1.958-7.188a2.5 2.5 0 0 0-2.412-3.157h-4.095V4.5a2 2 0 0 0-2-2h-.036a2 2 0 0 0-1.67.9z\"/></g>"}, "tiktok": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M20.2 10.1c0 .22-.18.401-.4.39a8 8 0 0 1-3.362-.93c-.281-.15-.638.045-.638.364V15.5a6 6 0 1 1-6.4-5.987a.38.38 0 0 1 .4.387v2.8c0 .22-.18.397-.398.433A2.4 2.4 0 1 0 12.2 15.5V2.9a.4.4 0 0 1 .4-.4h2.8a.43.43 0 0 1 .418.4a4.4 4.4 0 0 0 3.983 3.982c.22.02.4.197.4.418z\"/>"}, "timer": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" d=\"M9 2.75h6M12 9.5v4\"/><circle cx=\"12\" cy=\"13.5\" r=\"7.75\"/><path stroke-linecap=\"round\" d=\"m19.75 5.818l-2.236 2.236\"/></g>"}, "timer-off": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M9 2.75h6M12 9.5V12m7.75-6.182l-2.236 2.236M2.75 2.75l18.5 18.5m-3.071-3.071A7.75 7.75 0 0 0 7.32 7.32M5.361 9.5A7.75 7.75 0 0 0 16 20.14\"/>"}, "toolbox": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 10a3 3 0 0 1 3-3h12.5a3 3 0 0 1 3 3v7.5a3 3 0 0 1-3 3H5.75a3 3 0 0 1-3-3zM8 5.5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2V7H8zm.5 5.5v3m7-3v3M2.75 12.5h18.5\"/>"}, "toy-brick": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"13\" x=\"2.75\" y=\"7.25\" rx=\"3\"/><path d=\"M5.25 5.25a1.5 1.5 0 0 1 1.5-1.5h2a1.5 1.5 0 0 1 1.5 1.5v2h-5zm8.5 0a1.5 1.5 0 0 1 1.5-1.5h2a1.5 1.5 0 0 1 1.5 1.5v2h-5z\"/></g>"}, "treasure-chest": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M9.75 19.75v-12a3.5 3.5 0 0 0-3.5-3.5v0\"/><path d=\"M2.75 8.25a4 4 0 0 1 4-4h10.5a4 4 0 0 1 4 4v9.5a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2zm0 2h18.5M15.5 12V8.5\"/></g>"}, "triangle": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" d=\"M5.732 20.5c-2.29 0-3.723-2.498-2.581-4.5L9.419 5.006c1.144-2.008 4.018-2.008 5.163 0L20.849 16c1.142 2.002-.291 4.5-2.581 4.5z\"/>"}, "tune": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 12h10.5m0 0a2.5 2.5 0 0 0 5 0m-5 0a2.5 2.5 0 0 1 5 0m0 0h3M7.75 5.25h13.5m-13.5 0a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m-5 13.5h3m0 0a2.5 2.5 0 0 0 5 0m-5 0a2.5 2.5 0 0 1 5 0m0 0h10.5\"/>"}, "tv": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"13\" x=\"2.75\" y=\"4\" rx=\"3\"/><path d=\"M7 20h10\"/></g>"}, "typescript": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"16.5\" height=\"16.5\" x=\"3.75\" y=\"3.75\" rx=\"2\"/><path d=\"M17.25 11.25h-2a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-2m-4.75-6v6m-2-6h4\"/></g>"}, "ubuntu": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><circle cx=\"12.75\" cy=\"12\" r=\"4.75\"/><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.546 20.054a8.254 8.254 0 0 1-9.67-5.582m13.95 3.109A8.22 8.22 0 0 0 21 12a8.22 8.22 0 0 0-2.174-5.582m-4.28-2.473a8.254 8.254 0 0 0-9.67 5.582\"/><path d=\"M15.625 16.98a2.5 2.5 0 1 1 2.5 4.33a2.5 2.5 0 0 1-2.5-4.33ZM4.5 9.5a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5Zm11.125-6.81a2.5 2.5 0 1 1 2.5 4.33a2.5 2.5 0 0 1-2.5-4.33Z\"/></g>"}, "vehicle-car": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 11.75a2 2 0 0 1 2-2h12.5a2 2 0 0 1 2 2v6H3.75z\"/><circle cx=\"7\" cy=\"13\" r=\"1\" fill=\"currentColor\"/><circle cx=\"17\" cy=\"13\" r=\"1\" fill=\"currentColor\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10 14.75h4M6.271 5.567a2 2 0 0 1 1.88-1.317h7.698a2 2 0 0 1 1.88 1.317L19.25 9.75H4.75zM3.75 17.75h3.438v1.7a1.3 1.3 0 0 1-1.3 1.3H5.05a1.3 1.3 0 0 1-1.3-1.3zm13.063 0h3.437v1.7a1.3 1.3 0 0 1-1.3 1.3h-.837a1.3 1.3 0 0 1-1.3-1.3z\"/></g>"}, "video": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><rect width=\"13.5\" height=\"12\" x=\"2.75\" y=\"6\" rx=\"3.5\"/><path d=\"m16.25 9.74l3.554-1.77a1 1 0 0 1 1.446.895v6.268a1 1 0 0 1-1.447.895l-3.553-1.773z\"/></g>"}, "video-clip": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\"><rect width=\"18.5\" height=\"15.5\" x=\"2.75\" y=\"4.25\" stroke-linecap=\"round\" stroke-linejoin=\"round\" rx=\"3.5\"/><path d=\"M8.83 13.29c0 1.098 0 1.646.23 1.964c.201.277.51.456.85.492c.391.041.867-.232 1.818-.779l2.244-1.29c.956-.55 1.435-.825 1.595-1.185a1.2 1.2 0 0 0 0-.984c-.16-.36-.639-.635-1.595-1.184l-2.244-1.291c-.951-.547-1.427-.82-1.817-.779c-.34.036-.65.215-.85.492c-.231.318-.231.866-.231 1.963z\"/></g>"}, "visual-studio-code": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M15.695 16.318L10.333 12m5.362 4.318V7.682m0 8.636v2.18c0 .68-.27 1.332-.75 1.813M10.332 12l5.362-4.318M10.333 12L7.828 9.983m7.867-2.3v-2.18c0-.68-.27-1.333-.75-1.814M7.827 14.017l-3.516 2.831a.61.61 0 0 1-.813-.044l-1.071-1.076a.61.61 0 0 1 .022-.881L5.598 12m2.23 2.017l6.456 5.84q.304.273.66.454m-7.116-6.294L5.598 12m0 0L2.45 9.153a.61.61 0 0 1-.022-.881l1.071-1.076c.22-.22.57-.24.813-.044l3.516 2.83m0 0l6.456-5.839q.304-.273.66-.454m0 16.622a3.04 3.04 0 0 0 2.517.113l2.334-.937c.577-.232.955-.791.955-1.413V5.926c0-.622-.378-1.181-.955-1.413l-2.334-.937a3.04 3.04 0 0 0-2.517.113\"/>"}, "volume": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12.253 19.4L7.99 15.782a1 1 0 0 0-.647-.238H4.75a2 2 0 0 1-2-2v-3.086a2 2 0 0 1 2-2h2.594a1 1 0 0 0 .647-.238l4.262-3.62a1 1 0 0 1 1.647.762V18.64a1 1 0 0 1-1.647.762Z\"/><path stroke-linecap=\"round\" d=\"M16.664 8.542c.48.35.88.854 1.158 1.462c.277.607.424 1.295.424 1.996c0 .7-.147 1.39-.424 1.996c-.278.607-.677 1.112-1.158 1.462M18.7 6.424c.775.565 1.42 1.378 1.867 2.357c.447.978.683 2.089.683 3.219s-.236 2.24-.683 3.22c-.448.978-1.092 1.791-1.867 2.356\"/></g>"}, "volume-low": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12.253 19.4L7.99 15.782a1 1 0 0 0-.647-.238H4.75a2 2 0 0 1-2-2v-3.086a2 2 0 0 1 2-2h2.594a1 1 0 0 0 .647-.238l4.262-3.62a1 1 0 0 1 1.647.762V18.64a1 1 0 0 1-1.647.762Z\"/><path stroke-linecap=\"round\" d=\"M16.664 8.542c.48.35.88.854 1.158 1.462c.277.607.423 1.295.423 1.996c0 .7-.146 1.39-.423 1.996c-.278.607-.677 1.112-1.158 1.462\"/></g>"}, "volume-mute": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M12.253 19.4L7.99 15.782a1 1 0 0 0-.647-.238H4.75a2 2 0 0 1-2-2v-3.086a2 2 0 0 1 2-2h2.594a1 1 0 0 0 .647-.238l4.262-3.62a1 1 0 0 1 1.647.762V18.64a1 1 0 0 1-1.647.762Z\"/><path stroke-linecap=\"round\" d=\"m16.53 9.64l4.72 4.72m0-4.72l-4.72 4.72\"/></g>"}, "vuejs": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5.701 4.57h3.161c.384 0 .739.202.936.531L12 8.791l2.203-3.69a1.09 1.09 0 0 1 .935-.531h3.16m-12.597 0h-1.86a1.09 1.09 0 0 0-.935 1.648l8.158 13.671a1.09 1.09 0 0 0 1.872 0l8.158-13.671a1.09 1.09 0 0 0-.935-1.648h-1.86M5.7 4.57L12 15.124L18.299 4.57\"/>"}, "watch": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M18.314 12a6.3 6.3 0 0 0-2.623-5.123v-2.92c0-.805-.652-1.457-1.457-1.457H9.766c-.805 0-1.457.652-1.457 1.457v2.92A6.3 6.3 0 0 0 5.686 12m12.628 0a6.3 6.3 0 0 1-2.623 5.123v2.92c0 .805-.652 1.457-1.457 1.457H9.766a1.457 1.457 0 0 1-1.457-1.457v-2.92A6.3 6.3 0 0 1 5.686 12m12.628 0a6.314 6.314 0 1 1-12.628 0m12.628 0a6.314 6.314 0 1 0-12.628 0\"/>"}, "weather-cloudy": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.465 13.796a3.43 3.43 0 0 1-.715-2.101a3.457 3.457 0 0 1 3.114-3.437a.31.31 0 0 0 .276-.305A3.46 3.46 0 0 1 9.603 4.5c.88 0 1.682.327 2.293.866m4.665 6.083a3.46 3.46 0 0 0-3.493-3.453a3.46 3.46 0 0 0-3.432 3.453c0 .157-.12.29-.276.305a3.46 3.46 0 0 0-3.114 3.437a3.46 3.46 0 0 0 3.462 3.453h8.08a3.46 3.46 0 0 0 3.462-3.453a3.46 3.46 0 0 0-3.462-3.454h-.95a.283.283 0 0 1-.277-.288\"/>"}, "webpack": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12 12L7.435 9.364M12 12l4.565-2.636M12 12v5.272m-8.01-.647c.178.31.437.573.757.757l6.218 3.59c.32.186.678.278 1.035.278m-8.01-4.625a2.07 2.07 0 0 1-.278-1.035V8.41c0-.37.099-.726.277-1.035m0 9.25l3.446-1.99M12 21.25a2.07 2.07 0 0 0 1.035-.277l6.218-3.59c.32-.185.58-.449.758-.758M12 21.25v-3.978m8.01-.647l-3.306-1.91l-.136-.083m3.443 1.993c.178-.31.277-.665.277-1.035V8.41c0-.37-.099-.726-.277-1.035m0 0a2.07 2.07 0 0 0-.758-.757l-6.218-3.59A2.07 2.07 0 0 0 12 2.75m8.01 4.625l-3.445 1.99M12 2.75a2.07 2.07 0 0 0-1.035.277l-6.218 3.59c-.32.185-.58.449-.758.758M12 2.75v3.978m-8.01.647l3.445 1.99m0 0c-.09.154-.139.332-.139.516v4.237c0 .185.05.363.139.518m0-5.272c.089-.155.218-.286.378-.379l3.67-2.118c.16-.092.338-.139.517-.139m4.565 2.636a1.03 1.03 0 0 0-.378-.379l-3.67-2.118A1.03 1.03 0 0 0 12 6.728m4.565 2.636c.09.155.139.333.139.517v4.237c0 .184-.049.36-.136.514m0 0c-.09.156-.22.29-.381.383l-3.67 2.118c-.16.092-.338.139-.517.139m-4.565-2.636c.089.155.218.286.378.379l3.67 2.118c.16.092.338.139.517.139\"/>"}, "wi-fi": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M2.75 8.988A12.068 12.068 0 0 1 21.25 9\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M5.64 11.964a8.297 8.297 0 0 1 12.72.01m-9.805 3.029a4.495 4.495 0 0 1 6.89.005\"/><circle cx=\"12\" cy=\"17.878\" r=\"1.445\" fill=\"currentColor\"/></g>"}, "window": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 7.75a4 4 0 0 1 4-4h8.5a4 4 0 0 1 4 4v8.5a4 4 0 0 1-4 4h-8.5a4 4 0 0 1-4-4zm0 .5h16.5\"/>"}, "window-add": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M3.75 8.25h16.5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M20.25 10.736V7.75a4 4 0 0 0-4-4h-8.5a4 4 0 0 0-4 4v8.5a4 4 0 0 0 4 4h2.986\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.5 23a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-8.993a.5.5 0 0 1 .5.5V17h2.493a.5.5 0 1 1 0 1H18v2.493a.5.5 0 1 1-1 0V18h-2.493a.5.5 0 1 1 0-1H17v-2.493a.5.5 0 0 1 .5-.5\" clip-rule=\"evenodd\"/></g>"}, "window-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 6.25a3.5 3.5 0 0 1 3.5-3.5h8.5a3.5 3.5 0 0 1 3.5 3.5v8.5a3.5 3.5 0 0 1-3.5 3.5h-8.5a3.5 3.5 0 0 1-3.5-3.5z\"/><path d=\"M21.25 6.75v8a6.5 6.5 0 0 1-6.5 6.5h-8m-4-14.273h15.5\"/></g>"}, "window-multiple-var": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M2.75 6.75a4 4 0 0 1 4-4h7.5a4 4 0 0 1 4 4v7.5a4 4 0 0 1-4 4h-7.5a4 4 0 0 1-4-4z\"/><path d=\"M21.25 6.75v7.5a7 7 0 0 1-7 7h-7.5m-4-14.273h15.5\"/></g>", "hidden": true}, "wrench": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M10.691 4.562a6.19 6.19 0 0 1 6.545-1.42c.378.*************.906l-2.787 2.787a1.037 1.037 0 0 0 0 1.467l1.084 1.084a1.037 1.037 0 0 0 1.467 0L19.953 6.6c.285-.285.764-.212.905.165a6.187 6.187 0 0 1-7.696 8.058c-.396-.128-.84-.054-1.134.24L6.481 20.61a2.186 2.186 0 1 1-3.09-3.09l5.547-5.548c.294-.294.368-.738.24-1.134a6.19 6.19 0 0 1 1.513-6.276Z\"/>"}, "x-twitter": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"m13.081 10.712l-4.786-6.71a.6.6 0 0 0-.489-.252H5.28a.6.6 0 0 0-.488.948l6.127 8.59m2.162-2.576l6.127 8.59a.6.6 0 0 1-.488.948h-2.526a.6.6 0 0 1-.489-.252l-4.786-6.71m2.162-2.576l5.842-6.962m-8.004 9.538L5.077 20.25\"/>"}, "youtube": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path stroke-linecap=\"round\" d=\"M2.45 11.419c0-3.017.3-4.526 1.237-5.463s2.446-.937 5.463-.937h5.7c3.017 0 4.525 0 5.463.937s1.237 2.446 1.237 5.463v1.162c0 3.017-.3 4.526-1.237 5.463s-2.446.937-5.463.937h-5.7c-3.017 0-4.526 0-5.463-.937S2.45 15.598 2.45 12.581z\"/><path d=\"m14.686 11.491l-4.268-2.667a.6.6 0 0 0-.918.509v5.335a.6.6 0 0 0 .918.508l4.268-2.667a.6.6 0 0 0 0-1.018Z\"/></g>"}, "youtube-shorts": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M12.834 3.186a3.627 3.627 0 0 1 3.627 6.282l-.74.426a3.626 3.626 0 0 1 1.935 6.766l-7.02 4.053a3.626 3.626 0 1 1-3.627-6.28l.739-.428A3.627 3.627 0 0 1 5.814 7.24z\"/><path fill=\"currentColor\" d=\"M13.992 11.016L11.2 9.271c-.74-.463-1.7.07-1.7.942v3.49c0 .873.96 1.405 1.7.943l2.792-1.746a1.11 1.11 0 0 0 0-1.884\"/></g>"}, "zoom-in": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M7.56 10.56h6m-3 3v-6m4.993 7.993a7.06 7.06 0 1 0-9.985-9.985a7.06 7.06 0 0 0 9.985 9.985m0 0L20 20\"/><path d=\"M15.553 5.568a7.06 7.06 0 1 1-9.985 9.985a7.06 7.06 0 0 1 9.985-9.985\"/></g>"}, "zoom-out": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M7.56 10.56h6m1.993 4.993a7.06 7.06 0 1 0-9.985-9.985a7.06 7.06 0 0 0 9.985 9.985m0 0L20 20\"/><path d=\"M15.553 5.568a7.06 7.06 0 1 1-9.985 9.985a7.06 7.06 0 0 1 9.985-9.985\"/></g>"}}, "aliases": {"add-circular": {"parent": "add-circle"}, "add-square-multiple-var": {"parent": "add-square-multiple"}, "apps-list": {"parent": "checkbox-list"}, "arrow-foward": {"parent": "arrow-forward"}, "arrow-up-down-1": {"parent": "arrow-left-right"}, "attatch": {"parent": "attach"}, "box-drag": {"parent": "square-drag"}, "box-margins": {"parent": "square-margins"}, "checkbox-intermediate-2": {"parent": "checkbox-indeterminate-2"}, "checkmark-checked": {"parent": "checkbox-checked"}, "checkmark-intermediate": {"parent": "checkbox-indeterminate"}, "comment-multiple-var": {"parent": "comment-multiple"}, "fast-foward": {"parent": "fast-forward"}, "museum": {"parent": "bank"}, "panel-left-open-1": {"parent": "panel-right-open"}, "play-circular": {"parent": "play-circle"}, "save-as": {"parent": "save-pencil"}, "slash-box": {"parent": "slash-square"}, "text-bullet-list": {"parent": "bullet-list"}, "text-bullet-list-square": {"parent": "bullet-list-square"}, "text-bullet-list-square-add": {"parent": "bullet-list-square-add"}, "text-bullet-list-tree": {"parent": "bullet-list-tree"}, "volume-0": {"parent": "volume"}, "volume-1": {"parent": "volume-low"}, "volume-medium": {"parent": "volume"}}, "width": 24, "height": 24}