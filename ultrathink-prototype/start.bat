@echo off
chcp 65001 >nul
title Ultrathink 股票量化分析系统 - 前端原型

echo.
echo 🚀 启动 Ultrathink 股票量化分析系统原型...
echo.

REM 检查是否存在构建文件
if exist "dist" (
    echo 📦 发现构建文件，使用静态服务器启动...
    echo.
    
    REM 优先使用Python
    python --version >nul 2>&1
    if not errorlevel 1 (
        echo 🐍 使用 Python 启动服务器...
        python 启动原型.py
        goto :end
    )
    
    REM 备选Node.js
    node --version >nul 2>&1
    if not errorlevel 1 (
        echo ⚡️ 使用 Node.js 启动服务器...
        node 启动原型.js
        goto :end
    )
    
    echo ❌ 错误: 需要安装 Python 或 Node.js 来启动服务器
    echo.
    echo 安装方法:
    echo   Windows: 访问 https://python.org 或 https://nodejs.org
    echo.
    echo 或者使用开发模式: npm run dev
    pause
    exit /b 1
    
) else (
    echo 📦 未找到构建文件，使用开发模式...
    echo.
    
    REM 检查 Node.js 环境
    node --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ 错误: 未检测到 Node.js，请先安装 Node.js ^(建议版本 ^>= 16^)
        pause
        exit /b 1
    )

    REM 检查 npm 环境
    npm --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ 错误: 未检测到 npm，请确保 Node.js 安装完整
        pause
        exit /b 1
    )

    echo ✅ Node.js 版本:
    node --version
    echo ✅ npm 版本:
    npm --version
    echo.

    REM 检查依赖是否已安装
    if not exist "node_modules" (
        echo 📦 正在安装项目依赖...
        npm install
        
        if errorlevel 1 (
            echo ❌ 依赖安装失败，请检查网络连接或使用国内镜像
            echo    可尝试: npm config set registry https://registry.npmmirror.com/
            pause
            exit /b 1
        )
        
        echo ✅ 依赖安装完成
        echo.
    )

    echo 🎯 演示账户信息:
    echo    管理员: admin / admin123
    echo    普通用户: user / user123
    echo    演示用户: demo / demo123
    echo.

    echo 🌐 启动开发服务器...
    echo    本地访问: http://localhost:5173
    echo.
    echo 💡 使用 Ctrl+C 停止服务器
    echo.

    REM 启动开发服务器
    npm run prototype
)

:end
pause