#!/usr/bin/env node

/**
 * Ultrathink 股票量化分析原型 - Node.js 启动服务器
 * 使用Node.js内置模块，无需额外依赖
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.mjs': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject'
};

function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'application/octet-stream';
}

function findFreePort(startPort = 8080) {
  return new Promise((resolve) => {
    const net = require('net');
    
    function tryPort(port) {
      const server = net.createServer();
      
      server.listen(port, (err) => {
        if (err) {
          server.close();
          tryPort(port + 1);
        } else {
          server.close();
          resolve(port);
        }
      });
      
      server.on('error', () => {
        tryPort(port + 1);
      });
    }
    
    tryPort(startPort);
  });
}

function openBrowser(url) {
  const commands = {
    darwin: 'open',
    win32: 'start',
    linux: 'xdg-open'
  };
  
  const command = commands[process.platform];
  if (command) {
    exec(`${command} ${url}`, (error) => {
      if (error) {
        console.log(`   无法自动打开浏览器: ${error.message}`);
        console.log(`   请手动访问: ${url}`);
      }
    });
  } else {
    console.log(`   请手动访问: ${url}`);
  }
}

function createServer(distDir, port) {
  const server = http.createServer((req, res) => {
    // 处理CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', '*');
    
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }
    
    let filePath = req.url;
    
    // 处理根路径
    if (filePath === '/') {
      filePath = '/index.html';
    }
    
    // 移除查询参数
    const urlParts = filePath.split('?');
    filePath = urlParts[0];
    
    const fullPath = path.join(distDir, filePath);
    
    // 安全检查 - 防止目录遍历攻击
    if (!fullPath.startsWith(distDir)) {
      res.writeHead(403);
      res.end('Forbidden');
      return;
    }
    
    fs.readFile(fullPath, (err, data) => {
      if (err) {
        if (err.code === 'ENOENT') {
          // 文件不存在，尝试返回index.html（用于SPA路由）
          const indexPath = path.join(distDir, 'index.html');
          fs.readFile(indexPath, (indexErr, indexData) => {
            if (indexErr) {
              res.writeHead(404);
              res.end('File Not Found');
            } else {
              res.setHeader('Content-Type', 'text/html');
              res.writeHead(200);
              res.end(indexData);
            }
          });
        } else {
          res.writeHead(500);
          res.end('Server Error');
        }
      } else {
        const mimeType = getMimeType(fullPath);
        res.setHeader('Content-Type', mimeType);
        res.writeHead(200);
        res.end(data);
      }
    });
  });
  
  return server;
}

async function startServer() {
  // 获取脚本所在目录
  const scriptDir = path.dirname(__filename);
  const distDir = path.join(scriptDir, 'dist');
  
  // 检查dist目录是否存在
  if (!fs.existsSync(distDir)) {
    console.log('❌ 错误: 未找到 dist 目录');
    console.log('   请先运行 \'npm run build\' 构建项目');
    process.exit(1);
  }
  
  try {
    const port = await findFreePort();
    const server = createServer(distDir, port);
    
    console.log(`
🚀 Ultrathink 股票量化分析原型启动中...

📂 服务目录: ${distDir}
🌐 服务地址: http://localhost:${port}
🎯 演示账户:
   管理员: admin / admin123
   普通用户: user / user123  
   演示用户: demo / demo123

💡 使用 Ctrl+C 停止服务器
`);
    
    server.listen(port, 'localhost', () => {
      const url = `http://localhost:${port}`;
      console.log(`🌟 正在打开浏览器: ${url}`);
      
      // 延迟一秒后打开浏览器
      setTimeout(() => {
        openBrowser(url);
      }, 1000);
      
      console.log('\n✅ 服务器运行中... (按 Ctrl+C 停止)');
    });
    
    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n\n🛑 正在停止服务器...');
      server.close(() => {
        console.log('✅ 服务器已停止');
        process.exit(0);
      });
    });
    
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      console.error('❌ 服务器错误:', error.message);
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ 启动服务器失败:', error.message);
    process.exit(1);
  }
}

// 显示欢迎信息
console.log(`
╔══════════════════════════════════════════════╗
║   🚀 Ultrathink 股票量化分析系统原型         ║
║                                              ║
║   高保真交互式前端原型                        ║
║   让投资更智能，让决策更精准                   ║
╚══════════════════════════════════════════════╝
`);

// 检查Node.js版本
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 12) {
  console.log('❌ 错误: 需要 Node.js 12 或更高版本');
  console.log(`   当前版本: ${nodeVersion}`);
  process.exit(1);
}

// 启动服务器
startServer();