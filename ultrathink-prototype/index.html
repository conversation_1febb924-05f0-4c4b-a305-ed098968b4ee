<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Ultrathink 股票量化分析原型</title>
    <link rel="icon" type="image/svg+xml" href="./public/vite.svg" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 700px;
            margin: 20px;
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .launch-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ff8e53);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px 0 rgba(31, 38, 135, 0.4);
            cursor: pointer;
            border: none;
        }

        .launch-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba(31, 38, 135, 0.6);
        }

        .launch-button.primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            font-size: 1.3em;
            padding: 20px 40px;
        }

        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .warning-box {
            background: rgba(255, 193, 7, 0.2);
            border-left: 4px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .account-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .account-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: left;
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-error { background-color: #ff4757; }
        .status-success { background-color: #2ed573; }
        .status-warning { background-color: #ffa502; }

        .hidden { display: none; }

        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Ultrathink 股票量化分析原型</h1>
        <div class="subtitle">高保真交互式前端原型系统</div>

        <!-- 状态检测区域 -->
        <div id="status-area">
            <div class="warning-box">
                <h3><span class="status-indicator status-warning"></span>检测到文件协议访问</h3>
                <p>您正在通过 <code>file://</code> 协议访问此页面。由于现代浏览器的安全限制，ES模块无法直接加载。</p>
                <p><strong>请选择以下方式之一启动原型：</strong></p>
            </div>
        </div>

        <!-- 启动选项 -->
        <div id="launch-options">
            <div style="margin: 30px 0;">
                <button class="launch-button primary" onclick="startWithPython()">
                    🐍 一键启动 (Python)
                </button>
                <br>
                <button class="launch-button" onclick="startWithNode()">
                    ⚡️ 使用 Node.js 启动
                </button>
                <button class="launch-button" onclick="showManualInstructions()">
                    📖 查看手动启动说明
                </button>
            </div>
        </div>

        <!-- 演示账户信息 -->
        <div class="info-box">
            <h3>🎯 演示账户信息</h3>
            <div class="account-info">
                <div class="account-card">
                    <strong>管理员</strong><br>
                    用户名: <code>admin</code><br>
                    密码: <code>admin123</code>
                </div>
                <div class="account-card">
                    <strong>普通用户</strong><br>
                    用户名: <code>user</code><br>
                    密码: <code>user123</code>
                </div>
                <div class="account-card">
                    <strong>演示用户</strong><br>
                    用户名: <code>demo</code><br>
                    密码: <code>demo123</code>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="spinner"></div>正在启动原型系统...
        </div>

        <!-- 手动说明（默认隐藏） -->
        <div class="info-box hidden" id="manual-instructions">
            <h3>📋 手动启动说明</h3>

            <h4>方式一：Python 启动（推荐）</h4>
            <p>在项目目录中运行：</p>
            <code>python3 启动原型.py</code>
            <p>或双击 <code>启动原型.py</code> 文件</p>

            <h4>方式二：Node.js 启动</h4>
            <p>在项目目录中运行：</p>
            <code>node 启动原型.js</code>
            <p>或双击启动脚本：</p>
            <ul style="text-align: left;">
                <li>Windows: <code>start.bat</code></li>
                <li>Mac/Linux: <code>start.sh</code></li>
            </ul>

            <h4>方式三：开发模式</h4>
            <p>需要 Node.js 环境：</p>
            <ol style="text-align: left;">
                <li><code>npm install</code> (首次运行)</li>
                <li><code>npm run dev</code></li>
                <li>访问 <code>http://localhost:5173</code></li>
            </ol>
        </div>

        <!-- 功能特性 -->
        <div class="info-box">
            <h3>✨ 核心功能特性</h3>
            <ul style="text-align: left; line-height: 1.8;">
                <li>📈 股票分析和K线图表展示</li>
                <li>📊 技术指标计算和可视化</li>
                <li>🔍 多策略股票扫描器</li>
                <li>⭐ 自选股管理功能</li>
                <li>⏰ 定时任务管理</li>
                <li>👥 用户权限管理</li>
                <li>📱 响应式设计支持</li>
                <li>🌙 深色主题模式</li>
            </ul>
        </div>

        <div style="margin-top: 30px; opacity: 0.8; font-size: 0.9em;">
            <p>💡 让投资更智能，让决策更精准</p>
            <p>版本: v1.0.0 | 纯前端原型版 | 构建时间: 2025-08-15</p>
        </div>
    </div>

    <!-- 应用容器（用于正常模式） -->
    <div id="app" class="hidden"></div>

    <script>
        // 检测访问协议
        function detectProtocol() {
            return window.location.protocol === 'file:';
        }

        // 启动 Python 服务器
        function startWithPython() {
            showLoading();

            // 创建启动说明
            const instructions = `
🐍 Python 启动步骤：

1. 打开命令行/终端
2. 切换到项目目录：
   cd "${window.location.pathname.replace('/index.html', '').replace('file://', '')}"
3. 运行启动脚本：
   python3 启动原型.py

或者直接双击项目目录中的 "启动原型.py" 文件

需要 Python 3.6+ 版本：
- Windows: https://python.org
- Mac: brew install python3
- Ubuntu: sudo apt install python3

启动后会自动打开浏览器访问原型系统。
            `;

            setTimeout(() => {
                hideLoading();
                alert(instructions);
            }, 1000);
        }

        // 启动 Node.js 服务器
        function startWithNode() {
            showLoading();

            const instructions = `
⚡️ Node.js 启动步骤：

1. 打开命令行/终端
2. 切换到项目目录：
   cd "${window.location.pathname.replace('/index.html', '').replace('file://', '')}"
3. 运行启动脚本：
   node 启动原型.js

或者双击启动脚本：
- Windows: start.bat
- Mac/Linux: start.sh

需要 Node.js 12+ 版本：
- 下载地址: https://nodejs.org

启动后会自动打开浏览器访问原型系统。
            `;

            setTimeout(() => {
                hideLoading();
                alert(instructions);
            }, 1000);
        }

        // 显示手动说明
        function showManualInstructions() {
            const manual = document.getElementById('manual-instructions');
            manual.classList.toggle('hidden');
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('🚀 Ultrathink 股票量化分析原型');
            console.log('📊 演示账户: admin/admin123, user/user123, demo/demo123');

            // 如果是通过 HTTP 协议访问，加载正常的应用
            if (!detectProtocol()) {
                // 隐藏启动页面，显示应用
                document.querySelector('.container').style.display = 'none';
                document.getElementById('app').classList.remove('hidden');

                // 动态加载应用脚本
                const script = document.createElement('script');
                script.type = 'module';
                script.src = '/src/main.js';
                document.body.appendChild(script);

                // 更新标题
                document.title = 'Ultrathink 股票量化分析系统';
            } else {
                console.log('💡 提示: 检测到文件协议访问，请使用启动脚本启动本地服务器');
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        startWithPython();
                        break;
                    case '2':
                        e.preventDefault();
                        startWithNode();
                        break;
                    case 'h':
                        e.preventDefault();
                        showManualInstructions();
                        break;
                }
            }
        });
    </script>
</body>
</html>
