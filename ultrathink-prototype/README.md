# Ultrathink 股票量化分析系统 - 前端原型

这是一个高保真、带交互的纯前端原型，展示股票量化分析系统的完整用户界面和交互流程。

## 🚀 项目特点

- ✅ **纯前端原型**：无需后端服务，使用模拟数据提供完整体验
- ✅ **高保真界面**：1:1 还原真实系统的设计和交互
- ✅ **完整功能演示**：股票分析、技术指标、扫描器、自选股等功能
- ✅ **响应式设计**：支持桌面和移动端访问
- ✅ **暗黑模式**：内置深色主题支持
- ✅ **模拟数据**：丰富的股票数据和图表展示

## 📦 技术栈

- **前端框架**：Vue 3 + Composition API
- **构建工具**：Vite
- **UI 组件库**：Element Plus
- **样式框架**：UnoCSS + SCSS
- **状态管理**：Pinia
- **图表库**：ECharts
- **图标库**：Iconify (Carbon 系列)
- **路由**：Vue Router

## 🎯 功能模块

### 核心功能
- **股票分析页面**：K线图、技术指标、实时数据展示
- **自选股管理**：添加、删除、监控自选股票
- **技术指标扫描**：多种策略扫描，信号检测
- **定时任务管理**：计划任务设置和历史记录
- **数据管理**：股票数据同步和管理

### 用户体验
- **用户认证**：登录/退出（支持多个演示账户）
- **权限管理**：普通用户和管理员角色
- **主题切换**：深色/浅色模式
- **响应式布局**：适配不同屏幕尺寸

## 🚦 快速开始

### ⭐️ 方式一：直接打开（推荐）
无需安装任何依赖，直接双击启动：

1. **双击 `打开原型.html`** - 美观的启动页面
2. **或直接打开 `dist/index.html`** - 直接进入系统

### 方式二：开发模式
如需修改代码或开发调试：

```bash
# 1. 安装依赖（首次运行）
npm install

# 2. 开发模式运行
npm run dev
# 或原型演示模式
npm run prototype

# 3. 构建生产版本
npm run build

# 4. 预览构建结果
npm run preview
```

## 👤 演示账户

系统提供以下演示账户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 拥有所有功能权限 |
| user | user123 | 普通用户 | 标准用户权限 |
| demo | demo123 | 演示用户 | 演示账户 |

## 🎨 界面预览

- **股票分析页面**：实时K线图、技术指标分析
- **自选股列表**：个人股票监控面板
- **扫描器界面**：技术指标筛选和策略扫描
- **任务管理**：定时任务设置和执行历史
- **用户管理**：账户设置和权限管理

## 📊 模拟数据说明

### 股票数据
- 包含主流股票的基本信息（平安银行、万科A、贵州茅台等）
- 模拟60天的K线历史数据
- 实时价格和交易数据
- 技术指标计算结果（MA、MACD、KDJ、RSI等）

### 扫描结果
- 强势突破策略
- 量价齐升检测
- 均线金叉信号
- RSI超卖反弹
- MACD多头信号

### 用户数据
- 模拟用户账户和权限
- 个人自选股列表
- 任务执行历史

## 🔧 配置说明

### 环境变量
- `VITE_PROTOTYPE_MODE=true` - 启用原型模式
- `VITE_SHOW_DEBUG_INFO=true` - 显示调试信息
- `VITE_DEMO_MODE=true` - 演示模式标识

### 原型模式特性
- 自动使用 Mock API 替代真实后端调用
- 数据持久化到 localStorage（模拟用户状态）
- 网络请求延迟模拟（100-400ms）
- 完整的错误处理和用户反馈

## 🎯 使用场景

### 产品演示
- 向客户展示系统完整功能
- 产品原型验证和用户反馈收集
- 销售演示和市场推广

### 开发参考
- 前端开发规范和组件设计参考
- UI/UX 设计标准
- 交互流程设计模板

### 培训教学
- 系统操作流程培训
- 功能特性介绍
- 用户体验测试

## 🔄 与真实系统的区别

| 方面 | 原型版本 | 真实系统 |
|------|----------|----------|
| 数据来源 | 模拟数据 | 真实市场数据 |
| API调用 | Mock API | 后端RESTful API |
| 数据持久化 | localStorage | 数据库 |
| 用户认证 | 本地模拟 | JWT + 后端验证 |
| 实时更新 | 模拟刷新 | WebSocket推送 |

## 📝 注意事项

1. **数据仅供演示**：所有股票数据和分析结果均为模拟生成
2. **功能限制**：某些高级功能可能简化或模拟实现
3. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
4. **移动端适配**：支持响应式设计，但建议在桌面端获得最佳体验

## 🤝 反馈与建议

如果您在使用过程中有任何问题或建议，欢迎通过以下方式联系：

- 项目Issues
- 邮件反馈
- 产品讨论

---

**Ultrathink 股票量化分析系统** - 让投资更智能，让决策更精准。